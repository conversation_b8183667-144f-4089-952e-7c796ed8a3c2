{"version": 3, "sources": ["../src/index.ts", "../src/cloud.ts", "../src/events.ts", "../src/message.ts", "../src/tool.ts", "../src/task.ts", "../src/global-settings.ts", "../src/provider-settings.ts", "../src/model.ts", "../src/codebase-index.ts", "../src/providers/anthropic.ts", "../src/providers/bedrock.ts", "../src/providers/cerebras.ts", "../src/providers/chutes.ts", "../src/providers/claude-code.ts", "../src/providers/deepseek.ts", "../src/providers/doubao.ts", "../src/providers/featherless.ts", "../src/providers/fireworks.ts", "../src/providers/gemini.ts", "../src/providers/gemini-cli.ts", "../src/providers/glama.ts", "../src/providers/groq.ts", "../src/providers/huggingface.ts", "../src/providers/io-intelligence.ts", "../src/providers/lite-llm.ts", "../src/providers/lm-studio.ts", "../src/providers/mistral.ts", "../src/providers/moonshot.ts", "../src/providers/ollama.ts", "../src/providers/openai.ts", "../src/providers/openrouter.ts", "../src/providers/qwen-code.ts", "../src/providers/requesty.ts", "../src/providers/roo.ts", "../src/providers/sambanova.ts", "../src/providers/unbound.ts", "../src/providers/vertex.ts", "../src/providers/vscode-llm.ts", "../src/providers/xai.ts", "../src/providers/vercel-ai-gateway.ts", "../src/providers/zai.ts", "../src/providers/deepinfra.ts", "../src/history.ts", "../src/experiment.ts", "../src/telemetry.ts", "../src/mode.ts", "../src/vscode.ts", "../src/marketplace.ts", "../src/followup.ts", "../src/ipc.ts", "../src/mcp.ts", "../src/single-file-read-models.ts", "../src/todo.ts", "../src/terminal.ts"], "sourcesContent": ["export * from \"./api.js\"\r\nexport * from \"./cloud.js\"\r\nexport * from \"./codebase-index.js\"\r\nexport * from \"./events.js\"\r\nexport * from \"./experiment.js\"\r\nexport * from \"./followup.js\"\r\nexport * from \"./global-settings.js\"\r\nexport * from \"./history.js\"\r\nexport * from \"./ipc.js\"\r\nexport * from \"./marketplace.js\"\r\nexport * from \"./mcp.js\"\r\nexport * from \"./message.js\"\r\nexport * from \"./mode.js\"\r\nexport * from \"./model.js\"\r\nexport * from \"./provider-settings.js\"\r\nexport * from \"./single-file-read-models.js\"\r\nexport * from \"./task.js\"\r\nexport * from \"./todo.js\"\r\nexport * from \"./telemetry.js\"\r\nexport * from \"./terminal.js\"\r\nexport * from \"./tool.js\"\r\nexport * from \"./type-fu.js\"\r\nexport * from \"./vscode.js\"\r\n\r\nexport * from \"./providers/index.js\"\r\n", "import EventEmitter from \"events\"\r\n\r\nimport { z } from \"zod\"\r\n\r\nimport { RooCodeEventName } from \"./events.js\"\r\nimport { TaskStatus, taskMetadataSchema } from \"./task.js\"\r\nimport { globalSettingsSchema } from \"./global-settings.js\"\r\nimport { providerSettingsWithIdSchema } from \"./provider-settings.js\"\r\nimport { mcpMarketplaceItemSchema } from \"./marketplace.js\"\r\nimport { clineMessageSchema, queuedMessageSchema, tokenUsageSchema } from \"./message.js\"\r\nimport { staticAppPropertiesSchema, gitPropertiesSchema } from \"./telemetry.js\"\r\n\r\n/**\r\n * JWTPayload\r\n */\r\n\r\nexport interface JWTPayload {\r\n\tiss?: string // Issuer (should be 'rcc')\r\n\tsub?: string // Subject - CloudJob ID for job tokens (t:'cj'), User ID for auth tokens (t:'auth')\r\n\texp?: number // Expiration time\r\n\tiat?: number // Issued at time\r\n\tnbf?: number // Not before time\r\n\tv?: number // Version (should be 1)\r\n\tr?: {\r\n\t\tu?: string // User ID (always present in valid tokens)\r\n\t\to?: string // Organization ID (optional - undefined when orgId is null)\r\n\t\tt?: string // Token type: 'cj' for job tokens, 'auth' for auth tokens\r\n\t}\r\n}\r\n\r\n/**\r\n * CloudUserInfo\r\n */\r\n\r\nexport interface CloudUserInfo {\r\n\tid?: string\r\n\tname?: string\r\n\temail?: string\r\n\tpicture?: string\r\n\torganizationId?: string\r\n\torganizationName?: string\r\n\torganizationRole?: string\r\n\torganizationImageUrl?: string\r\n\textensionBridgeEnabled?: boolean\r\n}\r\n\r\n/**\r\n * CloudOrganization\r\n */\r\n\r\nexport interface CloudOrganization {\r\n\tid: string\r\n\tname: string\r\n\tslug?: string\r\n\timage_url?: string\r\n\thas_image?: boolean\r\n\tcreated_at?: number\r\n\tupdated_at?: number\r\n}\r\n\r\n/**\r\n * CloudOrganizationMembership\r\n */\r\n\r\nexport interface CloudOrganizationMembership {\r\n\tid: string\r\n\torganization: CloudOrganization\r\n\trole: string\r\n\tpermissions?: string[]\r\n\tcreated_at?: number\r\n\tupdated_at?: number\r\n}\r\n\r\n/**\r\n * OrganizationAllowList\r\n */\r\n\r\nexport const organizationAllowListSchema = z.object({\r\n\tallowAll: z.boolean(),\r\n\tproviders: z.record(\r\n\t\tz.object({\r\n\t\t\tallowAll: z.boolean(),\r\n\t\t\tmodels: z.array(z.string()).optional(),\r\n\t\t}),\r\n\t),\r\n})\r\n\r\nexport type OrganizationAllowList = z.infer<typeof organizationAllowListSchema>\r\n\r\n/**\r\n * OrganizationDefaultSettings\r\n */\r\n\r\nexport const organizationDefaultSettingsSchema = globalSettingsSchema\r\n\t.pick({\r\n\t\tenableCheckpoints: true,\r\n\t\tfuzzyMatchThreshold: true,\r\n\t\tmaxOpenTabsContext: true,\r\n\t\tmaxReadFileLine: true,\r\n\t\tmaxWorkspaceFiles: true,\r\n\t\tshowRooIgnoredFiles: true,\r\n\t\tterminalCommandDelay: true,\r\n\t\tterminalCompressProgressBar: true,\r\n\t\tterminalOutputLineLimit: true,\r\n\t\tterminalShellIntegrationDisabled: true,\r\n\t\tterminalShellIntegrationTimeout: true,\r\n\t\tterminalZshClearEolMark: true,\r\n\t})\r\n\t// Add stronger validations for some fields.\r\n\t.merge(\r\n\t\tz.object({\r\n\t\t\tmaxOpenTabsContext: z.number().int().nonnegative().optional(),\r\n\t\t\tmaxReadFileLine: z.number().int().gte(-1).optional(),\r\n\t\t\tmaxWorkspaceFiles: z.number().int().nonnegative().optional(),\r\n\t\t\tterminalCommandDelay: z.number().int().nonnegative().optional(),\r\n\t\t\tterminalOutputLineLimit: z.number().int().nonnegative().optional(),\r\n\t\t\tterminalShellIntegrationTimeout: z.number().int().nonnegative().optional(),\r\n\t\t}),\r\n\t)\r\n\r\nexport type OrganizationDefaultSettings = z.infer<typeof organizationDefaultSettingsSchema>\r\n\r\n/**\r\n * OrganizationCloudSettings\r\n */\r\n\r\nexport const organizationCloudSettingsSchema = z.object({\r\n\trecordTaskMessages: z.boolean().optional(),\r\n\tenableTaskSharing: z.boolean().optional(),\r\n\ttaskShareExpirationDays: z.number().int().positive().optional(),\r\n\tallowMembersViewAllTasks: z.boolean().optional(),\r\n})\r\n\r\nexport type OrganizationCloudSettings = z.infer<typeof organizationCloudSettingsSchema>\r\n\r\n/**\r\n * OrganizationSettings\r\n */\r\n\r\nexport const organizationSettingsSchema = z.object({\r\n\tversion: z.number(),\r\n\tcloudSettings: organizationCloudSettingsSchema.optional(),\r\n\tdefaultSettings: organizationDefaultSettingsSchema,\r\n\tallowList: organizationAllowListSchema,\r\n\thiddenMcps: z.array(z.string()).optional(),\r\n\thideMarketplaceMcps: z.boolean().optional(),\r\n\tmcps: z.array(mcpMarketplaceItemSchema).optional(),\r\n\tproviderProfiles: z.record(z.string(), providerSettingsWithIdSchema).optional(),\r\n})\r\n\r\nexport type OrganizationSettings = z.infer<typeof organizationSettingsSchema>\r\n\r\n/**\r\n * User Settings Schemas\r\n */\r\n\r\nexport const userFeaturesSchema = z.object({\r\n\troomoteControlEnabled: z.boolean().optional(),\r\n})\r\n\r\nexport type UserFeatures = z.infer<typeof userFeaturesSchema>\r\n\r\nexport const userSettingsConfigSchema = z.object({\r\n\textensionBridgeEnabled: z.boolean().optional(),\r\n})\r\n\r\nexport type UserSettingsConfig = z.infer<typeof userSettingsConfigSchema>\r\n\r\nexport const userSettingsDataSchema = z.object({\r\n\tfeatures: userFeaturesSchema,\r\n\tsettings: userSettingsConfigSchema,\r\n\tversion: z.number(),\r\n})\r\n\r\nexport type UserSettingsData = z.infer<typeof userSettingsDataSchema>\r\n\r\n/**\r\n * Constants\r\n */\r\n\r\nexport const ORGANIZATION_ALLOW_ALL: OrganizationAllowList = {\r\n\tallowAll: true,\r\n\tproviders: {},\r\n} as const\r\n\r\nexport const ORGANIZATION_DEFAULT: OrganizationSettings = {\r\n\tversion: 0,\r\n\tcloudSettings: {\r\n\t\trecordTaskMessages: true,\r\n\t\tenableTaskSharing: true,\r\n\t\ttaskShareExpirationDays: 30,\r\n\t\tallowMembersViewAllTasks: true,\r\n\t},\r\n\tdefaultSettings: {},\r\n\tallowList: ORGANIZATION_ALLOW_ALL,\r\n} as const\r\n\r\n/**\r\n * ShareVisibility\r\n */\r\n\r\nexport type ShareVisibility = \"organization\" | \"public\"\r\n\r\n/**\r\n * ShareResponse\r\n */\r\n\r\nexport const shareResponseSchema = z.object({\r\n\tsuccess: z.boolean(),\r\n\tshareUrl: z.string().optional(),\r\n\terror: z.string().optional(),\r\n\tisNewShare: z.boolean().optional(),\r\n\tmanageUrl: z.string().optional(),\r\n})\r\n\r\nexport type ShareResponse = z.infer<typeof shareResponseSchema>\r\n\r\n/**\r\n * AuthService\r\n */\r\n\r\nexport type AuthState = \"initializing\" | \"logged-out\" | \"active-session\" | \"attempting-session\" | \"inactive-session\"\r\n\r\nexport interface AuthService extends EventEmitter<AuthServiceEvents> {\r\n\t// Lifecycle\r\n\tinitialize(): Promise<void>\r\n\tbroadcast(): void\r\n\r\n\t// Authentication methods\r\n\tlogin(): Promise<void>\r\n\tlogout(): Promise<void>\r\n\thandleCallback(code: string | null, state: string | null, organizationId?: string | null): Promise<void>\r\n\r\n\t// State methods\r\n\tgetState(): AuthState\r\n\tisAuthenticated(): boolean\r\n\thasActiveSession(): boolean\r\n\thasOrIsAcquiringActiveSession(): boolean\r\n\r\n\t// Token and user info\r\n\tgetSessionToken(): string | undefined\r\n\tgetUserInfo(): CloudUserInfo | null\r\n\tgetStoredOrganizationId(): string | null\r\n}\r\n\r\n/**\r\n * AuthServiceEvents\r\n */\r\n\r\nexport interface AuthServiceEvents {\r\n\t\"auth-state-changed\": [\r\n\t\tdata: {\r\n\t\t\tstate: AuthState\r\n\t\t\tpreviousState: AuthState\r\n\t\t},\r\n\t]\r\n\t\"user-info\": [data: { userInfo: CloudUserInfo }]\r\n}\r\n\r\n/**\r\n * SettingsService\r\n */\r\n\r\n/**\r\n * Interface for settings services that provide organization settings\r\n */\r\nexport interface SettingsService {\r\n\t/**\r\n\t * Get the organization allow list\r\n\t * @returns The organization allow list or default if none available\r\n\t */\r\n\tgetAllowList(): OrganizationAllowList\r\n\r\n\t/**\r\n\t * Get the current organization settings\r\n\t * @returns The organization settings or undefined if none available\r\n\t */\r\n\tgetSettings(): OrganizationSettings | undefined\r\n\r\n\t/**\r\n\t * Get the current user settings\r\n\t * @returns The user settings data or undefined if none available\r\n\t */\r\n\tgetUserSettings(): UserSettingsData | undefined\r\n\r\n\t/**\r\n\t * Get the current user features\r\n\t * @returns The user features or empty object if none available\r\n\t */\r\n\tgetUserFeatures(): UserFeatures\r\n\r\n\t/**\r\n\t * Get the current user settings configuration\r\n\t * @returns The user settings configuration or empty object if none available\r\n\t */\r\n\tgetUserSettingsConfig(): UserSettingsConfig\r\n\r\n\t/**\r\n\t * Update user settings with partial configuration\r\n\t * @param settings Partial user settings configuration to update\r\n\t * @returns Promise that resolves to true if successful, false otherwise\r\n\t */\r\n\tupdateUserSettings(settings: Partial<UserSettingsConfig>): Promise<boolean>\r\n\r\n\t/**\r\n\t * Dispose of the settings service and clean up resources\r\n\t */\r\n\tdispose(): void\r\n}\r\n\r\n/**\r\n * SettingsServiceEvents\r\n */\r\n\r\nexport interface SettingsServiceEvents {\r\n\t\"settings-updated\": [data: Record<string, never>]\r\n}\r\n\r\n/**\r\n * CloudServiceEvents\r\n */\r\n\r\nexport type CloudServiceEvents = AuthServiceEvents & SettingsServiceEvents\r\n\r\n/**\r\n * ConnectionState\r\n */\r\n\r\nexport enum ConnectionState {\r\n\tDISCONNECTED = \"disconnected\",\r\n\tCONNECTING = \"connecting\",\r\n\tCONNECTED = \"connected\",\r\n\tRETRYING = \"retrying\",\r\n\tFAILED = \"failed\",\r\n}\r\n\r\n/**\r\n * RetryConfig\r\n */\r\n\r\nexport interface RetryConfig {\r\n\tmaxInitialAttempts: number\r\n\tinitialDelay: number\r\n\tmaxDelay: number\r\n\tbackoffMultiplier: number\r\n}\r\n\r\n/**\r\n * Constants\r\n */\r\n\r\nexport const HEARTBEAT_INTERVAL_MS = 20_000\r\nexport const INSTANCE_TTL_SECONDS = 60\r\n\r\n/**\r\n * ExtensionTask\r\n */\r\n\r\nconst extensionTaskSchema = z.object({\r\n\ttaskId: z.string(),\r\n\ttaskStatus: z.nativeEnum(TaskStatus),\r\n\ttaskAsk: clineMessageSchema.optional(),\r\n\tqueuedMessages: z.array(queuedMessageSchema).optional(),\r\n\tparentTaskId: z.string().optional(),\r\n\tchildTaskId: z.string().optional(),\r\n\ttokenUsage: tokenUsageSchema.optional(),\r\n\t...taskMetadataSchema.shape,\r\n})\r\n\r\nexport type ExtensionTask = z.infer<typeof extensionTaskSchema>\r\n\r\n/**\r\n * ExtensionInstance\r\n */\r\n\r\nexport const extensionInstanceSchema = z.object({\r\n\tinstanceId: z.string(),\r\n\tuserId: z.string(),\r\n\tworkspacePath: z.string(),\r\n\tappProperties: staticAppPropertiesSchema,\r\n\tgitProperties: gitPropertiesSchema.optional(),\r\n\tlastHeartbeat: z.coerce.number(),\r\n\ttask: extensionTaskSchema,\r\n\ttaskAsk: clineMessageSchema.optional(),\r\n\ttaskHistory: z.array(z.string()),\r\n\tmode: z.string().optional(),\r\n\tmodes: z.array(z.object({ slug: z.string(), name: z.string() })).optional(),\r\n\tproviderProfile: z.string().optional(),\r\n\tproviderProfiles: z.array(z.object({ name: z.string(), provider: z.string().optional() })).optional(),\r\n})\r\n\r\nexport type ExtensionInstance = z.infer<typeof extensionInstanceSchema>\r\n\r\n/**\r\n * ExtensionBridgeEvent\r\n */\r\n\r\nexport enum ExtensionBridgeEventName {\r\n\tTaskCreated = RooCodeEventName.TaskCreated,\r\n\tTaskStarted = RooCodeEventName.TaskStarted,\r\n\tTaskCompleted = RooCodeEventName.TaskCompleted,\r\n\tTaskAborted = RooCodeEventName.TaskAborted,\r\n\tTaskFocused = RooCodeEventName.TaskFocused,\r\n\tTaskUnfocused = RooCodeEventName.TaskUnfocused,\r\n\tTaskActive = RooCodeEventName.TaskActive,\r\n\tTaskInteractive = RooCodeEventName.TaskInteractive,\r\n\tTaskResumable = RooCodeEventName.TaskResumable,\r\n\tTaskIdle = RooCodeEventName.TaskIdle,\r\n\r\n\tTaskPaused = RooCodeEventName.TaskPaused,\r\n\tTaskUnpaused = RooCodeEventName.TaskUnpaused,\r\n\tTaskSpawned = RooCodeEventName.TaskSpawned,\r\n\r\n\tTaskUserMessage = RooCodeEventName.TaskUserMessage,\r\n\r\n\tTaskTokenUsageUpdated = RooCodeEventName.TaskTokenUsageUpdated,\r\n\r\n\tModeChanged = RooCodeEventName.ModeChanged,\r\n\tProviderProfileChanged = RooCodeEventName.ProviderProfileChanged,\r\n\r\n\tInstanceRegistered = \"instance_registered\",\r\n\tInstanceUnregistered = \"instance_unregistered\",\r\n\tHeartbeatUpdated = \"heartbeat_updated\",\r\n}\r\n\r\nexport const extensionBridgeEventSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskCreated),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskStarted),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskCompleted),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskAborted),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskFocused),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskUnfocused),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskActive),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskInteractive),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskResumable),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskIdle),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskPaused),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskUnpaused),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskSpawned),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskUserMessage),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.TaskTokenUsageUpdated),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.ModeChanged),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\tmode: z.string(),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.ProviderProfileChanged),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\tproviderProfile: z.object({ name: z.string(), provider: z.string().optional() }),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.InstanceRegistered),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.InstanceUnregistered),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeEventName.HeartbeatUpdated),\r\n\t\tinstance: extensionInstanceSchema,\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n])\r\n\r\nexport type ExtensionBridgeEvent = z.infer<typeof extensionBridgeEventSchema>\r\n\r\n/**\r\n * ExtensionBridgeCommand\r\n */\r\n\r\nexport enum ExtensionBridgeCommandName {\r\n\tStartTask = \"start_task\",\r\n\tStopTask = \"stop_task\",\r\n\tResumeTask = \"resume_task\",\r\n}\r\n\r\nexport const extensionBridgeCommandSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeCommandName.StartTask),\r\n\t\tinstanceId: z.string(),\r\n\t\tpayload: z.object({\r\n\t\t\ttext: z.string(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t\tmode: z.string().optional(),\r\n\t\t\tproviderProfile: z.string().optional(),\r\n\t\t}),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeCommandName.StopTask),\r\n\t\tinstanceId: z.string(),\r\n\t\tpayload: z.object({ taskId: z.string() }),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(ExtensionBridgeCommandName.ResumeTask),\r\n\t\tinstanceId: z.string(),\r\n\t\tpayload: z.object({ taskId: z.string() }),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n])\r\n\r\nexport type ExtensionBridgeCommand = z.infer<typeof extensionBridgeCommandSchema>\r\n\r\n/**\r\n * TaskBridgeEvent\r\n */\r\n\r\nexport enum TaskBridgeEventName {\r\n\tMessage = RooCodeEventName.Message,\r\n\tTaskModeSwitched = RooCodeEventName.TaskModeSwitched,\r\n\tTaskInteractive = RooCodeEventName.TaskInteractive,\r\n}\r\n\r\nexport const taskBridgeEventSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeEventName.Message),\r\n\t\ttaskId: z.string(),\r\n\t\taction: z.string(),\r\n\t\tmessage: clineMessageSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeEventName.TaskModeSwitched),\r\n\t\ttaskId: z.string(),\r\n\t\tmode: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeEventName.TaskInteractive),\r\n\t\ttaskId: z.string(),\r\n\t}),\r\n])\r\n\r\nexport type TaskBridgeEvent = z.infer<typeof taskBridgeEventSchema>\r\n\r\n/**\r\n * TaskBridgeCommand\r\n */\r\n\r\nexport enum TaskBridgeCommandName {\r\n\tMessage = \"message\",\r\n\tApproveAsk = \"approve_ask\",\r\n\tDenyAsk = \"deny_ask\",\r\n}\r\n\r\nexport const taskBridgeCommandSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeCommandName.Message),\r\n\t\ttaskId: z.string(),\r\n\t\tpayload: z.object({\r\n\t\t\ttext: z.string(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t\tmode: z.string().optional(),\r\n\t\t\tproviderProfile: z.string().optional(),\r\n\t\t}),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeCommandName.ApproveAsk),\r\n\t\ttaskId: z.string(),\r\n\t\tpayload: z.object({\r\n\t\t\ttext: z.string().optional(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t}),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TaskBridgeCommandName.DenyAsk),\r\n\t\ttaskId: z.string(),\r\n\t\tpayload: z.object({\r\n\t\t\ttext: z.string().optional(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t}),\r\n\t\ttimestamp: z.number(),\r\n\t}),\r\n])\r\n\r\nexport type TaskBridgeCommand = z.infer<typeof taskBridgeCommandSchema>\r\n\r\n/**\r\n * ExtensionSocketEvents\r\n */\r\n\r\nexport enum ExtensionSocketEvents {\r\n\tCONNECTED = \"extension:connected\",\r\n\r\n\tREGISTER = \"extension:register\",\r\n\tUNREGISTER = \"extension:unregister\",\r\n\r\n\tHEARTBEAT = \"extension:heartbeat\",\r\n\r\n\tEVENT = \"extension:event\", // event from extension instance\r\n\tRELAYED_EVENT = \"extension:relayed_event\", // relay from server\r\n\r\n\tCOMMAND = \"extension:command\", // command from user\r\n\tRELAYED_COMMAND = \"extension:relayed_command\", // relay from server\r\n}\r\n\r\n/**\r\n * TaskSocketEvents\r\n */\r\n\r\nexport enum TaskSocketEvents {\r\n\tJOIN = \"task:join\",\r\n\tLEAVE = \"task:leave\",\r\n\r\n\tEVENT = \"task:event\", // event from extension task\r\n\tRELAYED_EVENT = \"task:relayed_event\", // relay from server\r\n\r\n\tCOMMAND = \"task:command\", // command from user\r\n\tRELAYED_COMMAND = \"task:relayed_command\", // relay from server\r\n}\r\n\r\n/**\r\n * `emit()` Response Types\r\n */\r\n\r\nexport type JoinResponse = {\r\n\tsuccess: boolean\r\n\terror?: string\r\n\ttaskId?: string\r\n\ttimestamp?: string\r\n}\r\n\r\nexport type LeaveResponse = {\r\n\tsuccess: boolean\r\n\ttaskId?: string\r\n\ttimestamp?: string\r\n}\r\n", "import { z } from \"zod\"\r\n\r\nimport { clineMessageSchema, tokenUsageSchema } from \"./message.js\"\r\nimport { toolNamesSchema, toolUsageSchema } from \"./tool.js\"\r\n\r\n/**\r\n * RooCodeEventName\r\n */\r\n\r\nexport enum RooCodeEventName {\r\n\t// Task Provider Lifecycle\r\n\tTaskCreated = \"taskCreated\",\r\n\r\n\t// Task Lifecycle\r\n\tTaskStarted = \"taskStarted\",\r\n\tTaskCompleted = \"taskCompleted\",\r\n\tTaskAborted = \"taskAborted\",\r\n\tTaskFocused = \"taskFocused\",\r\n\tTaskUnfocused = \"taskUnfocused\",\r\n\tTaskActive = \"taskActive\",\r\n\tTaskInteractive = \"taskInteractive\",\r\n\tTaskResumable = \"taskResumable\",\r\n\tTaskIdle = \"taskIdle\",\r\n\r\n\t// Subtask Lifecycle\r\n\tTaskPaused = \"taskPaused\",\r\n\tTaskUnpaused = \"taskUnpaused\",\r\n\tTaskSpawned = \"taskSpawned\",\r\n\r\n\t// Task Execution\r\n\tMessage = \"message\",\r\n\tTaskModeSwitched = \"taskModeSwitched\",\r\n\tTaskAskResponded = \"taskAskResponded\",\r\n\tTaskUserMessage = \"taskUserMessage\",\r\n\r\n\t// Task Analytics\r\n\tTaskTokenUsageUpdated = \"taskTokenUsageUpdated\",\r\n\tTaskToolFailed = \"taskToolFailed\",\r\n\r\n\t// Configuration Changes\r\n\tModeChanged = \"modeChanged\",\r\n\tProviderProfileChanged = \"providerProfileChanged\",\r\n\r\n\t// Evals\r\n\tEvalPass = \"evalPass\",\r\n\tEvalFail = \"evalFail\",\r\n}\r\n\r\n/**\r\n * RooCodeEvents\r\n */\r\n\r\nexport const rooCodeEventsSchema = z.object({\r\n\t[RooCodeEventName.TaskCreated]: z.tuple([z.string()]),\r\n\r\n\t[RooCodeEventName.TaskStarted]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskCompleted]: z.tuple([\r\n\t\tz.string(),\r\n\t\ttokenUsageSchema,\r\n\t\ttoolUsageSchema,\r\n\t\tz.object({\r\n\t\t\tisSubtask: z.boolean(),\r\n\t\t}),\r\n\t]),\r\n\t[RooCodeEventName.TaskAborted]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskFocused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskUnfocused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskActive]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskInteractive]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskResumable]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskIdle]: z.tuple([z.string()]),\r\n\r\n\t[RooCodeEventName.TaskPaused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskUnpaused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskSpawned]: z.tuple([z.string(), z.string()]),\r\n\r\n\t[RooCodeEventName.Message]: z.tuple([\r\n\t\tz.object({\r\n\t\t\ttaskId: z.string(),\r\n\t\t\taction: z.union([z.literal(\"created\"), z.literal(\"updated\")]),\r\n\t\t\tmessage: clineMessageSchema,\r\n\t\t}),\r\n\t]),\r\n\t[RooCodeEventName.TaskModeSwitched]: z.tuple([z.string(), z.string()]),\r\n\t[RooCodeEventName.TaskAskResponded]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskUserMessage]: z.tuple([z.string()]),\r\n\r\n\t[RooCodeEventName.TaskToolFailed]: z.tuple([z.string(), toolNamesSchema, z.string()]),\r\n\t[RooCodeEventName.TaskTokenUsageUpdated]: z.tuple([z.string(), tokenUsageSchema]),\r\n\r\n\t[RooCodeEventName.ModeChanged]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.ProviderProfileChanged]: z.tuple([z.object({ name: z.string(), provider: z.string() })]),\r\n})\r\n\r\nexport type RooCodeEvents = z.infer<typeof rooCodeEventsSchema>\r\n\r\n/**\r\n * TaskEvent\r\n */\r\n\r\nexport const taskEventSchema = z.discriminatedUnion(\"eventName\", [\r\n\t// Task Provider Lifecycle\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskCreated),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskCreated],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\r\n\t// Task Lifecycle\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskStarted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskStarted],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskCompleted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskCompleted],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskAborted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskAborted],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskFocused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskFocused],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskUnfocused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskUnfocused],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskActive),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskActive],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskInteractive),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskInteractive],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskResumable),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskResumable],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskIdle),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskIdle],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\r\n\t// Subtask Lifecycle\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskPaused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskPaused],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskUnpaused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskUnpaused],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskSpawned),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskSpawned],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\r\n\t// Task Execution\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.Message),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.Message],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskModeSwitched),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskModeSwitched],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskAskResponded),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskAskResponded],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\r\n\t// Task Analytics\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskToolFailed),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskToolFailed],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskTokenUsageUpdated),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskTokenUsageUpdated],\r\n\t\ttaskId: z.number().optional(),\r\n\t}),\r\n\r\n\t// Evals\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.EvalPass),\r\n\t\tpayload: z.undefined(),\r\n\t\ttaskId: z.number(),\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.EvalFail),\r\n\t\tpayload: z.undefined(),\r\n\t\ttaskId: z.number(),\r\n\t}),\r\n])\r\n\r\nexport type TaskEvent = z.infer<typeof taskEventSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ClineAsk\r\n */\r\n\r\n/**\r\n * Array of possible ask types that the LLM can use to request user interaction or approval.\r\n * These represent different scenarios where the assistant needs user input to proceed.\r\n *\r\n * @constant\r\n * @readonly\r\n *\r\n * Ask type descriptions:\r\n * - `followup`: LLM asks a clarifying question to gather more information needed to complete the task\r\n * - `command`: Permission to execute a terminal/shell command\r\n * - `command_output`: Permission to read the output from a previously executed command\r\n * - `completion_result`: Task has been completed, awaiting user feedback or a new task\r\n * - `tool`: Permission to use a tool for file operations (read, write, search, etc.)\r\n * - `api_req_failed`: API request failed, asking user whether to retry\r\n * - `resume_task`: Confirmation needed to resume a previously paused task\r\n * - `resume_completed_task`: Confirmation needed to resume a task that was already marked as completed\r\n * - `mistake_limit_reached`: Too many errors encountered, needs user guidance on how to proceed\r\n * - `browser_action_launch`: Permission to open or interact with a browser\r\n * - `use_mcp_server`: Permission to use Model Context Protocol (MCP) server functionality\r\n * - `auto_approval_max_req_reached`: Auto-approval limit has been reached, manual approval required\r\n */\r\nexport const clineAsks = [\r\n\t\"followup\",\r\n\t\"command\",\r\n\t\"command_output\",\r\n\t\"completion_result\",\r\n\t\"tool\",\r\n\t\"api_req_failed\",\r\n\t\"resume_task\",\r\n\t\"resume_completed_task\",\r\n\t\"mistake_limit_reached\",\r\n\t\"browser_action_launch\",\r\n\t\"use_mcp_server\",\r\n\t\"auto_approval_max_req_reached\",\r\n] as const\r\n\r\nexport const clineAskSchema = z.enum(clineAsks)\r\n\r\nexport type ClineAsk = z.infer<typeof clineAskSchema>\r\n\r\n// Needs classification:\r\n// - `followup`\r\n// - `command_output\r\n\r\n/**\r\n * IdleAsk\r\n *\r\n * Asks that put the task into an \"idle\" state.\r\n */\r\n\r\nexport const idleAsks = [\r\n\t\"completion_result\",\r\n\t\"api_req_failed\",\r\n\t\"resume_completed_task\",\r\n\t\"mistake_limit_reached\",\r\n\t\"auto_approval_max_req_reached\",\r\n] as const satisfies readonly ClineAsk[]\r\n\r\nexport type IdleAsk = (typeof idleAsks)[number]\r\n\r\nexport function isIdleAsk(ask: ClineAsk): ask is IdleAsk {\r\n\treturn (idleAsks as readonly ClineAsk[]).includes(ask)\r\n}\r\n\r\n/**\r\n * ResumableAsk\r\n *\r\n * Asks that put the task into an \"resumable\" state.\r\n */\r\n\r\nexport const resumableAsks = [\"resume_task\"] as const satisfies readonly ClineAsk[]\r\n\r\nexport type ResumableAsk = (typeof resumableAsks)[number]\r\n\r\nexport function isResumableAsk(ask: ClineAsk): ask is ResumableAsk {\r\n\treturn (resumableAsks as readonly ClineAsk[]).includes(ask)\r\n}\r\n\r\n/**\r\n * InteractiveAsk\r\n *\r\n * Asks that put the task into an \"user interaction required\" state.\r\n */\r\n\r\nexport const interactiveAsks = [\r\n\t\"command\",\r\n\t\"tool\",\r\n\t\"browser_action_launch\",\r\n\t\"use_mcp_server\",\r\n] as const satisfies readonly ClineAsk[]\r\n\r\nexport type InteractiveAsk = (typeof interactiveAsks)[number]\r\n\r\nexport function isInteractiveAsk(ask: ClineAsk): ask is InteractiveAsk {\r\n\treturn (interactiveAsks as readonly ClineAsk[]).includes(ask)\r\n}\r\n\r\n/**\r\n * ClineSay\r\n */\r\n\r\n/**\r\n * Array of possible say types that represent different kinds of messages the assistant can send.\r\n * These are used to categorize and handle various types of communication from the LLM to the user.\r\n *\r\n * @constant\r\n * @readonly\r\n *\r\n * Say type descriptions:\r\n * - `error`: General error message\r\n * - `api_req_started`: Indicates an API request has been initiated\r\n * - `api_req_finished`: Indicates an API request has completed successfully\r\n * - `api_req_retried`: Indicates an API request is being retried after a failure\r\n * - `api_req_retry_delayed`: Indicates an API request retry has been delayed\r\n * - `api_req_deleted`: Indicates an API request has been deleted/cancelled\r\n * - `text`: General text message or assistant response\r\n * - `reasoning`: Assistant's reasoning or thought process (often hidden from user)\r\n * - `completion_result`: Final result of task completion\r\n * - `user_feedback`: Message containing user feedback\r\n * - `user_feedback_diff`: Diff-formatted feedback from user showing requested changes\r\n * - `command_output`: Output from an executed command\r\n * - `shell_integration_warning`: Warning about shell integration issues or limitations\r\n * - `browser_action`: Action performed in the browser\r\n * - `browser_action_result`: Result of a browser action\r\n * - `mcp_server_request_started`: MCP server request has been initiated\r\n * - `mcp_server_response`: Response received from MCP server\r\n * - `subtask_result`: Result of a completed subtask\r\n * - `checkpoint_saved`: Indicates a checkpoint has been saved\r\n * - `rooignore_error`: Error related to .rooignore file processing\r\n * - `diff_error`: Error occurred while applying a diff/patch\r\n * - `condense_context`: Context condensation/summarization has started\r\n * - `condense_context_error`: Error occurred during context condensation\r\n * - `codebase_search_result`: Results from searching the codebase\r\n */\r\nexport const clineSays = [\r\n\t\"error\",\r\n\t\"api_req_started\",\r\n\t\"api_req_finished\",\r\n\t\"api_req_retried\",\r\n\t\"api_req_retry_delayed\",\r\n\t\"api_req_deleted\",\r\n\t\"text\",\r\n\t\"image\",\r\n\t\"reasoning\",\r\n\t\"completion_result\",\r\n\t\"user_feedback\",\r\n\t\"user_feedback_diff\",\r\n\t\"command_output\",\r\n\t\"shell_integration_warning\",\r\n\t\"browser_action\",\r\n\t\"browser_action_result\",\r\n\t\"mcp_server_request_started\",\r\n\t\"mcp_server_response\",\r\n\t\"subtask_result\",\r\n\t\"checkpoint_saved\",\r\n\t\"rooignore_error\",\r\n\t\"diff_error\",\r\n\t\"condense_context\",\r\n\t\"condense_context_error\",\r\n\t\"codebase_search_result\",\r\n\t\"user_edit_todos\",\r\n] as const\r\n\r\nexport const clineSaySchema = z.enum(clineSays)\r\n\r\nexport type ClineSay = z.infer<typeof clineSaySchema>\r\n\r\n/**\r\n * ToolProgressStatus\r\n */\r\n\r\nexport const toolProgressStatusSchema = z.object({\r\n\ticon: z.string().optional(),\r\n\ttext: z.string().optional(),\r\n})\r\n\r\nexport type ToolProgressStatus = z.infer<typeof toolProgressStatusSchema>\r\n\r\n/**\r\n * ContextCondense\r\n */\r\n\r\nexport const contextCondenseSchema = z.object({\r\n\tcost: z.number(),\r\n\tprevContextTokens: z.number(),\r\n\tnewContextTokens: z.number(),\r\n\tsummary: z.string(),\r\n})\r\n\r\nexport type ContextCondense = z.infer<typeof contextCondenseSchema>\r\n\r\n/**\r\n * ClineMessage\r\n */\r\n\r\nexport const clineMessageSchema = z.object({\r\n\tts: z.number(),\r\n\ttype: z.union([z.literal(\"ask\"), z.literal(\"say\")]),\r\n\task: clineAskSchema.optional(),\r\n\tsay: clineSaySchema.optional(),\r\n\ttext: z.string().optional(),\r\n\timages: z.array(z.string()).optional(),\r\n\tpartial: z.boolean().optional(),\r\n\treasoning: z.string().optional(),\r\n\tconversationHistoryIndex: z.number().optional(),\r\n\tcheckpoint: z.record(z.string(), z.unknown()).optional(),\r\n\tprogressStatus: toolProgressStatusSchema.optional(),\r\n\tcontextCondense: contextCondenseSchema.optional(),\r\n\tisProtected: z.boolean().optional(),\r\n\tapiProtocol: z.union([z.literal(\"openai\"), z.literal(\"anthropic\")]).optional(),\r\n\tisAnswered: z.boolean().optional(),\r\n\tmetadata: z\r\n\t\t.object({\r\n\t\t\tgpt5: z\r\n\t\t\t\t.object({\r\n\t\t\t\t\tprevious_response_id: z.string().optional(),\r\n\t\t\t\t\tinstructions: z.string().optional(),\r\n\t\t\t\t\treasoning_summary: z.string().optional(),\r\n\t\t\t\t})\r\n\t\t\t\t.optional(),\r\n\t\t})\r\n\t\t.optional(),\r\n})\r\n\r\nexport type ClineMessage = z.infer<typeof clineMessageSchema>\r\n\r\n/**\r\n * TokenUsage\r\n */\r\n\r\nexport const tokenUsageSchema = z.object({\r\n\ttotalTokensIn: z.number(),\r\n\ttotalTokensOut: z.number(),\r\n\ttotalCacheWrites: z.number().optional(),\r\n\ttotalCacheReads: z.number().optional(),\r\n\ttotalCost: z.number(),\r\n\tcontextTokens: z.number(),\r\n})\r\n\r\nexport type TokenUsage = z.infer<typeof tokenUsageSchema>\r\n\r\n/**\r\n * QueuedMessage\r\n */\r\n\r\nexport const queuedMessageSchema = z.object({\r\n\ttimestamp: z.number(),\r\n\tid: z.string(),\r\n\ttext: z.string(),\r\n\timages: z.array(z.string()).optional(),\r\n})\r\n\r\nexport type QueuedMessage = z.infer<typeof queuedMessageSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ToolGroup\r\n */\r\n\r\nexport const toolGroups = [\"read\", \"edit\", \"browser\", \"command\", \"mcp\", \"modes\"] as const\r\n\r\nexport const toolGroupsSchema = z.enum(toolGroups)\r\n\r\nexport type ToolGroup = z.infer<typeof toolGroupsSchema>\r\n\r\n/**\r\n * ToolName\r\n */\r\n\r\nexport const toolNames = [\r\n\t\"execute_command\",\r\n\t\"read_file\",\r\n\t\"write_to_file\",\r\n\t\"apply_diff\",\r\n\t\"insert_content\",\r\n\t\"search_and_replace\",\r\n\t\"search_files\",\r\n\t\"list_files\",\r\n\t\"list_code_definition_names\",\r\n\t\"browser_action\",\r\n\t\"use_mcp_tool\",\r\n\t\"access_mcp_resource\",\r\n\t\"ask_followup_question\",\r\n\t\"attempt_completion\",\r\n\t\"switch_mode\",\r\n\t\"new_task\",\r\n\t\"fetch_instructions\",\r\n\t\"codebase_search\",\r\n\t\"update_todo_list\",\r\n\t\"run_slash_command\",\r\n\t\"generate_image\",\r\n] as const\r\n\r\nexport const toolNamesSchema = z.enum(toolNames)\r\n\r\nexport type ToolName = z.infer<typeof toolNamesSchema>\r\n\r\n/**\r\n * ToolUsage\r\n */\r\n\r\nexport const toolUsageSchema = z.record(\r\n\ttoolNamesSchema,\r\n\tz.object({\r\n\t\tattempts: z.number(),\r\n\t\tfailures: z.number(),\r\n\t}),\r\n)\r\n\r\nexport type ToolUsage = z.infer<typeof toolUsageSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport { RooCodeEventName } from \"./events.js\"\r\nimport type { RooCodeSettings } from \"./global-settings.js\"\r\nimport type { ClineMessage, QueuedMessage, TokenUsage } from \"./message.js\"\r\nimport type { ToolUsage, ToolName } from \"./tool.js\"\r\nimport type { StaticAppProperties, GitProperties, TelemetryProperties } from \"./telemetry.js\"\r\nimport type { TodoItem } from \"./todo.js\"\r\n\r\n/**\r\n * TaskProviderLike\r\n */\r\n\r\nexport interface TaskProviderLike {\r\n\t// Tasks\r\n\tgetCurrentTask(): TaskLike | undefined\r\n\tgetRecentTasks(): string[]\r\n\tcreateTask(\r\n\t\ttext?: string,\r\n\t\timages?: string[],\r\n\t\tparentTask?: TaskLike,\r\n\t\toptions?: CreateTaskOptions,\r\n\t\tconfiguration?: RooCodeSettings,\r\n\t): Promise<TaskLike>\r\n\tcancelTask(): Promise<void>\r\n\tclearTask(): Promise<void>\r\n\tresumeTask(taskId: string): void\r\n\r\n\t// Modes\r\n\tgetModes(): Promise<{ slug: string; name: string }[]>\r\n\tgetMode(): Promise<string>\r\n\tsetMode(mode: string): Promise<void>\r\n\r\n\t// Provider Profiles\r\n\tgetProviderProfiles(): Promise<{ name: string; provider?: string }[]>\r\n\tgetProviderProfile(): Promise<string>\r\n\tsetProviderProfile(providerProfile: string): Promise<void>\r\n\r\n\t// Telemetry\r\n\treadonly appProperties: StaticAppProperties\r\n\treadonly gitProperties: GitProperties | undefined\r\n\tgetTelemetryProperties(): Promise<TelemetryProperties>\r\n\treadonly cwd: string\r\n\r\n\t// Event Emitter\r\n\ton<K extends keyof TaskProviderEvents>(\r\n\t\tevent: K,\r\n\t\tlistener: (...args: TaskProviderEvents[K]) => void | Promise<void>,\r\n\t): this\r\n\r\n\toff<K extends keyof TaskProviderEvents>(\r\n\t\tevent: K,\r\n\t\tlistener: (...args: TaskProviderEvents[K]) => void | Promise<void>,\r\n\t): this\r\n\r\n\t// @TODO: Find a better way to do this.\r\n\tpostStateToWebview(): Promise<void>\r\n}\r\n\r\nexport type TaskProviderEvents = {\r\n\t[RooCodeEventName.TaskCreated]: [task: TaskLike]\r\n\t[RooCodeEventName.TaskStarted]: [taskId: string]\r\n\t[RooCodeEventName.TaskCompleted]: [taskId: string, tokenUsage: TokenUsage, toolUsage: ToolUsage]\r\n\t[RooCodeEventName.TaskAborted]: [taskId: string]\r\n\t[RooCodeEventName.TaskFocused]: [taskId: string]\r\n\t[RooCodeEventName.TaskUnfocused]: [taskId: string]\r\n\t[RooCodeEventName.TaskActive]: [taskId: string]\r\n\t[RooCodeEventName.TaskInteractive]: [taskId: string]\r\n\t[RooCodeEventName.TaskResumable]: [taskId: string]\r\n\t[RooCodeEventName.TaskIdle]: [taskId: string]\r\n\r\n\t[RooCodeEventName.TaskPaused]: [taskId: string]\r\n\t[RooCodeEventName.TaskUnpaused]: [taskId: string]\r\n\t[RooCodeEventName.TaskSpawned]: [taskId: string]\r\n\r\n\t[RooCodeEventName.TaskUserMessage]: [taskId: string]\r\n\r\n\t[RooCodeEventName.TaskTokenUsageUpdated]: [taskId: string, tokenUsage: TokenUsage]\r\n\r\n\t[RooCodeEventName.ModeChanged]: [mode: string]\r\n\t[RooCodeEventName.ProviderProfileChanged]: [config: { name: string; provider?: string }]\r\n}\r\n\r\n/**\r\n * TaskLike\r\n */\r\n\r\nexport interface CreateTaskOptions {\r\n\tenableDiff?: boolean\r\n\tenableCheckpoints?: boolean\r\n\tfuzzyMatchThreshold?: number\r\n\tconsecutiveMistakeLimit?: number\r\n\texperiments?: Record<string, boolean>\r\n\tinitialTodos?: TodoItem[]\r\n}\r\n\r\nexport enum TaskStatus {\r\n\tRunning = \"running\",\r\n\tInteractive = \"interactive\",\r\n\tResumable = \"resumable\",\r\n\tIdle = \"idle\",\r\n\tNone = \"none\",\r\n}\r\n\r\nexport const taskMetadataSchema = z.object({\r\n\ttask: z.string().optional(),\r\n\timages: z.array(z.string()).optional(),\r\n})\r\n\r\nexport type TaskMetadata = z.infer<typeof taskMetadataSchema>\r\n\r\nexport interface TaskLike {\r\n\treadonly taskId: string\r\n\treadonly rootTaskId?: string\r\n\treadonly parentTaskId?: string\r\n\treadonly childTaskId?: string\r\n\treadonly metadata: TaskMetadata\r\n\treadonly taskStatus: TaskStatus\r\n\treadonly taskAsk: ClineMessage | undefined\r\n\treadonly queuedMessages: QueuedMessage[]\r\n\treadonly tokenUsage: TokenUsage | undefined\r\n\r\n\ton<K extends keyof TaskEvents>(event: K, listener: (...args: TaskEvents[K]) => void | Promise<void>): this\r\n\toff<K extends keyof TaskEvents>(event: K, listener: (...args: TaskEvents[K]) => void | Promise<void>): this\r\n\r\n\tapproveAsk(options?: { text?: string; images?: string[] }): void\r\n\tdenyAsk(options?: { text?: string; images?: string[] }): void\r\n\tsubmitUserMessage(text: string, images?: string[], mode?: string, providerProfile?: string): Promise<void>\r\n\tabortTask(): void\r\n}\r\n\r\nexport type TaskEvents = {\r\n\t// Task Lifecycle\r\n\t[RooCodeEventName.TaskStarted]: []\r\n\t[RooCodeEventName.TaskCompleted]: [taskId: string, tokenUsage: TokenUsage, toolUsage: ToolUsage]\r\n\t[RooCodeEventName.TaskAborted]: []\r\n\t[RooCodeEventName.TaskFocused]: []\r\n\t[RooCodeEventName.TaskUnfocused]: []\r\n\t[RooCodeEventName.TaskActive]: [taskId: string]\r\n\t[RooCodeEventName.TaskInteractive]: [taskId: string]\r\n\t[RooCodeEventName.TaskResumable]: [taskId: string]\r\n\t[RooCodeEventName.TaskIdle]: [taskId: string]\r\n\r\n\t// Subtask Lifecycle\r\n\t[RooCodeEventName.TaskPaused]: [taskId: string]\r\n\t[RooCodeEventName.TaskUnpaused]: [taskId: string]\r\n\t[RooCodeEventName.TaskSpawned]: [taskId: string]\r\n\r\n\t// Task Execution\r\n\t[RooCodeEventName.Message]: [{ action: \"created\" | \"updated\"; message: ClineMessage }]\r\n\t[RooCodeEventName.TaskModeSwitched]: [taskId: string, mode: string]\r\n\t[RooCodeEventName.TaskAskResponded]: []\r\n\t[RooCodeEventName.TaskUserMessage]: [taskId: string]\r\n\r\n\t// Task Analytics\r\n\t[RooCodeEventName.TaskToolFailed]: [taskId: string, tool: ToolName, error: string]\r\n\t[RooCodeEventName.TaskTokenUsageUpdated]: [taskId: string, tokenUsage: TokenUsage]\r\n}\r\n", "import { z } from \"zod\"\r\n\r\nimport { type Keys } from \"./type-fu.js\"\r\nimport {\r\n\ttype ProviderSettings,\r\n\tPROVIDER_SETTINGS_KEYS,\r\n\tproviderSettingsEntrySchema,\r\n\tproviderSettingsSchema,\r\n} from \"./provider-settings.js\"\r\nimport { historyItemSchema } from \"./history.js\"\r\nimport { codebaseIndexModelsSchema, codebaseIndexConfigSchema } from \"./codebase-index.js\"\r\nimport { experimentsSchema } from \"./experiment.js\"\r\nimport { telemetrySettingsSchema } from \"./telemetry.js\"\r\nimport { modeConfigSchema } from \"./mode.js\"\r\nimport { customModePromptsSchema, customSupportPromptsSchema } from \"./mode.js\"\r\nimport { languagesSchema } from \"./vscode.js\"\r\n\r\n/**\r\n * Default delay in milliseconds after writes to allow diagnostics to detect potential problems.\r\n * This delay is particularly important for Go and other languages where tools like goimports\r\n * need time to automatically clean up unused imports.\r\n */\r\nexport const DEFAULT_WRITE_DELAY_MS = 1000\r\n\r\n/**\r\n * Default terminal output character limit constant.\r\n * This provides a reasonable default that aligns with typical terminal usage\r\n * while preventing context window explosions from extremely long lines.\r\n */\r\nexport const DEFAULT_TERMINAL_OUTPUT_CHARACTER_LIMIT = 50_000\r\n\r\n/**\r\n * GlobalSettings\r\n */\r\n\r\nexport const globalSettingsSchema = z.object({\r\n\tcurrentApiConfigName: z.string().optional(),\r\n\tlistApiConfigMeta: z.array(providerSettingsEntrySchema).optional(),\r\n\tpinnedApiConfigs: z.record(z.string(), z.boolean()).optional(),\r\n\r\n\tlastShownAnnouncementId: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n\ttaskHistory: z.array(historyItemSchema).optional(),\r\n\r\n\t// Image generation settings (experimental) - flattened for simplicity\r\n\topenRouterImageApiKey: z.string().optional(),\r\n\topenRouterImageGenerationSelectedModel: z.string().optional(),\r\n\r\n\tcondensingApiConfigId: z.string().optional(),\r\n\tcustomCondensingPrompt: z.string().optional(),\r\n\r\n\tautoApprovalEnabled: z.boolean().optional(),\r\n\talwaysAllowReadOnly: z.boolean().optional(),\r\n\talwaysAllowReadOnlyOutsideWorkspace: z.boolean().optional(),\r\n\talwaysAllowWrite: z.boolean().optional(),\r\n\talwaysAllowWriteOutsideWorkspace: z.boolean().optional(),\r\n\talwaysAllowWriteProtected: z.boolean().optional(),\r\n\twriteDelayMs: z.number().min(0).optional(),\r\n\talwaysAllowBrowser: z.boolean().optional(),\r\n\talwaysApproveResubmit: z.boolean().optional(),\r\n\trequestDelaySeconds: z.number().optional(),\r\n\talwaysAllowMcp: z.boolean().optional(),\r\n\talwaysAllowModeSwitch: z.boolean().optional(),\r\n\talwaysAllowSubtasks: z.boolean().optional(),\r\n\talwaysAllowExecute: z.boolean().optional(),\r\n\talwaysAllowFollowupQuestions: z.boolean().optional(),\r\n\tfollowupAutoApproveTimeoutMs: z.number().optional(),\r\n\talwaysAllowUpdateTodoList: z.boolean().optional(),\r\n\tallowedCommands: z.array(z.string()).optional(),\r\n\tdeniedCommands: z.array(z.string()).optional(),\r\n\tcommandExecutionTimeout: z.number().optional(),\r\n\tcommandTimeoutAllowlist: z.array(z.string()).optional(),\r\n\tpreventCompletionWithOpenTodos: z.boolean().optional(),\r\n\tallowedMaxRequests: z.number().nullish(),\r\n\tallowedMaxCost: z.number().nullish(),\r\n\tautoCondenseContext: z.boolean().optional(),\r\n\tautoCondenseContextPercent: z.number().optional(),\r\n\tmaxConcurrentFileReads: z.number().optional(),\r\n\r\n\t/**\r\n\t * Whether to include diagnostic messages (errors, warnings) in tool outputs\r\n\t * @default true\r\n\t */\r\n\tincludeDiagnosticMessages: z.boolean().optional(),\r\n\t/**\r\n\t * Maximum number of diagnostic messages to include in tool outputs\r\n\t * @default 50\r\n\t */\r\n\tmaxDiagnosticMessages: z.number().optional(),\r\n\r\n\tbrowserToolEnabled: z.boolean().optional(),\r\n\tbrowserViewportSize: z.string().optional(),\r\n\tscreenshotQuality: z.number().optional(),\r\n\tremoteBrowserEnabled: z.boolean().optional(),\r\n\tremoteBrowserHost: z.string().optional(),\r\n\tcachedChromeHostUrl: z.string().optional(),\r\n\r\n\tenableCheckpoints: z.boolean().optional(),\r\n\r\n\tttsEnabled: z.boolean().optional(),\r\n\tttsSpeed: z.number().optional(),\r\n\tsoundEnabled: z.boolean().optional(),\r\n\tsoundVolume: z.number().optional(),\r\n\r\n\tmaxOpenTabsContext: z.number().optional(),\r\n\tmaxWorkspaceFiles: z.number().optional(),\r\n\tshowRooIgnoredFiles: z.boolean().optional(),\r\n\tmaxReadFileLine: z.number().optional(),\r\n\tmaxImageFileSize: z.number().optional(),\r\n\tmaxTotalImageSize: z.number().optional(),\r\n\r\n\tterminalOutputLineLimit: z.number().optional(),\r\n\tterminalOutputCharacterLimit: z.number().optional(),\r\n\tterminalShellIntegrationTimeout: z.number().optional(),\r\n\tterminalShellIntegrationDisabled: z.boolean().optional(),\r\n\tterminalCommandDelay: z.number().optional(),\r\n\tterminalPowershellCounter: z.boolean().optional(),\r\n\tterminalZshClearEolMark: z.boolean().optional(),\r\n\tterminalZshOhMy: z.boolean().optional(),\r\n\tterminalZshP10k: z.boolean().optional(),\r\n\tterminalZdotdir: z.boolean().optional(),\r\n\tterminalCompressProgressBar: z.boolean().optional(),\r\n\r\n\tdiagnosticsEnabled: z.boolean().optional(),\r\n\r\n\trateLimitSeconds: z.number().optional(),\r\n\tdiffEnabled: z.boolean().optional(),\r\n\tfuzzyMatchThreshold: z.number().optional(),\r\n\texperiments: experimentsSchema.optional(),\r\n\r\n\tcodebaseIndexModels: codebaseIndexModelsSchema.optional(),\r\n\tcodebaseIndexConfig: codebaseIndexConfigSchema.optional(),\r\n\r\n\tlanguage: languagesSchema.optional(),\r\n\r\n\ttelemetrySetting: telemetrySettingsSchema.optional(),\r\n\r\n\tmcpEnabled: z.boolean().optional(),\r\n\tenableMcpServerCreation: z.boolean().optional(),\r\n\r\n\tremoteControlEnabled: z.boolean().optional(),\r\n\r\n\tmode: z.string().optional(),\r\n\tmodeApiConfigs: z.record(z.string(), z.string()).optional(),\r\n\tcustomModes: z.array(modeConfigSchema).optional(),\r\n\tcustomModePrompts: customModePromptsSchema.optional(),\r\n\tcustomSupportPrompts: customSupportPromptsSchema.optional(),\r\n\tenhancementApiConfigId: z.string().optional(),\r\n\tincludeTaskHistoryInEnhance: z.boolean().optional(),\r\n\thistoryPreviewCollapsed: z.boolean().optional(),\r\n\tprofileThresholds: z.record(z.string(), z.number()).optional(),\r\n\thasOpenedModeSelector: z.boolean().optional(),\r\n\tlastModeExportPath: z.string().optional(),\r\n\tlastModeImportPath: z.string().optional(),\r\n})\r\n\r\nexport type GlobalSettings = z.infer<typeof globalSettingsSchema>\r\n\r\nexport const GLOBAL_SETTINGS_KEYS = globalSettingsSchema.keyof().options\r\n\r\n/**\r\n * RooCodeSettings\r\n */\r\n\r\nexport const rooCodeSettingsSchema = providerSettingsSchema.merge(globalSettingsSchema)\r\n\r\nexport type RooCodeSettings = GlobalSettings & ProviderSettings\r\n\r\n/**\r\n * SecretState\r\n */\r\nexport const SECRET_STATE_KEYS = [\r\n\t\"apiKey\",\r\n\t\"glamaApiKey\",\r\n\t\"openRouterApiKey\",\r\n\t\"awsAccessKey\",\r\n\t\"awsApiKey\",\r\n\t\"awsSecretKey\",\r\n\t\"awsSessionToken\",\r\n\t\"openAiApiKey\",\r\n\t\"ollamaApiKey\",\r\n\t\"geminiApiKey\",\r\n\t\"openAiNativeApiKey\",\r\n\t\"cerebrasApiKey\",\r\n\t\"deepSeekApiKey\",\r\n\t\"doubaoApiKey\",\r\n\t\"moonshotApiKey\",\r\n\t\"mistralApiKey\",\r\n\t\"unboundApiKey\",\r\n\t\"requestyApiKey\",\r\n\t\"xaiApiKey\",\r\n\t\"groqApiKey\",\r\n\t\"chutesApiKey\",\r\n\t\"litellmApiKey\",\r\n\t\"deepInfraApiKey\",\r\n\t\"codeIndexOpenAiKey\",\r\n\t\"codeIndexQdrantApiKey\",\r\n\t\"codebaseIndexOpenAiCompatibleApiKey\",\r\n\t\"codebaseIndexGeminiApiKey\",\r\n\t\"codebaseIndexMistralApiKey\",\r\n\t\"codebaseIndexVercelAiGatewayApiKey\",\r\n\t\"huggingFaceApiKey\",\r\n\t\"sambaNovaApiKey\",\r\n\t\"zaiApiKey\",\r\n\t\"fireworksApiKey\",\r\n\t\"featherlessApiKey\",\r\n\t\"ioIntelligenceApiKey\",\r\n\t\"vercelAiGatewayApiKey\",\r\n] as const\r\n\r\n// Global secrets that are part of GlobalSettings (not ProviderSettings)\r\nexport const GLOBAL_SECRET_KEYS = [\r\n\t\"openRouterImageApiKey\", // For image generation\r\n] as const\r\n\r\n// Type for the actual secret storage keys\r\ntype ProviderSecretKey = (typeof SECRET_STATE_KEYS)[number]\r\ntype GlobalSecretKey = (typeof GLOBAL_SECRET_KEYS)[number]\r\n\r\n// Type representing all secrets that can be stored\r\nexport type SecretState = Pick<ProviderSettings, Extract<ProviderSecretKey, keyof ProviderSettings>> & {\r\n\t[K in GlobalSecretKey]?: string\r\n}\r\n\r\nexport const isSecretStateKey = (key: string): key is Keys<SecretState> =>\r\n\tSECRET_STATE_KEYS.includes(key as ProviderSecretKey) || GLOBAL_SECRET_KEYS.includes(key as GlobalSecretKey)\r\n\r\n/**\r\n * GlobalState\r\n */\r\n\r\nexport type GlobalState = Omit<RooCodeSettings, Keys<SecretState>>\r\n\r\nexport const GLOBAL_STATE_KEYS = [...GLOBAL_SETTINGS_KEYS, ...PROVIDER_SETTINGS_KEYS].filter(\r\n\t(key: Keys<RooCodeSettings>) => !isSecretStateKey(key),\r\n) as Keys<GlobalState>[]\r\n\r\nexport const isGlobalStateKey = (key: string): key is Keys<GlobalState> =>\r\n\tGLOBAL_STATE_KEYS.includes(key as Keys<GlobalState>)\r\n\r\n/**\r\n * Evals\r\n */\r\n\r\n// Default settings when running evals (unless overridden).\r\nexport const EVALS_SETTINGS: RooCodeSettings = {\r\n\tapiProvider: \"openrouter\",\r\n\topenRouterUseMiddleOutTransform: false,\r\n\r\n\tlastShownAnnouncementId: \"jul-09-2025-3-23-0\",\r\n\r\n\tpinnedApiConfigs: {},\r\n\r\n\tautoApprovalEnabled: true,\r\n\talwaysAllowReadOnly: true,\r\n\talwaysAllowReadOnlyOutsideWorkspace: false,\r\n\talwaysAllowWrite: true,\r\n\talwaysAllowWriteOutsideWorkspace: false,\r\n\talwaysAllowWriteProtected: false,\r\n\twriteDelayMs: 1000,\r\n\talwaysAllowBrowser: true,\r\n\talwaysApproveResubmit: true,\r\n\trequestDelaySeconds: 10,\r\n\talwaysAllowMcp: true,\r\n\talwaysAllowModeSwitch: true,\r\n\talwaysAllowSubtasks: true,\r\n\talwaysAllowExecute: true,\r\n\talwaysAllowFollowupQuestions: true,\r\n\talwaysAllowUpdateTodoList: true,\r\n\tfollowupAutoApproveTimeoutMs: 0,\r\n\tallowedCommands: [\"*\"],\r\n\tcommandExecutionTimeout: 20,\r\n\tcommandTimeoutAllowlist: [],\r\n\tpreventCompletionWithOpenTodos: false,\r\n\r\n\tbrowserToolEnabled: false,\r\n\tbrowserViewportSize: \"900x600\",\r\n\tscreenshotQuality: 75,\r\n\tremoteBrowserEnabled: false,\r\n\r\n\tttsEnabled: false,\r\n\tttsSpeed: 1,\r\n\tsoundEnabled: false,\r\n\tsoundVolume: 0.5,\r\n\r\n\tterminalOutputLineLimit: 500,\r\n\tterminalOutputCharacterLimit: DEFAULT_TERMINAL_OUTPUT_CHARACTER_LIMIT,\r\n\tterminalShellIntegrationTimeout: 30000,\r\n\tterminalCommandDelay: 0,\r\n\tterminalPowershellCounter: false,\r\n\tterminalZshOhMy: true,\r\n\tterminalZshClearEolMark: true,\r\n\tterminalZshP10k: false,\r\n\tterminalZdotdir: true,\r\n\tterminalCompressProgressBar: true,\r\n\tterminalShellIntegrationDisabled: true,\r\n\r\n\tdiagnosticsEnabled: true,\r\n\r\n\tdiffEnabled: true,\r\n\tfuzzyMatchThreshold: 1,\r\n\r\n\tenableCheckpoints: false,\r\n\r\n\trateLimitSeconds: 0,\r\n\tmaxOpenTabsContext: 20,\r\n\tmaxWorkspaceFiles: 200,\r\n\tshowRooIgnoredFiles: true,\r\n\tmaxReadFileLine: -1, // -1 to enable full file reading.\r\n\r\n\tincludeDiagnosticMessages: true,\r\n\tmaxDiagnosticMessages: 50,\r\n\r\n\tlanguage: \"en\",\r\n\ttelemetrySetting: \"enabled\",\r\n\r\n\tmcpEnabled: false,\r\n\r\n\tremoteControlEnabled: false,\r\n\r\n\tmode: \"code\", // \"architect\",\r\n\r\n\tcustomModes: [],\r\n}\r\n\r\nexport const EVALS_TIMEOUT = 5 * 60 * 1_000\r\n", "import { z } from \"zod\"\r\n\r\nimport { modelInfoSchema, reasoningEffortWithMinimalSchema, verbosityLevelsSchema, serviceTierSchema } from \"./model.js\"\r\nimport { codebaseIndexProviderSchema } from \"./codebase-index.js\"\r\nimport {\r\n\tanthropicModels,\r\n\tbedrockModels,\r\n\tcerebrasModels,\r\n\tchutesModels,\r\n\tclaudeCodeModels,\r\n\tdeepSeekModels,\r\n\tdoubaoModels,\r\n\tfeatherlessModels,\r\n\tfireworksModels,\r\n\tgeminiModels,\r\n\tgeminiCliModels,\r\n\tgroqModels,\r\n\tioIntelligenceModels,\r\n\tmistralModels,\r\n\tmoonshotModels,\r\n\topenAiNativeModels,\r\n\tqwenCodeModels,\r\n\trooModels,\r\n\tsambaNovaModels,\r\n\tvertexModels,\r\n\tvscodeLlmModels,\r\n\txaiModels,\r\n\tinternationalZAiModels,\r\n} from \"./providers/index.js\"\r\n\r\n/**\r\n * ProviderName\r\n */\r\n\r\nexport const providerNames = [\r\n\t\"anthropic\",\r\n\t\"claude-code\",\r\n\t\"glama\",\r\n\t\"openrouter\",\r\n\t\"bedrock\",\r\n\t\"vertex\",\r\n\t\"openai\",\r\n\t\"ollama\",\r\n\t\"vscode-lm\",\r\n\t\"lmstudio\",\r\n\t\"gemini\",\r\n\t\"gemini-cli\",\r\n\t\"openai-native\",\r\n\t\"mistral\",\r\n\t\"moonshot\",\r\n\t\"deepseek\",\r\n\t\"deepinfra\",\r\n\t\"doubao\",\r\n\t\"qwen-code\",\r\n\t\"unbound\",\r\n\t\"requesty\",\r\n\t\"human-relay\",\r\n\t\"fake-ai\",\r\n\t\"xai\",\r\n\t\"groq\",\r\n\t\"chutes\",\r\n\t\"litellm\",\r\n\t\"huggingface\",\r\n\t\"cerebras\",\r\n\t\"sambanova\",\r\n\t\"zai\",\r\n\t\"fireworks\",\r\n\t\"featherless\",\r\n\t\"io-intelligence\",\r\n\t\"roo\",\r\n\t\"vercel-ai-gateway\",\r\n] as const\r\n\r\nexport const providerNamesSchema = z.enum(providerNames)\r\n\r\nexport type ProviderName = z.infer<typeof providerNamesSchema>\r\n\r\n/**\r\n * ProviderSettingsEntry\r\n */\r\n\r\nexport const providerSettingsEntrySchema = z.object({\r\n\tid: z.string(),\r\n\tname: z.string(),\r\n\tapiProvider: providerNamesSchema.optional(),\r\n\tmodelId: z.string().optional(),\r\n})\r\n\r\nexport type ProviderSettingsEntry = z.infer<typeof providerSettingsEntrySchema>\r\n\r\n/**\r\n * ProviderSettings\r\n */\r\n\r\n/**\r\n * Default value for consecutive mistake limit\r\n */\r\nexport const DEFAULT_CONSECUTIVE_MISTAKE_LIMIT = 3\r\n\r\nconst baseProviderSettingsSchema = z.object({\r\n\tincludeMaxTokens: z.boolean().optional(),\r\n\tdiffEnabled: z.boolean().optional(),\r\n\ttodoListEnabled: z.boolean().optional(),\r\n\tfuzzyMatchThreshold: z.number().optional(),\r\n\tmodelTemperature: z.number().nullish(),\r\n\trateLimitSeconds: z.number().optional(),\r\n\tconsecutiveMistakeLimit: z.number().min(0).optional(),\r\n\r\n\t// Model reasoning.\r\n\tenableReasoningEffort: z.boolean().optional(),\r\n\treasoningEffort: reasoningEffortWithMinimalSchema.optional(),\r\n\tmodelMaxTokens: z.number().optional(),\r\n\tmodelMaxThinkingTokens: z.number().optional(),\r\n\r\n\t// Model verbosity.\r\n\tverbosity: verbosityLevelsSchema.optional(),\r\n})\r\n\r\n// Several of the providers share common model config properties.\r\nconst apiModelIdProviderModelSchema = baseProviderSettingsSchema.extend({\r\n\tapiModelId: z.string().optional(),\r\n})\r\n\r\nconst anthropicSchema = apiModelIdProviderModelSchema.extend({\r\n\tapiKey: z.string().optional(),\r\n\tanthropicBaseUrl: z.string().optional(),\r\n\tanthropicUseAuthToken: z.boolean().optional(),\r\n\tanthropicBeta1MContext: z.boolean().optional(), // Enable 'context-1m-2025-08-07' beta for 1M context window\r\n})\r\n\r\nconst claudeCodeSchema = apiModelIdProviderModelSchema.extend({\r\n\tclaudeCodePath: z.string().optional(),\r\n\tclaudeCodeMaxOutputTokens: z.number().int().min(1).max(200000).optional(),\r\n})\r\n\r\nconst glamaSchema = baseProviderSettingsSchema.extend({\r\n\tglamaModelId: z.string().optional(),\r\n\tglamaApiKey: z.string().optional(),\r\n})\r\n\r\nconst openRouterSchema = baseProviderSettingsSchema.extend({\r\n\topenRouterApiKey: z.string().optional(),\r\n\topenRouterModelId: z.string().optional(),\r\n\topenRouterBaseUrl: z.string().optional(),\r\n\topenRouterSpecificProvider: z.string().optional(),\r\n\topenRouterUseMiddleOutTransform: z.boolean().optional(),\r\n})\r\n\r\nconst bedrockSchema = apiModelIdProviderModelSchema.extend({\r\n\tawsAccessKey: z.string().optional(),\r\n\tawsSecretKey: z.string().optional(),\r\n\tawsSessionToken: z.string().optional(),\r\n\tawsRegion: z.string().optional(),\r\n\tawsUseCrossRegionInference: z.boolean().optional(),\r\n\tawsUsePromptCache: z.boolean().optional(),\r\n\tawsProfile: z.string().optional(),\r\n\tawsUseProfile: z.boolean().optional(),\r\n\tawsApiKey: z.string().optional(),\r\n\tawsUseApiKey: z.boolean().optional(),\r\n\tawsCustomArn: z.string().optional(),\r\n\tawsModelContextWindow: z.number().optional(),\r\n\tawsBedrockEndpointEnabled: z.boolean().optional(),\r\n\tawsBedrockEndpoint: z.string().optional(),\r\n\tawsBedrock1MContext: z.boolean().optional(), // Enable 'context-1m-2025-08-07' beta for 1M context window\r\n})\r\n\r\nconst vertexSchema = apiModelIdProviderModelSchema.extend({\r\n\tvertexKeyFile: z.string().optional(),\r\n\tvertexJsonCredentials: z.string().optional(),\r\n\tvertexProjectId: z.string().optional(),\r\n\tvertexRegion: z.string().optional(),\r\n\tenableUrlContext: z.boolean().optional(),\r\n\tenableGrounding: z.boolean().optional(),\r\n})\r\n\r\nconst openAiSchema = baseProviderSettingsSchema.extend({\r\n\topenAiBaseUrl: z.string().optional(),\r\n\topenAiApiKey: z.string().optional(),\r\n\topenAiLegacyFormat: z.boolean().optional(),\r\n\topenAiR1FormatEnabled: z.boolean().optional(),\r\n\topenAiModelId: z.string().optional(),\r\n\topenAiCustomModelInfo: modelInfoSchema.nullish(),\r\n\topenAiUseAzure: z.boolean().optional(),\r\n\tazureApiVersion: z.string().optional(),\r\n\topenAiStreamingEnabled: z.boolean().optional(),\r\n\topenAiHostHeader: z.string().optional(), // Keep temporarily for backward compatibility during migration.\r\n\topenAiHeaders: z.record(z.string(), z.string()).optional(),\r\n})\r\n\r\nconst ollamaSchema = baseProviderSettingsSchema.extend({\r\n\tollamaModelId: z.string().optional(),\r\n\tollamaBaseUrl: z.string().optional(),\r\n\tollamaApiKey: z.string().optional(),\r\n})\r\n\r\nconst vsCodeLmSchema = baseProviderSettingsSchema.extend({\r\n\tvsCodeLmModelSelector: z\r\n\t\t.object({\r\n\t\t\tvendor: z.string().optional(),\r\n\t\t\tfamily: z.string().optional(),\r\n\t\t\tversion: z.string().optional(),\r\n\t\t\tid: z.string().optional(),\r\n\t\t})\r\n\t\t.optional(),\r\n})\r\n\r\nconst lmStudioSchema = baseProviderSettingsSchema.extend({\r\n\tlmStudioModelId: z.string().optional(),\r\n\tlmStudioBaseUrl: z.string().optional(),\r\n\tlmStudioDraftModelId: z.string().optional(),\r\n\tlmStudioSpeculativeDecodingEnabled: z.boolean().optional(),\r\n})\r\n\r\nconst geminiSchema = apiModelIdProviderModelSchema.extend({\r\n\tgeminiApiKey: z.string().optional(),\r\n\tgoogleGeminiBaseUrl: z.string().optional(),\r\n\tenableUrlContext: z.boolean().optional(),\r\n\tenableGrounding: z.boolean().optional(),\r\n})\r\n\r\nconst geminiCliSchema = apiModelIdProviderModelSchema.extend({\r\n\tgeminiCliOAuthPath: z.string().optional(),\r\n\tgeminiCliProjectId: z.string().optional(),\r\n})\r\n\r\nconst openAiNativeSchema = apiModelIdProviderModelSchema.extend({\r\n\topenAiNativeApiKey: z.string().optional(),\r\n\topenAiNativeBaseUrl: z.string().optional(),\r\n\t// OpenAI Responses API service tier for openai-native provider only.\r\n\t// UI should only expose this when the selected model supports flex/priority.\r\n\topenAiNativeServiceTier: serviceTierSchema.optional(),\r\n})\r\n\r\nconst mistralSchema = apiModelIdProviderModelSchema.extend({\r\n\tmistralApiKey: z.string().optional(),\r\n\tmistralCodestralUrl: z.string().optional(),\r\n})\r\n\r\nconst deepSeekSchema = apiModelIdProviderModelSchema.extend({\r\n\tdeepSeekBaseUrl: z.string().optional(),\r\n\tdeepSeekApiKey: z.string().optional(),\r\n})\r\n\r\nconst deepInfraSchema = apiModelIdProviderModelSchema.extend({\r\n\tdeepInfraBaseUrl: z.string().optional(),\r\n\tdeepInfraApiKey: z.string().optional(),\r\n\tdeepInfraModelId: z.string().optional(),\r\n})\r\n\r\nconst doubaoSchema = apiModelIdProviderModelSchema.extend({\r\n\tdoubaoBaseUrl: z.string().optional(),\r\n\tdoubaoApiKey: z.string().optional(),\r\n})\r\n\r\nconst moonshotSchema = apiModelIdProviderModelSchema.extend({\r\n\tmoonshotBaseUrl: z\r\n\t\t.union([z.literal(\"https://api.moonshot.ai/v1\"), z.literal(\"https://api.moonshot.cn/v1\")])\r\n\t\t.optional(),\r\n\tmoonshotApiKey: z.string().optional(),\r\n})\r\n\r\nconst unboundSchema = baseProviderSettingsSchema.extend({\r\n\tunboundApiKey: z.string().optional(),\r\n\tunboundModelId: z.string().optional(),\r\n})\r\n\r\nconst requestySchema = baseProviderSettingsSchema.extend({\r\n\trequestyBaseUrl: z.string().optional(),\r\n\trequestyApiKey: z.string().optional(),\r\n\trequestyModelId: z.string().optional(),\r\n})\r\n\r\nconst humanRelaySchema = baseProviderSettingsSchema\r\n\r\nconst fakeAiSchema = baseProviderSettingsSchema.extend({\r\n\tfakeAi: z.unknown().optional(),\r\n})\r\n\r\nconst xaiSchema = apiModelIdProviderModelSchema.extend({\r\n\txaiApiKey: z.string().optional(),\r\n})\r\n\r\nconst groqSchema = apiModelIdProviderModelSchema.extend({\r\n\tgroqApiKey: z.string().optional(),\r\n})\r\n\r\nconst huggingFaceSchema = baseProviderSettingsSchema.extend({\r\n\thuggingFaceApiKey: z.string().optional(),\r\n\thuggingFaceModelId: z.string().optional(),\r\n\thuggingFaceInferenceProvider: z.string().optional(),\r\n})\r\n\r\nconst chutesSchema = apiModelIdProviderModelSchema.extend({\r\n\tchutesApiKey: z.string().optional(),\r\n})\r\n\r\nconst litellmSchema = baseProviderSettingsSchema.extend({\r\n\tlitellmBaseUrl: z.string().optional(),\r\n\tlitellmApiKey: z.string().optional(),\r\n\tlitellmModelId: z.string().optional(),\r\n\tlitellmUsePromptCache: z.boolean().optional(),\r\n})\r\n\r\nconst cerebrasSchema = apiModelIdProviderModelSchema.extend({\r\n\tcerebrasApiKey: z.string().optional(),\r\n})\r\n\r\nconst sambaNovaSchema = apiModelIdProviderModelSchema.extend({\r\n\tsambaNovaApiKey: z.string().optional(),\r\n})\r\n\r\nconst zaiSchema = apiModelIdProviderModelSchema.extend({\r\n\tzaiApiKey: z.string().optional(),\r\n\tzaiApiLine: z.union([z.literal(\"china\"), z.literal(\"international\")]).optional(),\r\n})\r\n\r\nconst fireworksSchema = apiModelIdProviderModelSchema.extend({\r\n\tfireworksApiKey: z.string().optional(),\r\n})\r\n\r\nconst featherlessSchema = apiModelIdProviderModelSchema.extend({\r\n\tfeatherlessApiKey: z.string().optional(),\r\n})\r\n\r\nconst ioIntelligenceSchema = apiModelIdProviderModelSchema.extend({\r\n\tioIntelligenceModelId: z.string().optional(),\r\n\tioIntelligenceApiKey: z.string().optional(),\r\n})\r\n\r\nconst qwenCodeSchema = apiModelIdProviderModelSchema.extend({\r\n\tqwenCodeOauthPath: z.string().optional(),\r\n})\r\n\r\nconst rooSchema = apiModelIdProviderModelSchema.extend({\r\n\t// No additional fields needed - uses cloud authentication\r\n})\r\n\r\nconst vercelAiGatewaySchema = baseProviderSettingsSchema.extend({\r\n\tvercelAiGatewayApiKey: z.string().optional(),\r\n\tvercelAiGatewayModelId: z.string().optional(),\r\n})\r\n\r\nconst defaultSchema = z.object({\r\n\tapiProvider: z.undefined(),\r\n})\r\n\r\nexport const providerSettingsSchemaDiscriminated = z.discriminatedUnion(\"apiProvider\", [\r\n\tanthropicSchema.merge(z.object({ apiProvider: z.literal(\"anthropic\") })),\r\n\tclaudeCodeSchema.merge(z.object({ apiProvider: z.literal(\"claude-code\") })),\r\n\tglamaSchema.merge(z.object({ apiProvider: z.literal(\"glama\") })),\r\n\topenRouterSchema.merge(z.object({ apiProvider: z.literal(\"openrouter\") })),\r\n\tbedrockSchema.merge(z.object({ apiProvider: z.literal(\"bedrock\") })),\r\n\tvertexSchema.merge(z.object({ apiProvider: z.literal(\"vertex\") })),\r\n\topenAiSchema.merge(z.object({ apiProvider: z.literal(\"openai\") })),\r\n\tollamaSchema.merge(z.object({ apiProvider: z.literal(\"ollama\") })),\r\n\tvsCodeLmSchema.merge(z.object({ apiProvider: z.literal(\"vscode-lm\") })),\r\n\tlmStudioSchema.merge(z.object({ apiProvider: z.literal(\"lmstudio\") })),\r\n\tgeminiSchema.merge(z.object({ apiProvider: z.literal(\"gemini\") })),\r\n\tgeminiCliSchema.merge(z.object({ apiProvider: z.literal(\"gemini-cli\") })),\r\n\topenAiNativeSchema.merge(z.object({ apiProvider: z.literal(\"openai-native\") })),\r\n\tmistralSchema.merge(z.object({ apiProvider: z.literal(\"mistral\") })),\r\n\tdeepSeekSchema.merge(z.object({ apiProvider: z.literal(\"deepseek\") })),\r\n\tdeepInfraSchema.merge(z.object({ apiProvider: z.literal(\"deepinfra\") })),\r\n\tdoubaoSchema.merge(z.object({ apiProvider: z.literal(\"doubao\") })),\r\n\tmoonshotSchema.merge(z.object({ apiProvider: z.literal(\"moonshot\") })),\r\n\tunboundSchema.merge(z.object({ apiProvider: z.literal(\"unbound\") })),\r\n\trequestySchema.merge(z.object({ apiProvider: z.literal(\"requesty\") })),\r\n\thumanRelaySchema.merge(z.object({ apiProvider: z.literal(\"human-relay\") })),\r\n\tfakeAiSchema.merge(z.object({ apiProvider: z.literal(\"fake-ai\") })),\r\n\txaiSchema.merge(z.object({ apiProvider: z.literal(\"xai\") })),\r\n\tgroqSchema.merge(z.object({ apiProvider: z.literal(\"groq\") })),\r\n\thuggingFaceSchema.merge(z.object({ apiProvider: z.literal(\"huggingface\") })),\r\n\tchutesSchema.merge(z.object({ apiProvider: z.literal(\"chutes\") })),\r\n\tlitellmSchema.merge(z.object({ apiProvider: z.literal(\"litellm\") })),\r\n\tcerebrasSchema.merge(z.object({ apiProvider: z.literal(\"cerebras\") })),\r\n\tsambaNovaSchema.merge(z.object({ apiProvider: z.literal(\"sambanova\") })),\r\n\tzaiSchema.merge(z.object({ apiProvider: z.literal(\"zai\") })),\r\n\tfireworksSchema.merge(z.object({ apiProvider: z.literal(\"fireworks\") })),\r\n\tfeatherlessSchema.merge(z.object({ apiProvider: z.literal(\"featherless\") })),\r\n\tioIntelligenceSchema.merge(z.object({ apiProvider: z.literal(\"io-intelligence\") })),\r\n\tqwenCodeSchema.merge(z.object({ apiProvider: z.literal(\"qwen-code\") })),\r\n\trooSchema.merge(z.object({ apiProvider: z.literal(\"roo\") })),\r\n\tvercelAiGatewaySchema.merge(z.object({ apiProvider: z.literal(\"vercel-ai-gateway\") })),\r\n\tdefaultSchema,\r\n])\r\n\r\nexport const providerSettingsSchema = z.object({\r\n\tapiProvider: providerNamesSchema.optional(),\r\n\t...anthropicSchema.shape,\r\n\t...claudeCodeSchema.shape,\r\n\t...glamaSchema.shape,\r\n\t...openRouterSchema.shape,\r\n\t...bedrockSchema.shape,\r\n\t...vertexSchema.shape,\r\n\t...openAiSchema.shape,\r\n\t...ollamaSchema.shape,\r\n\t...vsCodeLmSchema.shape,\r\n\t...lmStudioSchema.shape,\r\n\t...geminiSchema.shape,\r\n\t...geminiCliSchema.shape,\r\n\t...openAiNativeSchema.shape,\r\n\t...mistralSchema.shape,\r\n\t...deepSeekSchema.shape,\r\n\t...deepInfraSchema.shape,\r\n\t...doubaoSchema.shape,\r\n\t...moonshotSchema.shape,\r\n\t...unboundSchema.shape,\r\n\t...requestySchema.shape,\r\n\t...humanRelaySchema.shape,\r\n\t...fakeAiSchema.shape,\r\n\t...xaiSchema.shape,\r\n\t...groqSchema.shape,\r\n\t...huggingFaceSchema.shape,\r\n\t...chutesSchema.shape,\r\n\t...litellmSchema.shape,\r\n\t...cerebrasSchema.shape,\r\n\t...sambaNovaSchema.shape,\r\n\t...zaiSchema.shape,\r\n\t...fireworksSchema.shape,\r\n\t...featherlessSchema.shape,\r\n\t...ioIntelligenceSchema.shape,\r\n\t...qwenCodeSchema.shape,\r\n\t...rooSchema.shape,\r\n\t...vercelAiGatewaySchema.shape,\r\n\t...codebaseIndexProviderSchema.shape,\r\n})\r\n\r\nexport type ProviderSettings = z.infer<typeof providerSettingsSchema>\r\n\r\nexport const providerSettingsWithIdSchema = providerSettingsSchema.extend({ id: z.string().optional() })\r\n\r\nexport const discriminatedProviderSettingsWithIdSchema = providerSettingsSchemaDiscriminated.and(\r\n\tz.object({ id: z.string().optional() }),\r\n)\r\n\r\nexport type ProviderSettingsWithId = z.infer<typeof providerSettingsWithIdSchema>\r\n\r\nexport const PROVIDER_SETTINGS_KEYS = providerSettingsSchema.keyof().options\r\n\r\nexport const MODEL_ID_KEYS: Partial<keyof ProviderSettings>[] = [\r\n\t\"apiModelId\",\r\n\t\"glamaModelId\",\r\n\t\"openRouterModelId\",\r\n\t\"openAiModelId\",\r\n\t\"ollamaModelId\",\r\n\t\"lmStudioModelId\",\r\n\t\"lmStudioDraftModelId\",\r\n\t\"unboundModelId\",\r\n\t\"requestyModelId\",\r\n\t\"litellmModelId\",\r\n\t\"huggingFaceModelId\",\r\n\t\"ioIntelligenceModelId\",\r\n\t\"vercelAiGatewayModelId\",\r\n\t\"deepInfraModelId\",\r\n]\r\n\r\nexport const getModelId = (settings: ProviderSettings): string | undefined => {\r\n\tconst modelIdKey = MODEL_ID_KEYS.find((key) => settings[key])\r\n\treturn modelIdKey ? (settings[modelIdKey] as string) : undefined\r\n}\r\n\r\n// Providers that use Anthropic-style API protocol.\r\nexport const ANTHROPIC_STYLE_PROVIDERS: ProviderName[] = [\"anthropic\", \"claude-code\", \"bedrock\"]\r\n\r\nexport const getApiProtocol = (provider: ProviderName | undefined, modelId?: string): \"anthropic\" | \"openai\" => {\r\n\tif (provider && ANTHROPIC_STYLE_PROVIDERS.includes(provider)) {\r\n\t\treturn \"anthropic\"\r\n\t}\r\n\r\n\tif (provider && provider === \"vertex\" && modelId && modelId.toLowerCase().includes(\"claude\")) {\r\n\t\treturn \"anthropic\"\r\n\t}\r\n\r\n\t// Vercel AI Gateway uses anthropic protocol for anthropic models.\r\n\tif (provider && provider === \"vercel-ai-gateway\" && modelId && modelId.toLowerCase().startsWith(\"anthropic/\")) {\r\n\t\treturn \"anthropic\"\r\n\t}\r\n\r\n\treturn \"openai\"\r\n}\r\n\r\nexport const MODELS_BY_PROVIDER: Record<\r\n\tExclude<ProviderName, \"fake-ai\" | \"human-relay\" | \"lmstudio\" | \"openai\" | \"ollama\">,\r\n\t{ id: ProviderName; label: string; models: string[] }\r\n> = {\r\n\tanthropic: {\r\n\t\tid: \"anthropic\",\r\n\t\tlabel: \"Anthropic\",\r\n\t\tmodels: Object.keys(anthropicModels),\r\n\t},\r\n\tbedrock: {\r\n\t\tid: \"bedrock\",\r\n\t\tlabel: \"Amazon Bedrock\",\r\n\t\tmodels: Object.keys(bedrockModels),\r\n\t},\r\n\tcerebras: {\r\n\t\tid: \"cerebras\",\r\n\t\tlabel: \"Cerebras\",\r\n\t\tmodels: Object.keys(cerebrasModels),\r\n\t},\r\n\tchutes: {\r\n\t\tid: \"chutes\",\r\n\t\tlabel: \"Chutes AI\",\r\n\t\tmodels: Object.keys(chutesModels),\r\n\t},\r\n\t\"claude-code\": { id: \"claude-code\", label: \"Claude Code\", models: Object.keys(claudeCodeModels) },\r\n\tdeepseek: {\r\n\t\tid: \"deepseek\",\r\n\t\tlabel: \"DeepSeek\",\r\n\t\tmodels: Object.keys(deepSeekModels),\r\n\t},\r\n\tdoubao: { id: \"doubao\", label: \"Doubao\", models: Object.keys(doubaoModels) },\r\n\tfeatherless: {\r\n\t\tid: \"featherless\",\r\n\t\tlabel: \"Featherless\",\r\n\t\tmodels: Object.keys(featherlessModels),\r\n\t},\r\n\tfireworks: {\r\n\t\tid: \"fireworks\",\r\n\t\tlabel: \"Fireworks\",\r\n\t\tmodels: Object.keys(fireworksModels),\r\n\t},\r\n\tgemini: {\r\n\t\tid: \"gemini\",\r\n\t\tlabel: \"Google Gemini\",\r\n\t\tmodels: Object.keys(geminiModels),\r\n\t},\r\n\t\"gemini-cli\": {\r\n\t\tid: \"gemini-cli\",\r\n\t\tlabel: \"Google Gemini CLI\",\r\n\t\tmodels: Object.keys(geminiCliModels),\r\n\t},\r\n\tgroq: { id: \"groq\", label: \"Groq\", models: Object.keys(groqModels) },\r\n\t\"io-intelligence\": {\r\n\t\tid: \"io-intelligence\",\r\n\t\tlabel: \"IO Intelligence\",\r\n\t\tmodels: Object.keys(ioIntelligenceModels),\r\n\t},\r\n\tmistral: {\r\n\t\tid: \"mistral\",\r\n\t\tlabel: \"Mistral\",\r\n\t\tmodels: Object.keys(mistralModels),\r\n\t},\r\n\tmoonshot: {\r\n\t\tid: \"moonshot\",\r\n\t\tlabel: \"Moonshot\",\r\n\t\tmodels: Object.keys(moonshotModels),\r\n\t},\r\n\t\"openai-native\": {\r\n\t\tid: \"openai-native\",\r\n\t\tlabel: \"OpenAI\",\r\n\t\tmodels: Object.keys(openAiNativeModels),\r\n\t},\r\n\t\"qwen-code\": { id: \"qwen-code\", label: \"Qwen Code\", models: Object.keys(qwenCodeModels) },\r\n\troo: { id: \"roo\", label: \"Roo\", models: Object.keys(rooModels) },\r\n\tsambanova: {\r\n\t\tid: \"sambanova\",\r\n\t\tlabel: \"SambaNova\",\r\n\t\tmodels: Object.keys(sambaNovaModels),\r\n\t},\r\n\tvertex: {\r\n\t\tid: \"vertex\",\r\n\t\tlabel: \"GCP Vertex AI\",\r\n\t\tmodels: Object.keys(vertexModels),\r\n\t},\r\n\t\"vscode-lm\": {\r\n\t\tid: \"vscode-lm\",\r\n\t\tlabel: \"VS Code LM API\",\r\n\t\tmodels: Object.keys(vscodeLlmModels),\r\n\t},\r\n\txai: { id: \"xai\", label: \"xAI (Grok)\", models: Object.keys(xaiModels) },\r\n\tzai: { id: \"zai\", label: \"Zai\", models: Object.keys(internationalZAiModels) },\r\n\r\n\t// Dynamic providers; models pulled from the respective APIs.\r\n\tglama: { id: \"glama\", label: \"Glama\", models: [] },\r\n\thuggingface: { id: \"huggingface\", label: \"Hugging Face\", models: [] },\r\n\tlitellm: { id: \"litellm\", label: \"LiteLLM\", models: [] },\r\n\topenrouter: { id: \"openrouter\", label: \"OpenRouter\", models: [] },\r\n\trequesty: { id: \"requesty\", label: \"Requesty\", models: [] },\r\n\tunbound: { id: \"unbound\", label: \"Unbound\", models: [] },\r\n\tdeepinfra: { id: \"deepinfra\", label: \"DeepInfra\", models: [] },\r\n\t\"vercel-ai-gateway\": { id: \"vercel-ai-gateway\", label: \"Vercel AI Gateway\", models: [] },\r\n}\r\n\r\nexport const dynamicProviders = [\r\n\t\"glama\",\r\n\t\"huggingface\",\r\n\t\"litellm\",\r\n\t\"openrouter\",\r\n\t\"requesty\",\r\n\t\"unbound\",\r\n\t\"deepinfra\",\r\n\t\"vercel-ai-gateway\",\r\n] as const satisfies readonly ProviderName[]\r\n\r\nexport type DynamicProvider = (typeof dynamicProviders)[number]\r\n\r\nexport const isDynamicProvider = (key: string): key is DynamicProvider =>\r\n\tdynamicProviders.includes(key as DynamicProvider)\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ReasoningEffort\r\n */\r\n\r\nexport const reasoningEfforts = [\"low\", \"medium\", \"high\"] as const\r\n\r\nexport const reasoningEffortsSchema = z.enum(reasoningEfforts)\r\n\r\nexport type ReasoningEffort = z.infer<typeof reasoningEffortsSchema>\r\n\r\n/**\r\n * ReasoningEffortWithMinimal\r\n */\r\n\r\nexport const reasoningEffortWithMinimalSchema = z.union([reasoningEffortsSchema, z.literal(\"minimal\")])\r\n\r\nexport type ReasoningEffortWithMinimal = z.infer<typeof reasoningEffortWithMinimalSchema>\r\n\r\n/**\r\n * Verbosity\r\n */\r\n\r\nexport const verbosityLevels = [\"low\", \"medium\", \"high\"] as const\r\n\r\nexport const verbosityLevelsSchema = z.enum(verbosityLevels)\r\n\r\nexport type VerbosityLevel = z.infer<typeof verbosityLevelsSchema>\r\n\r\n/**\r\n * Service tiers (OpenAI Responses API)\r\n */\r\nexport const serviceTiers = [\"default\", \"flex\", \"priority\"] as const\r\nexport const serviceTierSchema = z.enum(serviceTiers)\r\nexport type ServiceTier = z.infer<typeof serviceTierSchema>\r\n\r\n/**\r\n * ModelParameter\r\n */\r\n\r\nexport const modelParameters = [\"max_tokens\", \"temperature\", \"reasoning\", \"include_reasoning\"] as const\r\n\r\nexport const modelParametersSchema = z.enum(modelParameters)\r\n\r\nexport type ModelParameter = z.infer<typeof modelParametersSchema>\r\n\r\nexport const isModelParameter = (value: string): value is ModelParameter =>\r\n\tmodelParameters.includes(value as ModelParameter)\r\n\r\n/**\r\n * ModelInfo\r\n */\r\n\r\nexport const modelInfoSchema = z.object({\r\n\tmaxTokens: z.number().nullish(),\r\n\tmaxThinkingTokens: z.number().nullish(),\r\n\tcontextWindow: z.number(),\r\n\tsupportsImages: z.boolean().optional(),\r\n\tsupportsComputerUse: z.boolean().optional(),\r\n\tsupportsPromptCache: z.boolean(),\r\n\t// Capability flag to indicate whether the model supports an output verbosity parameter\r\n\tsupportsVerbosity: z.boolean().optional(),\r\n\tsupportsReasoningBudget: z.boolean().optional(),\r\n\t// Capability flag to indicate whether the model supports temperature parameter\r\n\tsupportsTemperature: z.boolean().optional(),\r\n\trequiredReasoningBudget: z.boolean().optional(),\r\n\tsupportsReasoningEffort: z.boolean().optional(),\r\n\tsupportedParameters: z.array(modelParametersSchema).optional(),\r\n\tinputPrice: z.number().optional(),\r\n\toutputPrice: z.number().optional(),\r\n\tcacheWritesPrice: z.number().optional(),\r\n\tcacheReadsPrice: z.number().optional(),\r\n\tdescription: z.string().optional(),\r\n\treasoningEffort: reasoningEffortsSchema.optional(),\r\n\tminTokensPerCachePoint: z.number().optional(),\r\n\tmaxCachePoints: z.number().optional(),\r\n\tcachableFields: z.array(z.string()).optional(),\r\n\t/**\r\n\t * Service tiers with pricing information.\r\n\t * Each tier can have a name (for OpenAI service tiers) and pricing overrides.\r\n\t * The top-level input/output/cache* fields represent the default/standard tier.\r\n\t */\r\n\ttiers: z\r\n\t\t.array(\r\n\t\t\tz.object({\r\n\t\t\t\tname: serviceTierSchema.optional(), // Service tier name (flex, priority, etc.)\r\n\t\t\t\tcontextWindow: z.number(),\r\n\t\t\t\tinputPrice: z.number().optional(),\r\n\t\t\t\toutputPrice: z.number().optional(),\r\n\t\t\t\tcacheWritesPrice: z.number().optional(),\r\n\t\t\t\tcacheReadsPrice: z.number().optional(),\r\n\t\t\t}),\r\n\t\t)\r\n\t\t.optional(),\r\n})\r\n\r\nexport type ModelInfo = z.infer<typeof modelInfoSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * Codebase Index Constants\r\n */\r\nexport const CODEBASE_INDEX_DEFAULTS = {\r\n\tMIN_SEARCH_RESULTS: 10,\r\n\tMAX_SEARCH_RESULTS: 200,\r\n\tDEFAULT_SEARCH_RESULTS: 50,\r\n\t<PERSON>ARCH_RESULTS_STEP: 10,\r\n\tMIN_SEARCH_SCORE: 0,\r\n\tMAX_SEARCH_SCORE: 1,\r\n\tDEFAULT_SEARCH_MIN_SCORE: 0.4,\r\n\tSEARCH_SCORE_STEP: 0.05,\r\n} as const\r\n\r\n/**\r\n * CodebaseIndexConfig\r\n */\r\n\r\nexport const codebaseIndexConfigSchema = z.object({\r\n\tcodebaseIndexEnabled: z.boolean().optional(),\r\n\tcodebaseIndexQdrantUrl: z.string().optional(),\r\n\tcodebaseIndexEmbedderProvider: z\r\n\t\t.enum([\"openai\", \"ollama\", \"openai-compatible\", \"gemini\", \"mistral\", \"vercel-ai-gateway\"])\r\n\t\t.optional(),\r\n\tcodebaseIndexEmbedderBaseUrl: z.string().optional(),\r\n\tcodebaseIndexEmbedderModelId: z.string().optional(),\r\n\tcodebaseIndexEmbedderModelDimension: z.number().optional(),\r\n\tcodebaseIndexSearchMinScore: z.number().min(0).max(1).optional(),\r\n\tcodebaseIndexSearchMaxResults: z\r\n\t\t.number()\r\n\t\t.min(CODEBASE_INDEX_DEFAULTS.MIN_SEARCH_RESULTS)\r\n\t\t.max(CODEBASE_INDEX_DEFAULTS.MAX_SEARCH_RESULTS)\r\n\t\t.optional(),\r\n\t// OpenAI Compatible specific fields\r\n\tcodebaseIndexOpenAiCompatibleBaseUrl: z.string().optional(),\r\n\tcodebaseIndexOpenAiCompatibleModelDimension: z.number().optional(),\r\n})\r\n\r\nexport type CodebaseIndexConfig = z.infer<typeof codebaseIndexConfigSchema>\r\n\r\n/**\r\n * CodebaseIndexModels\r\n */\r\n\r\nexport const codebaseIndexModelsSchema = z.object({\r\n\topenai: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\tollama: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\t\"openai-compatible\": z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\tgemini: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\tmistral: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\t\"vercel-ai-gateway\": z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n})\r\n\r\nexport type CodebaseIndexModels = z.infer<typeof codebaseIndexModelsSchema>\r\n\r\n/**\r\n * CdebaseIndexProvider\r\n */\r\n\r\nexport const codebaseIndexProviderSchema = z.object({\r\n\tcodeIndexOpenAiKey: z.string().optional(),\r\n\tcodeIndexQdrantApiKey: z.string().optional(),\r\n\tcodebaseIndexOpenAiCompatibleBaseUrl: z.string().optional(),\r\n\tcodebaseIndexOpenAiCompatibleApiKey: z.string().optional(),\r\n\tcodebaseIndexOpenAiCompatibleModelDimension: z.number().optional(),\r\n\tcodebaseIndexGeminiApiKey: z.string().optional(),\r\n\tcodebaseIndexMistralApiKey: z.string().optional(),\r\n\tcodebaseIndexVercelAiGatewayApiKey: z.string().optional(),\r\n})\r\n\r\nexport type CodebaseIndexProvider = z.infer<typeof codebaseIndexProviderSchema>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.anthropic.com/en/docs/about-claude/models\r\n\r\nexport type AnthropicModelId = keyof typeof anthropicModels\r\nexport const anthropicDefaultModelId: AnthropicModelId = \"claude-sonnet-4-20250514\"\r\n\r\nexport const anthropicModels = {\r\n\t\"claude-sonnet-4-20250514\": {\r\n\t\tmaxTokens: 64_000, // Overridden to 8k if `enableReasoningEffort` is false.\r\n\t\tcontextWindow: 200_000, // Default 200K, extendable to 1M with beta flag 'context-1m-2025-08-07'\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens (≤200K context)\r\n\t\toutputPrice: 15.0, // $15 per million output tokens (≤200K context)\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t\t// Tiered pricing for extended context (requires beta flag 'context-1m-2025-08-07')\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 1_000_000, // 1M tokens with beta flag\r\n\t\t\t\tinputPrice: 6.0, // $6 per million input tokens (>200K context)\r\n\t\t\t\toutputPrice: 22.5, // $22.50 per million output tokens (>200K context)\r\n\t\t\t\tcacheWritesPrice: 7.5, // $7.50 per million tokens (>200K context)\r\n\t\t\t\tcacheReadsPrice: 0.6, // $0.60 per million tokens (>200K context)\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"claude-opus-4-1-20250805\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0, // $15 per million input tokens\r\n\t\toutputPrice: 75.0, // $75 per million output tokens\r\n\t\tcacheWritesPrice: 18.75, // $18.75 per million tokens\r\n\t\tcacheReadsPrice: 1.5, // $1.50 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-opus-4-20250514\": {\r\n\t\tmaxTokens: 32_000, // Overridden to 8k if `enableReasoningEffort` is false.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0, // $15 per million input tokens\r\n\t\toutputPrice: 75.0, // $75 per million output tokens\r\n\t\tcacheWritesPrice: 18.75, // $18.75 per million tokens\r\n\t\tcacheReadsPrice: 1.5, // $1.50 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet-20250219:thinking\": {\r\n\t\tmaxTokens: 128_000, // Unlocked by passing `beta` flag to the model. Otherwise, it's 64k.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet-20250219\": {\r\n\t\tmaxTokens: 8192, // Since we already have a `:thinking` virtual model we aren't setting `supportsReasoningBudget: true` here.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t},\r\n\t\"claude-3-5-sonnet-20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t},\r\n\t\"claude-3-5-haiku-20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 5.0,\r\n\t\tcacheWritesPrice: 1.25,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t},\r\n\t\"claude-3-opus-20240229\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-haiku-20240307\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t\tcacheWritesPrice: 0.3,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const ANTHROPIC_DEFAULT_MAX_TOKENS = 8192\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html\r\n\r\nexport type BedrockModelId = keyof typeof bedrockModels\r\n\r\nexport const bedrockDefaultModelId: BedrockModelId = \"anthropic.claude-sonnet-4-20250514-v1:0\"\r\n\r\nexport const bedrockDefaultPromptRouterModelId: BedrockModelId = \"anthropic.claude-3-sonnet-20240229-v1:0\"\r\n\r\n// March, 12 2025 - updated prices to match US-West-2 list price shown at\r\n// https://aws.amazon.com/bedrock/pricing, including older models that are part\r\n// of the default prompt routers AWS enabled for GA of the promot router\r\n// feature.\r\nexport const bedrockModels = {\r\n\t\"amazon.nova-pro-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 3.2,\r\n\t\tcacheWritesPrice: 0.8, // per million tokens\r\n\t\tcacheReadsPrice: 0.2, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"amazon.nova-pro-latency-optimized-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 4.0,\r\n\t\tcacheWritesPrice: 1.0, // per million tokens\r\n\t\tcacheReadsPrice: 0.25, // per million tokens\r\n\t\tdescription: \"Amazon Nova Pro with latency optimized inference\",\r\n\t},\r\n\t\"amazon.nova-lite-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.06,\r\n\t\toutputPrice: 0.24,\r\n\t\tcacheWritesPrice: 0.06, // per million tokens\r\n\t\tcacheReadsPrice: 0.015, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"amazon.nova-micro-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.035,\r\n\t\toutputPrice: 0.14,\r\n\t\tcacheWritesPrice: 0.035, // per million tokens\r\n\t\tcacheReadsPrice: 0.00875, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"anthropic.claude-sonnet-4-20250514-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-opus-4-1-20250805-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-opus-4-20250514-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-7-sonnet-20250219-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-sonnet-20241022-v2:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-haiku-20241022-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 4.0,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tcacheReadsPrice: 0.08,\r\n\t\tminTokensPerCachePoint: 2048,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-sonnet-20240620-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t},\r\n\t\"anthropic.claude-3-opus-20240229-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t},\r\n\t\"anthropic.claude-3-sonnet-20240229-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t},\r\n\t\"anthropic.claude-3-haiku-20240307-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t},\r\n\t\"anthropic.claude-2-1-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 8.0,\r\n\t\toutputPrice: 24.0,\r\n\t\tdescription: \"Claude 2.1\",\r\n\t},\r\n\t\"anthropic.claude-2-0-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 8.0,\r\n\t\toutputPrice: 24.0,\r\n\t\tdescription: \"Claude 2.0\",\r\n\t},\r\n\t\"anthropic.claude-instant-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 2.4,\r\n\t\tdescription: \"Claude Instant\",\r\n\t},\r\n\t\"deepseek.r1-v1:0\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.35,\r\n\t\toutputPrice: 5.4,\r\n\t},\r\n\t\"openai.gpt-oss-20b-1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.5,\r\n\t\toutputPrice: 1.5,\r\n\t\tdescription: \"GPT-OSS 20B - Optimized for low latency and local/specialized use cases\",\r\n\t},\r\n\t\"openai.gpt-oss-120b-1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 6.0,\r\n\t\tdescription: \"GPT-OSS 120B - Production-ready, general-purpose, high-reasoning model\",\r\n\t},\r\n\t\"meta.llama3-3-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.3 Instruct (70B)\",\r\n\t},\r\n\t\"meta.llama3-2-90b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.2 Instruct (90B)\",\r\n\t},\r\n\t\"meta.llama3-2-11b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.16,\r\n\t\toutputPrice: 0.16,\r\n\t\tdescription: \"Llama 3.2 Instruct (11B)\",\r\n\t},\r\n\t\"meta.llama3-2-3b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.15,\r\n\t\tdescription: \"Llama 3.2 Instruct (3B)\",\r\n\t},\r\n\t\"meta.llama3-2-1b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.1,\r\n\t\tdescription: \"Llama 3.2 Instruct (1B)\",\r\n\t},\r\n\t\"meta.llama3-1-405b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.4,\r\n\t\toutputPrice: 2.4,\r\n\t\tdescription: \"Llama 3.1 Instruct (405B)\",\r\n\t},\r\n\t\"meta.llama3-1-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.1 Instruct (70B)\",\r\n\t},\r\n\t\"meta.llama3-1-70b-instruct-latency-optimized-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.9,\r\n\t\toutputPrice: 0.9,\r\n\t\tdescription: \"Llama 3.1 Instruct (70B) (w/ latency optimized inference)\",\r\n\t},\r\n\t\"meta.llama3-1-8b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.22,\r\n\t\toutputPrice: 0.22,\r\n\t\tdescription: \"Llama 3.1 Instruct (8B)\",\r\n\t},\r\n\t\"meta.llama3-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 2048,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.65,\r\n\t\toutputPrice: 3.5,\r\n\t},\r\n\t\"meta.llama3-8b-instruct-v1:0\": {\r\n\t\tmaxTokens: 2048,\r\n\t\tcontextWindow: 4_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"amazon.titan-text-lite-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.2,\r\n\t\tdescription: \"Amazon Titan Text Lite\",\r\n\t},\r\n\t\"amazon.titan-text-express-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 0.6,\r\n\t\tdescription: \"Amazon Titan Text Express\",\r\n\t},\r\n\t\"amazon.titan-text-embeddings-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\tdescription: \"Amazon Titan Text Embeddings\",\r\n\t},\r\n\t\"amazon.titan-text-embeddings-v2:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.02,\r\n\t\tdescription: \"Amazon Titan Text Embeddings V2\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const BEDROCK_DEFAULT_TEMPERATURE = 0.3\r\n\r\nexport const BEDROCK_MAX_TOKENS = 4096\r\n\r\nexport const BEDROCK_DEFAULT_CONTEXT = 128_000\r\n\r\n// Amazon Bedrock Inference Profile mapping based on official documentation\r\n// https://docs.aws.amazon.com/bedrock/latest/userguide/inference-profiles-support.html\r\n// This mapping is pre-ordered by pattern length (descending) to ensure more specific patterns match first\r\nexport const AWS_INFERENCE_PROFILE_MAPPING: Array<[string, string]> = [\r\n\t// US Government Cloud → ug. inference profile (most specific prefix first)\r\n\t[\"us-gov-\", \"ug.\"],\r\n\t// Americas regions → us. inference profile\r\n\t[\"us-\", \"us.\"],\r\n\t// Europe regions → eu. inference profile\r\n\t[\"eu-\", \"eu.\"],\r\n\t// Asia Pacific regions → apac. inference profile\r\n\t[\"ap-\", \"apac.\"],\r\n\t// Canada regions → ca. inference profile\r\n\t[\"ca-\", \"ca.\"],\r\n\t// South America regions → sa. inference profile\r\n\t[\"sa-\", \"sa.\"],\r\n]\r\n\r\n// Amazon Bedrock supported regions for the regions dropdown\r\n// Based on official AWS documentation\r\nexport const BEDROCK_REGIONS = [\r\n\t{ value: \"us-east-1\", label: \"us-east-1\" },\r\n\t{ value: \"us-east-2\", label: \"us-east-2\" },\r\n\t{ value: \"us-west-1\", label: \"us-west-1\" },\r\n\t{ value: \"us-west-2\", label: \"us-west-2\" },\r\n\t{ value: \"ap-northeast-1\", label: \"ap-northeast-1\" },\r\n\t{ value: \"ap-northeast-2\", label: \"ap-northeast-2\" },\r\n\t{ value: \"ap-northeast-3\", label: \"ap-northeast-3\" },\r\n\t{ value: \"ap-south-1\", label: \"ap-south-1\" },\r\n\t{ value: \"ap-south-2\", label: \"ap-south-2\" },\r\n\t{ value: \"ap-southeast-1\", label: \"ap-southeast-1\" },\r\n\t{ value: \"ap-southeast-2\", label: \"ap-southeast-2\" },\r\n\t{ value: \"ap-east-1\", label: \"ap-east-1\" },\r\n\t{ value: \"eu-central-1\", label: \"eu-central-1\" },\r\n\t{ value: \"eu-central-2\", label: \"eu-central-2\" },\r\n\t{ value: \"eu-west-1\", label: \"eu-west-1\" },\r\n\t{ value: \"eu-west-2\", label: \"eu-west-2\" },\r\n\t{ value: \"eu-west-3\", label: \"eu-west-3\" },\r\n\t{ value: \"eu-north-1\", label: \"eu-north-1\" },\r\n\t{ value: \"eu-south-1\", label: \"eu-south-1\" },\r\n\t{ value: \"eu-south-2\", label: \"eu-south-2\" },\r\n\t{ value: \"ca-central-1\", label: \"ca-central-1\" },\r\n\t{ value: \"sa-east-1\", label: \"sa-east-1\" },\r\n\t{ value: \"us-gov-east-1\", label: \"us-gov-east-1\" },\r\n\t{ value: \"us-gov-west-1\", label: \"us-gov-west-1\" },\r\n].sort((a, b) => a.value.localeCompare(b.value))\r\n\r\nexport const BEDROCK_CLAUDE_SONNET_4_MODEL_ID = \"anthropic.claude-sonnet-4-20250514-v1:0\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://inference-docs.cerebras.ai/api-reference/chat-completions\r\nexport type CerebrasModelId = keyof typeof cerebrasModels\r\n\r\nexport const cerebrasDefaultModelId: CerebrasModelId = \"qwen-3-coder-480b-free\"\r\n\r\nexport const cerebrasModels = {\r\n\t\"qwen-3-coder-480b-free\": {\r\n\t\tmaxTokens: 40000,\r\n\t\tcontextWindow: 64000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"SOTA coding model with ~2000 tokens/s ($0 free tier)\\n\\n• Use this if you don't have a Cerebras subscription\\n• 64K context window\\n• Rate limits: 150K TPM, 1M TPH/TPD, 10 RPM, 100 RPH/RPD\\n\\nUpgrade for higher limits: [https://cloud.cerebras.ai/?utm=roocode](https://cloud.cerebras.ai/?utm=roocode)\",\r\n\t},\r\n\t\"qwen-3-coder-480b\": {\r\n\t\tmaxTokens: 40000,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"SOTA coding model with ~2000 tokens/s ($50/$250 paid tiers)\\n\\n• Use this if you have a Cerebras subscription\\n• 131K context window with higher rate limits\",\r\n\t},\r\n\t\"qwen-3-235b-a22b-instruct-2507\": {\r\n\t\tmaxTokens: 64000,\r\n\t\tcontextWindow: 64000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Intelligent model with ~1400 tokens/s\",\r\n\t},\r\n\t\"llama-3.3-70b\": {\r\n\t\tmaxTokens: 64000,\r\n\t\tcontextWindow: 64000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Powerful model with ~2600 tokens/s\",\r\n\t},\r\n\t\"qwen-3-32b\": {\r\n\t\tmaxTokens: 64000,\r\n\t\tcontextWindow: 64000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"SOTA coding performance with ~2500 tokens/s\",\r\n\t},\r\n\t\"qwen-3-235b-a22b-thinking-2507\": {\r\n\t\tmaxTokens: 40000,\r\n\t\tcontextWindow: 65000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"SOTA performance with ~1500 tokens/s\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"gpt-oss-120b\": {\r\n\t\tmaxTokens: 8000,\r\n\t\tcontextWindow: 64000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"OpenAI GPT OSS model with ~2800 tokens/s\\n\\n• 64K context window\\n• Excels at efficient reasoning across science, math, and coding\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://llm.chutes.ai/v1 (OpenAI compatible)\r\nexport type ChutesModelId =\r\n\t| \"deepseek-ai/DeepSeek-R1-0528\"\r\n\t| \"deepseek-ai/DeepSeek-R1\"\r\n\t| \"deepseek-ai/DeepSeek-V3\"\r\n\t| \"deepseek-ai/DeepSeek-V3.1\"\r\n\t| \"unsloth/Llama-3.3-70B-Instruct\"\r\n\t| \"chutesai/Llama-4-Scout-17B-16E-Instruct\"\r\n\t| \"unsloth/Mistral-Nemo-Instruct-2407\"\r\n\t| \"unsloth/gemma-3-12b-it\"\r\n\t| \"NousResearch/DeepHermes-3-Llama-3-8B-Preview\"\r\n\t| \"unsloth/gemma-3-4b-it\"\r\n\t| \"nvidia/Llama-3_3-Nemotron-Super-49B-v1\"\r\n\t| \"nvidia/Llama-3_1-Nemotron-Ultra-253B-v1\"\r\n\t| \"chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8\"\r\n\t| \"deepseek-ai/DeepSeek-V3-Base\"\r\n\t| \"deepseek-ai/DeepSeek-R1-Zero\"\r\n\t| \"deepseek-ai/DeepSeek-V3-0324\"\r\n\t| \"Qwen/Qwen3-235B-A22B\"\r\n\t| \"Qwen/Qwen3-235B-A22B-Instruct-2507\"\r\n\t| \"Qwen/Qwen3-32B\"\r\n\t| \"Qwen/Qwen3-30B-A3B\"\r\n\t| \"Qwen/Qwen3-14B\"\r\n\t| \"Qwen/Qwen3-8B\"\r\n\t| \"Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8\"\r\n\t| \"microsoft/MAI-DS-R1-FP8\"\r\n\t| \"tngtech/DeepSeek-R1T-Chimera\"\r\n\t| \"zai-org/GLM-4.5-Air\"\r\n\t| \"zai-org/GLM-4.5-FP8\"\r\n\t| \"moonshotai/Kimi-K2-Instruct-75k\"\r\n\t| \"moonshotai/Kimi-K2-Instruct-0905\"\r\n\t| \"Qwen/Qwen3-235B-A22B-Thinking-2507\"\r\n\r\nexport const chutesDefaultModelId: ChutesModelId = \"deepseek-ai/DeepSeek-R1-0528\"\r\n\r\nexport const chutesModels = {\r\n\t\"deepseek-ai/DeepSeek-R1-0528\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 0528 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-R1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3.1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3.1 model.\",\r\n\t},\r\n\t\"unsloth/Llama-3.3-70B-Instruct\": {\r\n\t\tmaxTokens: 32768, // From Groq\r\n\t\tcontextWindow: 131072, // From Groq\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Llama 3.3 70B Instruct model.\",\r\n\t},\r\n\t\"chutesai/Llama-4-Scout-17B-16E-Instruct\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 512000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"ChutesAI Llama 4 Scout 17B Instruct model, 512K context.\",\r\n\t},\r\n\t\"unsloth/Mistral-Nemo-Instruct-2407\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Mistral Nemo Instruct model.\",\r\n\t},\r\n\t\"unsloth/gemma-3-12b-it\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Gemma 3 12B IT model.\",\r\n\t},\r\n\t\"NousResearch/DeepHermes-3-Llama-3-8B-Preview\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nous DeepHermes 3 Llama 3 8B Preview model.\",\r\n\t},\r\n\t\"unsloth/gemma-3-4b-it\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Gemma 3 4B IT model.\",\r\n\t},\r\n\t\"nvidia/Llama-3_3-Nemotron-Super-49B-v1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nvidia Llama 3.3 Nemotron Super 49B model.\",\r\n\t},\r\n\t\"nvidia/Llama-3_1-Nemotron-Ultra-253B-v1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nvidia Llama 3.1 Nemotron Ultra 253B model.\",\r\n\t},\r\n\t\"chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 256000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"ChutesAI Llama 4 Maverick 17B Instruct FP8 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3-Base\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 Base model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-R1-Zero\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 Zero model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3-0324\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 (0324) model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-235B-A22B-Instruct-2507\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 235B A22B Instruct 2507 model with 262K context window.\",\r\n\t},\r\n\t\"Qwen/Qwen3-235B-A22B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 235B A22B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-32B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 32B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-30B-A3B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 30B A3B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-14B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 14B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-8B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 8B model.\",\r\n\t},\r\n\t\"microsoft/MAI-DS-R1-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Microsoft MAI-DS-R1 FP8 model.\",\r\n\t},\r\n\t\"tngtech/DeepSeek-R1T-Chimera\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"TNGTech DeepSeek R1T Chimera model.\",\r\n\t},\r\n\t\"zai-org/GLM-4.5-Air\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 151329,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5-Air model with 151,329 token context window and 106B total parameters with 12B activated.\",\r\n\t},\r\n\t\"zai-org/GLM-4.5-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5-FP8 model with 128k token context window, optimized for agent-based applications with MoE architecture.\",\r\n\t},\r\n\t\"Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 Coder 480B A35B Instruct FP8 model, optimized for coding tasks.\",\r\n\t},\r\n\t\"moonshotai/Kimi-K2-Instruct-75k\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 75000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1481,\r\n\t\toutputPrice: 0.5926,\r\n\t\tdescription: \"Moonshot AI Kimi K2 Instruct model with 75k context window.\",\r\n\t},\r\n\t\"moonshotai/Kimi-K2-Instruct-0905\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1999,\r\n\t\toutputPrice: 0.8001,\r\n\t\tdescription: \"Moonshot AI Kimi K2 Instruct 0905 model with 256k context window.\",\r\n\t},\r\n\t\"Qwen/Qwen3-235B-A22B-Thinking-2507\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.077968332,\r\n\t\toutputPrice: 0.31202496,\r\n\t\tdescription: \"Qwen3 235B A22B Thinking 2507 model with 262K context window.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\nimport { anthropicModels } from \"./anthropic.js\"\r\n\r\n// Regex pattern to match 8-digit date at the end of model names\r\nconst VERTEX_DATE_PATTERN = /-(\\d{8})$/\r\n\r\n/**\r\n * Converts Claude model names from hyphen-date format to Vertex AI's @-date format.\r\n *\r\n * @param modelName - The original model name (e.g., \"claude-sonnet-4-20250514\")\r\n * @returns The converted model name for Vertex AI (e.g., \"claude-sonnet-4@20250514\")\r\n *\r\n * @example\r\n * convertModelNameForVertex(\"claude-sonnet-4-20250514\") // returns \"claude-sonnet-4@20250514\"\r\n * convertModelNameForVertex(\"claude-model\") // returns \"claude-model\" (no change)\r\n */\r\nexport function convertModelNameForVertex(modelName: string): string {\r\n\t// Convert hyphen-date format to @date format for Vertex AI\r\n\treturn modelName.replace(VERTEX_DATE_PATTERN, \"@$1\")\r\n}\r\n\r\n// Claude Code\r\nexport type ClaudeCodeModelId = keyof typeof claudeCodeModels\r\nexport const claudeCodeDefaultModelId: ClaudeCodeModelId = \"claude-sonnet-4-20250514\"\r\nexport const CLAUDE_CODE_DEFAULT_MAX_OUTPUT_TOKENS = 16000\r\n\r\n/**\r\n * Gets the appropriate model ID based on whether Vertex AI is being used.\r\n *\r\n * @param baseModelId - The base Claude Code model ID\r\n * @param useVertex - Whether to format the model ID for Vertex AI (default: false)\r\n * @returns The model ID, potentially formatted for Vertex AI\r\n *\r\n * @example\r\n * getClaudeCodeModelId(\"claude-sonnet-4-20250514\", true) // returns \"claude-sonnet-4@20250514\"\r\n * getClaudeCodeModelId(\"claude-sonnet-4-20250514\", false) // returns \"claude-sonnet-4-20250514\"\r\n */\r\nexport function getClaudeCodeModelId(baseModelId: ClaudeCodeModelId, useVertex = false): string {\r\n\treturn useVertex ? convertModelNameForVertex(baseModelId) : baseModelId\r\n}\r\n\r\nexport const claudeCodeModels = {\r\n\t\"claude-sonnet-4-20250514\": {\r\n\t\t...anthropicModels[\"claude-sonnet-4-20250514\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n\t\"claude-opus-4-1-20250805\": {\r\n\t\t...anthropicModels[\"claude-opus-4-1-20250805\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n\t\"claude-opus-4-20250514\": {\r\n\t\t...anthropicModels[\"claude-opus-4-20250514\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n\t\"claude-3-7-sonnet-20250219\": {\r\n\t\t...anthropicModels[\"claude-3-7-sonnet-20250219\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n\t\"claude-3-5-sonnet-20241022\": {\r\n\t\t...anthropicModels[\"claude-3-5-sonnet-20241022\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n\t\"claude-3-5-haiku-20241022\": {\r\n\t\t...anthropicModels[\"claude-3-5-haiku-20241022\"],\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true, // Claude Code does report cache tokens\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tsupportsReasoningBudget: false,\r\n\t\trequiredReasoningBudget: false,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://platform.deepseek.com/docs/api\r\nexport type DeepSeekModelId = keyof typeof deepSeekModels\r\n\r\nexport const deepSeekDefaultModelId: DeepSeekModelId = \"deepseek-chat\"\r\n\r\nexport const deepSeekModels = {\r\n\t\"deepseek-chat\": {\r\n\t\tmaxTokens: 8192, // 8K max output\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.56, // $0.56 per million tokens (cache miss) - Updated Sept 5, 2025\r\n\t\toutputPrice: 1.68, // $1.68 per million tokens - Updated Sept 5, 2025\r\n\t\tcacheWritesPrice: 0.56, // $0.56 per million tokens (cache miss) - Updated Sept 5, 2025\r\n\t\tcacheReadsPrice: 0.07, // $0.07 per million tokens (cache hit) - Updated Sept 5, 2025\r\n\t\tdescription: `DeepSeek-V3 achieves a significant breakthrough in inference speed over previous models. It tops the leaderboard among open-source models and rivals the most advanced closed-source models globally.`,\r\n\t},\r\n\t\"deepseek-reasoner\": {\r\n\t\tmaxTokens: 65536, // 64K max output for reasoning mode\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.56, // $0.56 per million tokens (cache miss) - Updated Sept 5, 2025\r\n\t\toutputPrice: 1.68, // $1.68 per million tokens - Updated Sept 5, 2025\r\n\t\tcacheWritesPrice: 0.56, // $0.56 per million tokens (cache miss) - Updated Sept 5, 2025\r\n\t\tcacheReadsPrice: 0.07, // $0.07 per million tokens (cache hit) - Updated Sept 5, 2025\r\n\t\tdescription: `DeepSeek-R1 achieves performance comparable to OpenAI-o1 across math, code, and reasoning tasks. Supports Chain of Thought reasoning with up to 64K output tokens.`,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const DEEP_SEEK_DEFAULT_TEMPERATURE = 0.6\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport const doubaoDefaultModelId = \"doubao-seed-1-6-250615\"\r\n\r\nexport const doubaoModels = {\r\n\t\"doubao-seed-1-6-250615\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.0001, // $0.0001 per million tokens (cache miss)\r\n\t\toutputPrice: 0.0004, // $0.0004 per million tokens\r\n\t\tcacheWritesPrice: 0.0001, // $0.0001 per million tokens (cache miss)\r\n\t\tcacheReadsPrice: 0.00002, // $0.00002 per million tokens (cache hit)\r\n\t\tdescription: `Doubao Seed 1.6 is a powerful model designed for high-performance tasks with extensive context handling.`,\r\n\t},\r\n\t\"doubao-seed-1-6-thinking-250715\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.0002, // $0.0002 per million tokens\r\n\t\toutputPrice: 0.0008, // $0.0008 per million tokens\r\n\t\tcacheWritesPrice: 0.0002, // $0.0002 per million\r\n\t\tcacheReadsPrice: 0.00004, // $0.00004 per million tokens (cache hit)\r\n\t\tdescription: `Doubao Seed 1.6 Thinking is optimized for reasoning tasks, providing enhanced performance in complex problem-solving scenarios.`,\r\n\t},\r\n\t\"doubao-seed-1-6-flash-250715\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.00015, // $0.00015 per million tokens\r\n\t\toutputPrice: 0.0006, // $0.0006 per million tokens\r\n\t\tcacheWritesPrice: 0.00015, // $0.00015 per million\r\n\t\tcacheReadsPrice: 0.00003, // $0.00003 per million tokens (cache hit)\r\n\t\tdescription: `Doubao Seed 1.6 Flash is tailored for speed and efficiency, making it ideal for applications requiring rapid responses.`,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const doubaoDefaultModelInfo: ModelInfo = doubaoModels[doubaoDefaultModelId]\r\n\r\nexport const DOUBAO_API_BASE_URL = \"https://ark.cn-beijing.volces.com/api/v3\"\r\nexport const DOUBAO_API_CHAT_PATH = \"/chat/completions\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type FeatherlessModelId =\r\n\t| \"deepseek-ai/DeepSeek-V3-0324\"\r\n\t| \"deepseek-ai/DeepSeek-R1-0528\"\r\n\t| \"moonshotai/Kimi-K2-Instruct\"\r\n\t| \"openai/gpt-oss-120b\"\r\n\t| \"Qwen/Qwen3-Coder-480B-A35B-Instruct\"\r\n\r\nexport const featherlessModels = {\r\n\t\"deepseek-ai/DeepSeek-V3-0324\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 32678,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 0324 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-R1-0528\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 32678,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 0528 model.\",\r\n\t},\r\n\t\"moonshotai/Kimi-K2-Instruct\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 32678,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Kimi K2 Instruct model.\",\r\n\t},\r\n\t\"openai/gpt-oss-120b\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 32678,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"GPT-OSS 120B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-Coder-480B-A35B-Instruct\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 32678,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 Coder 480B A35B Instruct model.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const featherlessDefaultModelId: FeatherlessModelId = \"deepseek-ai/DeepSeek-R1-0528\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type FireworksModelId =\r\n\t| \"accounts/fireworks/models/kimi-k2-instruct\"\r\n\t| \"accounts/fireworks/models/kimi-k2-instruct-0905\"\r\n\t| \"accounts/fireworks/models/qwen3-235b-a22b-instruct-2507\"\r\n\t| \"accounts/fireworks/models/qwen3-coder-480b-a35b-instruct\"\r\n\t| \"accounts/fireworks/models/deepseek-r1-0528\"\r\n\t| \"accounts/fireworks/models/deepseek-v3\"\r\n\t| \"accounts/fireworks/models/deepseek-v3p1\"\r\n\t| \"accounts/fireworks/models/glm-4p5\"\r\n\t| \"accounts/fireworks/models/glm-4p5-air\"\r\n\t| \"accounts/fireworks/models/gpt-oss-20b\"\r\n\t| \"accounts/fireworks/models/gpt-oss-120b\"\r\n\r\nexport const fireworksDefaultModelId: FireworksModelId = \"accounts/fireworks/models/kimi-k2-instruct-0905\"\r\n\r\nexport const fireworksModels = {\r\n\t\"accounts/fireworks/models/kimi-k2-instruct-0905\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.15,\r\n\t\tdescription:\r\n\t\t\t\"Kimi K2 model gets a new version update: Agentic coding: more accurate, better generalization across scaffolds. Frontend coding: improved aesthetics and functionalities on web, 3d, and other tasks. Context length: extended from 128k to 256k, providing better long-horizon support.\",\r\n\t},\r\n\t\"accounts/fireworks/models/kimi-k2-instruct\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 2.5,\r\n\t\tdescription:\r\n\t\t\t\"Kimi K2 is a state-of-the-art mixture-of-experts (MoE) language model with 32 billion activated parameters and 1 trillion total parameters. Trained with the Muon optimizer, Kimi K2 achieves exceptional performance across frontier knowledge, reasoning, and coding tasks while being meticulously optimized for agentic capabilities.\",\r\n\t},\r\n\t\"accounts/fireworks/models/qwen3-235b-a22b-instruct-2507\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 256000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.22,\r\n\t\toutputPrice: 0.88,\r\n\t\tdescription: \"Latest Qwen3 thinking model, competitive against the best closed source models in Jul 2025.\",\r\n\t},\r\n\t\"accounts/fireworks/models/qwen3-coder-480b-a35b-instruct\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 256000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.45,\r\n\t\toutputPrice: 1.8,\r\n\t\tdescription: \"Qwen3's most agentic code model to date.\",\r\n\t},\r\n\t\"accounts/fireworks/models/deepseek-r1-0528\": {\r\n\t\tmaxTokens: 20480,\r\n\t\tcontextWindow: 160000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3,\r\n\t\toutputPrice: 8,\r\n\t\tdescription:\r\n\t\t\t\"05/28 updated checkpoint of Deepseek R1. Its overall performance is now approaching that of leading models, such as O3 and Gemini 2.5 Pro. Compared to the previous version, the upgraded model shows significant improvements in handling complex reasoning tasks, and this version also offers a reduced hallucination rate, enhanced support for function calling, and better experience for vibe coding. Note that fine-tuning for this model is only available through contacting fireworks at https://fireworks.ai/company/contact-us.\",\r\n\t},\r\n\t\"accounts/fireworks/models/deepseek-v3\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.9,\r\n\t\toutputPrice: 0.9,\r\n\t\tdescription:\r\n\t\t\t\"A strong Mixture-of-Experts (MoE) language model with 671B total parameters with 37B activated for each token from Deepseek. Note that fine-tuning for this model is only available through contacting fireworks at https://fireworks.ai/company/contact-us.\",\r\n\t},\r\n\t\"accounts/fireworks/models/deepseek-v3p1\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.56,\r\n\t\toutputPrice: 1.68,\r\n\t\tdescription:\r\n\t\t\t\"DeepSeek v3.1 is an improved version of the v3 model with enhanced performance, better reasoning capabilities, and improved code generation. This Mixture-of-Experts (MoE) model maintains the same 671B total parameters with 37B activated per token.\",\r\n\t},\r\n\t\"accounts/fireworks/models/glm-4p5\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.55,\r\n\t\toutputPrice: 2.19,\r\n\t\tdescription:\r\n\t\t\t\"Z.ai GLM-4.5 with 355B total parameters and 32B active parameters. Features unified reasoning, coding, and intelligent agent capabilities.\",\r\n\t},\r\n\t\"accounts/fireworks/models/glm-4p5-air\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.55,\r\n\t\toutputPrice: 2.19,\r\n\t\tdescription:\r\n\t\t\t\"Z.ai GLM-4.5-Air with 106B total parameters and 12B active parameters. Features unified reasoning, coding, and intelligent agent capabilities.\",\r\n\t},\r\n\t\"accounts/fireworks/models/gpt-oss-20b\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.07,\r\n\t\toutputPrice: 0.3,\r\n\t\tdescription:\r\n\t\t\t\"OpenAI gpt-oss-20b: Compact model for local/edge deployments. Optimized for low-latency and resource-constrained environments with chain-of-thought output, adjustable reasoning, and agentic workflows.\",\r\n\t},\r\n\t\"accounts/fireworks/models/gpt-oss-120b\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tdescription:\r\n\t\t\t\"OpenAI gpt-oss-120b: Production-grade, general-purpose model that fits on a single H100 GPU. Features complex reasoning, configurable effort, full chain-of-thought transparency, and supports function calling, tool use, and structured outputs.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://ai.google.dev/gemini-api/docs/models/gemini\r\nexport type GeminiModelId = keyof typeof geminiModels\r\n\r\nexport const geminiDefaultModelId: GeminiModelId = \"gemini-2.0-flash-001\"\r\n\r\nexport const geminiModels = {\r\n\t\"gemini-2.5-flash-preview-04-17:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.5-flash\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.075,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-pro-exp-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-preview-05-06\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-preview-06-05\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.0-flash-lite-preview-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-pro-exp-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-01-21\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-1219\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32_767,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-exp\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15, // This is the pricing for prompts above 128k tokens.\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 128_000,\r\n\t\t\t\tinputPrice: 0.075,\r\n\t\t\t\toutputPrice: 0.3,\r\n\t\t\t\tcacheReadsPrice: 0.01875,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 0.15,\r\n\t\t\t\toutputPrice: 0.6,\r\n\t\t\t\tcacheReadsPrice: 0.0375,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-1.5-flash-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-8b-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-exp-1206\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.5-flash-lite-preview-06-17\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Gemini CLI models - using the same models as regular Gemini\r\n// The CLI provides access to the same models through OAuth authentication\r\nexport type GeminiCliModelId = keyof typeof geminiCliModels\r\n\r\nexport const geminiCliDefaultModelId: GeminiCliModelId = \"gemini-2.0-flash-001\"\r\n\r\n// Re-use the same model definitions as the regular Gemini provider\r\n// since Gemini CLI provides access to the same models\r\nexport const geminiCliModels = {\r\n\t\"gemini-2.5-flash-preview-04-17:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.5-flash\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.075,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-pro-exp-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-preview-05-06\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-preview-06-05\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.0-flash-lite-preview-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-pro-exp-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-01-21\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-1219\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32_767,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-exp\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15, // This is the pricing for prompts above 128k tokens.\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 128_000,\r\n\t\t\t\tinputPrice: 0.075,\r\n\t\t\t\toutputPrice: 0.3,\r\n\t\t\t\tcacheReadsPrice: 0.01875,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 0.15,\r\n\t\t\t\toutputPrice: 0.6,\r\n\t\t\t\tcacheReadsPrice: 0.0375,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-1.5-flash-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-8b-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-exp-1206\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.5-flash-lite-preview-06-17\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://glama.ai/models\r\nexport const glamaDefaultModelId = \"anthropic/claude-3-7-sonnet\"\r\n\r\nexport const glamaDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)\",\r\n}\r\n\r\nexport const GLAMA_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://console.groq.com/docs/models\r\nexport type GroqModelId =\r\n\t| \"llama-3.1-8b-instant\"\r\n\t| \"llama-3.3-70b-versatile\"\r\n\t| \"meta-llama/llama-4-scout-17b-16e-instruct\"\r\n\t| \"meta-llama/llama-4-maverick-17b-128e-instruct\"\r\n\t| \"mistral-saba-24b\"\r\n\t| \"qwen-qwq-32b\"\r\n\t| \"qwen/qwen3-32b\"\r\n\t| \"deepseek-r1-distill-llama-70b\"\r\n\t| \"moonshotai/kimi-k2-instruct\"\r\n\t| \"moonshotai/kimi-k2-instruct-0905\"\r\n\t| \"openai/gpt-oss-120b\"\r\n\t| \"openai/gpt-oss-20b\"\r\n\r\nexport const groqDefaultModelId: GroqModelId = \"moonshotai/kimi-k2-instruct-0905\"\r\n\r\nexport const groqModels = {\r\n\t// Models based on API response: https://api.groq.com/openai/v1/models\r\n\t\"llama-3.1-8b-instant\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.05,\r\n\t\toutputPrice: 0.08,\r\n\t\tdescription: \"Meta Llama 3.1 8B Instant model, 128K context.\",\r\n\t},\r\n\t\"llama-3.3-70b-versatile\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.59,\r\n\t\toutputPrice: 0.79,\r\n\t\tdescription: \"Meta Llama 3.3 70B Versatile model, 128K context.\",\r\n\t},\r\n\t\"meta-llama/llama-4-scout-17b-16e-instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.11,\r\n\t\toutputPrice: 0.34,\r\n\t\tdescription: \"Meta Llama 4 Scout 17B Instruct model, 128K context.\",\r\n\t},\r\n\t\"meta-llama/llama-4-maverick-17b-128e-instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 0.6,\r\n\t\tdescription: \"Meta Llama 4 Maverick 17B Instruct model, 128K context.\",\r\n\t},\r\n\t\"mistral-saba-24b\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.79,\r\n\t\toutputPrice: 0.79,\r\n\t\tdescription: \"Mistral Saba 24B model, 32K context.\",\r\n\t},\r\n\t\"qwen-qwq-32b\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.29,\r\n\t\toutputPrice: 0.39,\r\n\t\tdescription: \"Alibaba Qwen QwQ 32B model, 128K context.\",\r\n\t},\r\n\t\"qwen/qwen3-32b\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.29,\r\n\t\toutputPrice: 0.59,\r\n\t\tdescription: \"Alibaba Qwen 3 32B model, 128K context.\",\r\n\t},\r\n\t\"deepseek-r1-distill-llama-70b\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.75,\r\n\t\toutputPrice: 0.99,\r\n\t\tdescription: \"DeepSeek R1 Distill Llama 70B model, 128K context.\",\r\n\t},\r\n\t\"moonshotai/kimi-k2-instruct\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 3.0,\r\n\t\tcacheReadsPrice: 0.5, // 50% discount for cached input tokens\r\n\t\tdescription: \"Moonshot AI Kimi K2 Instruct 1T model, 128K context.\",\r\n\t},\r\n\t\"moonshotai/kimi-k2-instruct-0905\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.15,\r\n\t\tdescription:\r\n\t\t\t\"Kimi K2 model gets a new version update: Agentic coding: more accurate, better generalization across scaffolds. Frontend coding: improved aesthetics and functionalities on web, 3d, and other tasks. Context length: extended from 128k to 256k, providing better long-horizon support.\",\r\n\t},\r\n\t\"openai/gpt-oss-120b\": {\r\n\t\tmaxTokens: 32766,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.75,\r\n\t\tdescription:\r\n\t\t\t\"GPT-OSS 120B is OpenAI's flagship open source model, built on a Mixture-of-Experts (MoE) architecture with 20 billion parameters and 128 experts.\",\r\n\t},\r\n\t\"openai/gpt-oss-20b\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.5,\r\n\t\tdescription:\r\n\t\t\t\"GPT-OSS 20B is OpenAI's flagship open source model, built on a Mixture-of-Experts (MoE) architecture with 20 billion parameters and 32 experts.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "/**\r\n * HuggingFace provider constants\r\n */\r\n\r\n// Default values for HuggingFace models\r\nexport const HUGGINGFACE_DEFAULT_MAX_TOKENS = 2048\r\nexport const HUGGINGFACE_MAX_TOKENS_FALLBACK = 8192\r\nexport const HUGGINGFACE_DEFAULT_CONTEXT_WINDOW = 128_000\r\n\r\n// UI constants\r\nexport const HUGGINGFACE_SLIDER_STEP = 256\r\nexport const HUGGINGFACE_SLIDER_MIN = 1\r\nexport const HUGGINGFACE_TEMPERATURE_MAX_VALUE = 2\r\n\r\n// API constants\r\nexport const HUGGINGFACE_API_URL = \"https://router.huggingface.co/v1/models?collection=roocode\"\r\nexport const HUGGINGFACE_CACHE_DURATION = 1000 * 60 * 60 // 1 hour\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type IOIntelligenceModelId =\r\n\t| \"deepseek-ai/DeepSeek-R1-0528\"\r\n\t| \"meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8\"\r\n\t| \"Intel/Qwen3-Coder-480B-A35B-Instruct-int4-mixed-ar\"\r\n\t| \"openai/gpt-oss-120b\"\r\n\r\nexport const ioIntelligenceDefaultModelId: IOIntelligenceModelId = \"meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8\"\r\n\r\nexport const ioIntelligenceDefaultBaseUrl = \"https://api.intelligence.io.solutions/api/v1\"\r\n\r\nexport const IO_INTELLIGENCE_CACHE_DURATION = 1000 * 60 * 60 // 1 hour\r\n\r\nexport const ioIntelligenceModels = {\r\n\t\"deepseek-ai/DeepSeek-R1-0528\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tdescription: \"DeepSeek R1 reasoning model\",\r\n\t},\r\n\t\"meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 430000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tdescription: \"Llama 4 Maverick 17B model\",\r\n\t},\r\n\t\"Intel/Qwen3-Coder-480B-A35B-Instruct-int4-mixed-ar\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 106000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tdescription: \"Qwen3 Coder 480B specialized for coding\",\r\n\t},\r\n\t\"openai/gpt-oss-120b\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tdescription: \"OpenAI GPT-OSS 120B model\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.litellm.ai/\r\nexport const litellmDefaultModelId = \"claude-3-7-sonnet-20250219\"\r\n\r\nexport const litellmDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n}\r\n\r\nexport const LITELLM_COMPUTER_USE_MODELS = new Set([\r\n\t\"claude-3-5-sonnet-latest\",\r\n\t\"claude-opus-4-1-20250805\",\r\n\t\"claude-opus-4-20250514\",\r\n\t\"claude-sonnet-4-20250514\",\r\n\t\"claude-3-7-sonnet-latest\",\r\n\t\"claude-3-7-sonnet-20250219\",\r\n\t\"claude-3-5-sonnet-20241022\",\r\n\t\"vertex_ai/claude-3-5-sonnet\",\r\n\t\"vertex_ai/claude-3-5-sonnet-v2\",\r\n\t\"vertex_ai/claude-3-5-sonnet-v2@20241022\",\r\n\t\"vertex_ai/claude-3-7-sonnet@20250219\",\r\n\t\"vertex_ai/claude-opus-4-1@20250805\",\r\n\t\"vertex_ai/claude-opus-4@20250514\",\r\n\t\"vertex_ai/claude-sonnet-4@20250514\",\r\n\t\"openrouter/anthropic/claude-3.5-sonnet\",\r\n\t\"openrouter/anthropic/claude-3.5-sonnet:beta\",\r\n\t\"openrouter/anthropic/claude-3.7-sonnet\",\r\n\t\"openrouter/anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic.claude-opus-4-1-20250805-v1:0\",\r\n\t\"anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"us.anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"us.anthropic.claude-opus-4-1-20250805-v1:0\",\r\n\t\"us.anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"eu.anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"eu.anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"eu.anthropic.claude-opus-4-1-20250805-v1:0\",\r\n\t\"eu.anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"eu.anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"snowflake/claude-3-5-sonnet\",\r\n])\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport const LMSTUDIO_DEFAULT_TEMPERATURE = 0\r\n\r\n// LM Studio\r\n// https://lmstudio.ai/docs/cli/ls\r\nexport const lMStudioDefaultModelId = \"mistralai/devstral-small-2505\"\r\nexport const lMStudioDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 0,\r\n\toutputPrice: 0,\r\n\tcacheWritesPrice: 0,\r\n\tcacheReadsPrice: 0,\r\n\tdescription: \"LM Studio hosted models\",\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.mistral.ai/getting-started/models/models_overview/\r\nexport type MistralModelId = keyof typeof mistralModels\r\n\r\nexport const mistralDefaultModelId: MistralModelId = \"codestral-latest\"\r\n\r\nexport const mistralModels = {\r\n\t\"magistral-medium-latest\": {\r\n\t\tmaxTokens: 41_000,\r\n\t\tcontextWindow: 41_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 5.0,\r\n\t},\r\n\t\"devstral-medium-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.4,\r\n\t\toutputPrice: 2.0,\r\n\t},\r\n\t\"mistral-medium-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.4,\r\n\t\toutputPrice: 2.0,\r\n\t},\r\n\t\"codestral-latest\": {\r\n\t\tmaxTokens: 256_000,\r\n\t\tcontextWindow: 256_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.9,\r\n\t},\r\n\t\"mistral-large-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 6.0,\r\n\t},\r\n\t\"ministral-8b-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.1,\r\n\t},\r\n\t\"ministral-3b-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.04,\r\n\t\toutputPrice: 0.04,\r\n\t},\r\n\t\"mistral-small-latest\": {\r\n\t\tmaxTokens: 32_000,\r\n\t\tcontextWindow: 32_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"pixtral-large-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 6.0,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const MISTRAL_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://platform.moonshot.ai/\r\nexport type MoonshotModelId = keyof typeof moonshotModels\r\n\r\nexport const moonshotDefaultModelId: MoonshotModelId = \"kimi-k2-0905-preview\"\r\n\r\nexport const moonshotModels = {\r\n\t\"kimi-k2-0711-preview\": {\r\n\t\tmaxTokens: 32_000,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6, // $0.60 per million tokens (cache miss)\r\n\t\toutputPrice: 2.5, // $2.50 per million tokens\r\n\t\tcacheWritesPrice: 0, // $0 per million tokens (cache miss)\r\n\t\tcacheReadsPrice: 0.15, // $0.15 per million tokens (cache hit)\r\n\t\tdescription: `Kimi K2 is a state-of-the-art mixture-of-experts (MoE) language model with 32 billion activated parameters and 1 trillion total parameters.`,\r\n\t},\r\n\t\"kimi-k2-0905-preview\": {\r\n\t\tmaxTokens: 16384,\r\n\t\tcontextWindow: 262144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.15,\r\n\t\tdescription:\r\n\t\t\t\"Kimi K2 model gets a new version update: Agentic coding: more accurate, better generalization across scaffolds. Frontend coding: improved aesthetics and functionalities on web, 3d, and other tasks. Context length: extended from 128k to 256k, providing better long-horizon support.\",\r\n\t},\r\n\t\"kimi-k2-turbo-preview\": {\r\n\t\tmaxTokens: 32_000,\r\n\t\tcontextWindow: 262_144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.4, // $2.40 per million tokens (cache miss)\r\n\t\toutputPrice: 10, // $10.00 per million tokens\r\n\t\tcacheWritesPrice: 0, // $0 per million tokens (cache miss)\r\n\t\tcacheReadsPrice: 0.6, // $0.60 per million tokens (cache hit)\r\n\t\tdescription: `Kimi K2 Turbo is a high-speed version of the state-of-the-art Kimi K2 mixture-of-experts (MoE) language model, with the same 32 billion activated parameters and 1 trillion total parameters, optimized for output speeds of up to 60 tokens per second, peaking at 100 tokens per second.`,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const MOONSHOT_DEFAULT_TEMPERATURE = 0.6\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Ollama\r\n// https://ollama.com/models\r\nexport const ollamaDefaultModelId = \"devstral:24b\"\r\nexport const ollamaDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 4096,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 0,\r\n\toutputPrice: 0,\r\n\tcacheWritesPrice: 0,\r\n\tcacheReadsPrice: 0,\r\n\tdescription: \"Ollama hosted models\",\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://openai.com/api/pricing/\r\nexport type OpenAiNativeModelId = keyof typeof openAiNativeModels\r\n\r\nexport const openAiNativeDefaultModelId: OpenAiNativeModelId = \"gpt-5-2025-08-07\"\r\n\r\nexport const openAiNativeModels = {\r\n\t\"gpt-5-chat-latest\": {\r\n\t\tmaxTokens: 128000,\r\n\t\tcontextWindow: 400000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningEffort: false,\r\n\t\tinputPrice: 1.25,\r\n\t\toutputPrice: 10.0,\r\n\t\tcacheReadsPrice: 0.13,\r\n\t\tdescription: \"GPT-5 Chat Latest: Optimized for conversational AI and non-reasoning tasks\",\r\n\t\tsupportsVerbosity: true,\r\n\t},\r\n\t\"gpt-5-2025-08-07\": {\r\n\t\tmaxTokens: 128000,\r\n\t\tcontextWindow: 400000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tinputPrice: 1.25,\r\n\t\toutputPrice: 10.0,\r\n\t\tcacheReadsPrice: 0.13,\r\n\t\tdescription: \"GPT-5: The best model for coding and agentic tasks across domains\",\r\n\t\t// supportsVerbosity is a new capability; ensure ModelInfo includes it\r\n\t\tsupportsVerbosity: true,\r\n\t\tsupportsTemperature: false,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"flex\", contextWindow: 400000, inputPrice: 0.625, outputPrice: 5.0, cacheReadsPrice: 0.0625 },\r\n\t\t\t{ name: \"priority\", contextWindow: 400000, inputPrice: 2.5, outputPrice: 20.0, cacheReadsPrice: 0.25 },\r\n\t\t],\r\n\t},\r\n\t\"gpt-5-mini-2025-08-07\": {\r\n\t\tmaxTokens: 128000,\r\n\t\tcontextWindow: 400000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 2.0,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t\tdescription: \"GPT-5 Mini: A faster, more cost-efficient version of GPT-5 for well-defined tasks\",\r\n\t\tsupportsVerbosity: true,\r\n\t\tsupportsTemperature: false,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"flex\", contextWindow: 400000, inputPrice: 0.125, outputPrice: 1.0, cacheReadsPrice: 0.0125 },\r\n\t\t\t{ name: \"priority\", contextWindow: 400000, inputPrice: 0.45, outputPrice: 3.6, cacheReadsPrice: 0.045 },\r\n\t\t],\r\n\t},\r\n\t\"gpt-5-nano-2025-08-07\": {\r\n\t\tmaxTokens: 128000,\r\n\t\tcontextWindow: 400000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tinputPrice: 0.05,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.01,\r\n\t\tdescription: \"GPT-5 Nano: Fastest, most cost-efficient version of GPT-5\",\r\n\t\tsupportsVerbosity: true,\r\n\t\tsupportsTemperature: false,\r\n\t\ttiers: [{ name: \"flex\", contextWindow: 400000, inputPrice: 0.025, outputPrice: 0.2, cacheReadsPrice: 0.0025 }],\r\n\t},\r\n\t\"gpt-4.1\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2,\r\n\t\toutputPrice: 8,\r\n\t\tcacheReadsPrice: 0.5,\r\n\t\tsupportsTemperature: true,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"priority\", contextWindow: 1_047_576, inputPrice: 3.5, outputPrice: 14.0, cacheReadsPrice: 0.875 },\r\n\t\t],\r\n\t},\r\n\t\"gpt-4.1-mini\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.4,\r\n\t\toutputPrice: 1.6,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t\tsupportsTemperature: true,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"priority\", contextWindow: 1_047_576, inputPrice: 0.7, outputPrice: 2.8, cacheReadsPrice: 0.175 },\r\n\t\t],\r\n\t},\r\n\t\"gpt-4.1-nano\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tsupportsTemperature: true,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"priority\", contextWindow: 1_047_576, inputPrice: 0.2, outputPrice: 0.8, cacheReadsPrice: 0.05 },\r\n\t\t],\r\n\t},\r\n\to3: {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 8.0,\r\n\t\tcacheReadsPrice: 0.5,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tsupportsTemperature: false,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"flex\", contextWindow: 200_000, inputPrice: 1.0, outputPrice: 4.0, cacheReadsPrice: 0.25 },\r\n\t\t\t{ name: \"priority\", contextWindow: 200_000, inputPrice: 3.5, outputPrice: 14.0, cacheReadsPrice: 0.875 },\r\n\t\t],\r\n\t},\r\n\t\"o3-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 8.0,\r\n\t\tcacheReadsPrice: 0.5,\r\n\t\treasoningEffort: \"high\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o3-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 8.0,\r\n\t\tcacheReadsPrice: 0.5,\r\n\t\treasoningEffort: \"low\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o4-mini\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tsupportsTemperature: false,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"flex\", contextWindow: 200_000, inputPrice: 0.55, outputPrice: 2.2, cacheReadsPrice: 0.138 },\r\n\t\t\t{ name: \"priority\", contextWindow: 200_000, inputPrice: 2.0, outputPrice: 8.0, cacheReadsPrice: 0.5 },\r\n\t\t],\r\n\t},\r\n\t\"o4-mini-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\treasoningEffort: \"high\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o4-mini-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\treasoningEffort: \"low\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o3-mini\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o3-mini-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\treasoningEffort: \"high\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o3-mini-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\treasoningEffort: \"low\",\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\to1: {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15,\r\n\t\toutputPrice: 60,\r\n\t\tcacheReadsPrice: 7.5,\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o1-preview\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15,\r\n\t\toutputPrice: 60,\r\n\t\tcacheReadsPrice: 7.5,\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"o1-mini\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\tsupportsTemperature: false,\r\n\t},\r\n\t\"gpt-4o\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 10,\r\n\t\tcacheReadsPrice: 1.25,\r\n\t\tsupportsTemperature: true,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"priority\", contextWindow: 128_000, inputPrice: 4.25, outputPrice: 17.0, cacheReadsPrice: 2.125 },\r\n\t\t],\r\n\t},\r\n\t\"gpt-4o-mini\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.075,\r\n\t\tsupportsTemperature: true,\r\n\t\ttiers: [\r\n\t\t\t{ name: \"priority\", contextWindow: 128_000, inputPrice: 0.25, outputPrice: 1.0, cacheReadsPrice: 0.125 },\r\n\t\t],\r\n\t},\r\n\t\"codex-mini-latest\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.5,\r\n\t\toutputPrice: 6,\r\n\t\tcacheReadsPrice: 0,\r\n\t\tsupportsTemperature: false,\r\n\t\tdescription:\r\n\t\t\t\"Codex Mini: Cloud-based software engineering agent powered by codex-1, a version of o3 optimized for coding tasks. Trained with reinforcement learning to generate human-style code, adhere to instructions, and iteratively run tests.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const openAiModelInfoSaneDefaults: ModelInfo = {\r\n\tmaxTokens: -1,\r\n\tcontextWindow: 128_000,\r\n\tsupportsImages: true,\r\n\tsupportsPromptCache: false,\r\n\tinputPrice: 0,\r\n\toutputPrice: 0,\r\n}\r\n\r\n// https://learn.microsoft.com/en-us/azure/ai-services/openai/api-version-deprecation\r\n// https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#api-specs\r\nexport const azureOpenAiDefaultApiVersion = \"2024-08-01-preview\"\r\n\r\nexport const OPENAI_NATIVE_DEFAULT_TEMPERATURE = 0\r\nexport const GPT5_DEFAULT_TEMPERATURE = 1.0\r\n\r\nexport const OPENAI_AZURE_AI_INFERENCE_PATH = \"/models/chat/completions\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://openrouter.ai/models?order=newest&supported_parameters=tools\r\nexport const openRouterDefaultModelId = \"anthropic/claude-sonnet-4\"\r\n\r\nexport const openRouterDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)\",\r\n}\r\n\r\nexport const OPENROUTER_DEFAULT_PROVIDER_NAME = \"[default]\"\r\n\r\nexport const OPEN_ROUTER_PROMPT_CACHING_MODELS = new Set([\r\n\t\"anthropic/claude-3-haiku\",\r\n\t\"anthropic/claude-3-haiku:beta\",\r\n\t\"anthropic/claude-3-opus\",\r\n\t\"anthropic/claude-3-opus:beta\",\r\n\t\"anthropic/claude-3-sonnet\",\r\n\t\"anthropic/claude-3-sonnet:beta\",\r\n\t\"anthropic/claude-3.5-haiku\",\r\n\t\"anthropic/claude-3.5-haiku-20241022\",\r\n\t\"anthropic/claude-3.5-haiku-20241022:beta\",\r\n\t\"anthropic/claude-3.5-haiku:beta\",\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.5-sonnet-20240620\",\r\n\t\"anthropic/claude-3.5-sonnet-20240620:beta\",\r\n\t\"anthropic/claude-3.5-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-opus-4.1\",\r\n\t\"google/gemini-2.5-flash-preview\",\r\n\t\"google/gemini-2.5-flash-preview:thinking\",\r\n\t\"google/gemini-2.5-flash-preview-05-20\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n\t\"google/gemini-2.5-flash\",\r\n\t\"google/gemini-2.5-flash-lite-preview-06-17\",\r\n\t\"google/gemini-2.0-flash-001\",\r\n\t\"google/gemini-flash-1.5\",\r\n\t\"google/gemini-flash-1.5-8b\",\r\n])\r\n\r\n// https://www.anthropic.com/news/3-5-models-and-computer-use\r\nexport const OPEN_ROUTER_COMPUTER_USE_MODELS = new Set([\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.5-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-opus-4.1\",\r\n])\r\n\r\n// When we first launched these models we didn't have support for\r\n// enabling/disabling the reasoning budget for hybrid models. Now that we\r\n// do support this we should give users the option to enable/disable it\r\n// whenever possible. However these particular (virtual) model ids with the\r\n// `:thinking` suffix always require the reasoning budget to be enabled, so\r\n// for backwards compatibility we should still require it.\r\n// We should *not* be adding new models to this set.\r\nexport const OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS = new Set([\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"google/gemini-2.5-pro\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n])\r\n\r\nexport const OPEN_ROUTER_REASONING_BUDGET_MODELS = new Set([\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-opus-4.1\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"google/gemini-2.5-pro-preview\",\r\n\t\"google/gemini-2.5-pro\",\r\n\t\"google/gemini-2.5-flash-preview-05-20\",\r\n\t\"google/gemini-2.5-flash\",\r\n\t\"google/gemini-2.5-flash-lite-preview-06-17\",\r\n\t// Also include the models that require the reasoning budget to be enabled\r\n\t// even though `OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS` takes precedence.\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n])\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type QwenCodeModelId = \"qwen3-coder-plus\" | \"qwen3-coder-flash\"\r\n\r\nexport const qwenCodeDefaultModelId: QwenCodeModelId = \"qwen3-coder-plus\"\r\n\r\nexport const qwenCodeModels = {\r\n\t\"qwen3-coder-plus\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 1_000_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0,\r\n\t\tdescription: \"Qwen3 Coder Plus - High-performance coding model with 1M context window for large codebases\",\r\n\t},\r\n\t\"qwen3-coder-flash\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 1_000_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0,\r\n\t\tdescription: \"Qwen3 Coder Flash - Fast coding model with 1M context window optimized for speed\",\r\n\t},\r\n} as const satisfies Record<QwenCodeModelId, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Requesty\r\n// https://requesty.ai/router-2\r\nexport const requestyDefaultModelId = \"coding/claude-4-sonnet\"\r\n\r\nexport const requestyDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"The best coding model, optimized by Requesty, and automatically routed to the fastest provider. Claude 4 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities.\",\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Roo provider with single model\r\nexport type RooModelId = \"xai/grok-code-fast-1\"\r\n\r\nexport const rooDefaultModelId: RooModelId = \"xai/grok-code-fast-1\"\r\n\r\nexport const rooModels = {\r\n\t\"xai/grok-code-fast-1\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 262_144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription:\r\n\t\t\t\"A reasoning model that is blazing fast and excels at agentic coding, accessible for free through Roo Code Cloud for a limited time. (Note: the free prompts and completions are logged by xAI and used to improve the model.)\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.sambanova.ai/cloud/docs/get-started/supported-models\r\nexport type SambaNovaModelId =\r\n\t| \"Meta-Llama-3.1-8B-Instruct\"\r\n\t| \"Meta-Llama-3.3-70B-Instruct\"\r\n\t| \"DeepSeek-R1\"\r\n\t| \"DeepSeek-V3-0324\"\r\n\t| \"DeepSeek-R1-Distill-Llama-70B\"\r\n\t| \"Llama-4-Maverick-17B-128E-Instruct\"\r\n\t| \"Llama-3.3-Swallow-70B-Instruct-v0.4\"\r\n\t| \"Qwen3-32B\"\r\n\r\nexport const sambaNovaDefaultModelId: SambaNovaModelId = \"Meta-Llama-3.3-70B-Instruct\"\r\n\r\nexport const sambaNovaModels = {\r\n\t\"Meta-Llama-3.1-8B-Instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 16384,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.2,\r\n\t\tdescription: \"Meta Llama 3.1 8B Instruct model with 16K context window.\",\r\n\t},\r\n\t\"Meta-Llama-3.3-70B-Instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 1.2,\r\n\t\tdescription: \"Meta Llama 3.3 70B Instruct model with 128K context window.\",\r\n\t},\r\n\t\"DeepSeek-R1\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 7.0,\r\n\t\tdescription: \"DeepSeek R1 reasoning model with 32K context window.\",\r\n\t},\r\n\t\"DeepSeek-V3-0324\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 4.5,\r\n\t\tdescription: \"DeepSeek V3 model with 32K context window.\",\r\n\t},\r\n\t\"DeepSeek-R1-Distill-Llama-70B\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.7,\r\n\t\toutputPrice: 1.4,\r\n\t\tdescription: \"DeepSeek R1 distilled Llama 70B model with 128K context window.\",\r\n\t},\r\n\t\"Llama-4-Maverick-17B-128E-Instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.63,\r\n\t\toutputPrice: 1.8,\r\n\t\tdescription: \"Meta Llama 4 Maverick 17B 128E Instruct model with 128K context window.\",\r\n\t},\r\n\t\"Llama-3.3-Swallow-70B-Instruct-v0.4\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 16384,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 1.2,\r\n\t\tdescription: \"Tokyotech Llama 3.3 Swallow 70B Instruct v0.4 model with 16K context window.\",\r\n\t},\r\n\t\"Qwen3-32B\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8192,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.4,\r\n\t\toutputPrice: 0.8,\r\n\t\tdescription: \"Alibaba Qwen 3 32B model with 8K context window.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport const unboundDefaultModelId = \"anthropic/claude-3-7-sonnet-20250219\"\r\n\r\nexport const unboundDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude\r\nexport type VertexModelId = keyof typeof vertexModels\r\n\r\nexport const vertexDefaultModelId: VertexModelId = \"claude-sonnet-4@20250514\"\r\n\r\nexport const vertexModels = {\r\n\t\"gemini-2.5-flash-preview-05-20:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-flash\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 2.5,\r\n\t\tcacheReadsPrice: 0.075,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-05-06\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-06-05\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-pro\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t\tmaxThinkingTokens: 32_768,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-exp-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-pro-exp-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.0-flash-lite-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.075,\r\n\t\toutputPrice: 0.3,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-01-21\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32_768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.075,\r\n\t\toutputPrice: 0.3,\r\n\t},\r\n\t\"gemini-1.5-pro-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.25,\r\n\t\toutputPrice: 5,\r\n\t},\r\n\t\"claude-sonnet-4@20250514\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-opus-4-1@20250805\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-opus-4@20250514\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-7-sonnet@20250219:thinking\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet@20250219\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-sonnet-v2@20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-sonnet@20240620\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-haiku@20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 5.0,\r\n\t\tcacheWritesPrice: 1.25,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t},\r\n\t\"claude-3-opus@20240229\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-haiku@20240307\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t\tcacheWritesPrice: 0.3,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t},\r\n\t\"gemini-2.5-flash-lite-preview-06-17\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"llama-4-maverick-17b-128e-instruct-maas\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.35,\r\n\t\toutputPrice: 1.15,\r\n\t\tdescription: \"Meta Llama 4 Maverick 17B Instruct model, 128K context.\",\r\n\t},\r\n\t\"deepseek-r1-0528-maas\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 163_840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.35,\r\n\t\toutputPrice: 5.4,\r\n\t\tdescription: \"DeepSeek R1 (0528). Available in us-central1\",\r\n\t},\r\n\t\"deepseek-v3.1-maas\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 163_840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 1.7,\r\n\t\tdescription: \"DeepSeek V3.1. Available in us-west2\",\r\n\t},\r\n\t\"gpt-oss-120b-maas\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tdescription: \"OpenAI gpt-oss 120B. Available in us-central1\",\r\n\t},\r\n\t\"gpt-oss-20b-maas\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.075,\r\n\t\toutputPrice: 0.3,\r\n\t\tdescription: \"OpenAI gpt-oss 20B. Available in us-central1\",\r\n\t},\r\n\t\"qwen3-coder-480b-a35b-instruct-maas\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 262_144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 4.0,\r\n\t\tdescription: \"Qwen3 Coder 480B A35B Instruct. Available in us-south1\",\r\n\t},\r\n\t\"qwen3-235b-a22b-instruct-2507-maas\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 262_144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.0,\r\n\t\tdescription: \"Qwen3 235B A22B Instruct. Available in us-south1\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const VERTEX_REGIONS = [\r\n\t{ value: \"global\", label: \"global\" },\r\n\t{ value: \"us-central1\", label: \"us-central1\" },\r\n\t{ value: \"us-east1\", label: \"us-east1\" },\r\n\t{ value: \"us-east4\", label: \"us-east4\" },\r\n\t{ value: \"us-east5\", label: \"us-east5\" },\r\n\t{ value: \"us-south1\", label: \"us-south1\" },\r\n\t{ value: \"us-west1\", label: \"us-west1\" },\r\n\t{ value: \"us-west2\", label: \"us-west2\" },\r\n\t{ value: \"us-west3\", label: \"us-west3\" },\r\n\t{ value: \"us-west4\", label: \"us-west4\" },\r\n\t{ value: \"northamerica-northeast1\", label: \"northamerica-northeast1\" },\r\n\t{ value: \"northamerica-northeast2\", label: \"northamerica-northeast2\" },\r\n\t{ value: \"southamerica-east1\", label: \"southamerica-east1\" },\r\n\t{ value: \"europe-west1\", label: \"europe-west1\" },\r\n\t{ value: \"europe-west2\", label: \"europe-west2\" },\r\n\t{ value: \"europe-west3\", label: \"europe-west3\" },\r\n\t{ value: \"europe-west4\", label: \"europe-west4\" },\r\n\t{ value: \"europe-west6\", label: \"europe-west6\" },\r\n\t{ value: \"europe-central2\", label: \"europe-central2\" },\r\n\t{ value: \"asia-east1\", label: \"asia-east1\" },\r\n\t{ value: \"asia-east2\", label: \"asia-east2\" },\r\n\t{ value: \"asia-northeast1\", label: \"asia-northeast1\" },\r\n\t{ value: \"asia-northeast2\", label: \"asia-northeast2\" },\r\n\t{ value: \"asia-northeast3\", label: \"asia-northeast3\" },\r\n\t{ value: \"asia-south1\", label: \"asia-south1\" },\r\n\t{ value: \"asia-south2\", label: \"asia-south2\" },\r\n\t{ value: \"asia-southeast1\", label: \"asia-southeast1\" },\r\n\t{ value: \"asia-southeast2\", label: \"asia-southeast2\" },\r\n\t{ value: \"australia-southeast1\", label: \"australia-southeast1\" },\r\n\t{ value: \"australia-southeast2\", label: \"australia-southeast2\" },\r\n\t{ value: \"me-west1\", label: \"me-west1\" },\r\n\t{ value: \"me-central1\", label: \"me-central1\" },\r\n\t{ value: \"africa-south1\", label: \"africa-south1\" },\r\n]\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type VscodeLlmModelId = keyof typeof vscodeLlmModels\r\n\r\nexport const vscodeLlmDefaultModelId: VscodeLlmModelId = \"claude-3.5-sonnet\"\r\n\r\n// https://docs.cline.bot/provider-config/vscode-language-model-api\r\nexport const vscodeLlmModels = {\r\n\t\"gpt-3.5-turbo\": {\r\n\t\tcontextWindow: 12114,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-3.5-turbo\",\r\n\t\tversion: \"gpt-3.5-turbo-0613\",\r\n\t\tname: \"GPT 3.5 Turbo\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 12114,\r\n\t},\r\n\t\"gpt-4o-mini\": {\r\n\t\tcontextWindow: 12115,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4o-mini\",\r\n\t\tversion: \"gpt-4o-mini-2024-07-18\",\r\n\t\tname: \"GPT-4o mini\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 12115,\r\n\t},\r\n\t\"gpt-4\": {\r\n\t\tcontextWindow: 28501,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4\",\r\n\t\tversion: \"gpt-4-0613\",\r\n\t\tname: \"GPT 4\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 28501,\r\n\t},\r\n\t\"gpt-4-0125-preview\": {\r\n\t\tcontextWindow: 63826,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4-turbo\",\r\n\t\tversion: \"gpt-4-0125-preview\",\r\n\t\tname: \"GPT 4 Turbo\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63826,\r\n\t},\r\n\t\"gpt-4o\": {\r\n\t\tcontextWindow: 63827,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4o\",\r\n\t\tversion: \"gpt-4o-2024-11-20\",\r\n\t\tname: \"GPT-4o\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63827,\r\n\t},\r\n\to1: {\r\n\t\tcontextWindow: 19827,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o1-ga\",\r\n\t\tversion: \"o1-2024-12-17\",\r\n\t\tname: \"o1 (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 19827,\r\n\t},\r\n\t\"o3-mini\": {\r\n\t\tcontextWindow: 63827,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o3-mini\",\r\n\t\tversion: \"o3-mini-2025-01-31\",\r\n\t\tname: \"o3-mini\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63827,\r\n\t},\r\n\t\"claude-3.5-sonnet\": {\r\n\t\tcontextWindow: 81638,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"claude-3.5-sonnet\",\r\n\t\tversion: \"claude-3.5-sonnet\",\r\n\t\tname: \"Claude 3.5 Sonnet\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 81638,\r\n\t},\r\n\t\"claude-4-sonnet\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"claude-sonnet-4\",\r\n\t\tversion: \"claude-sonnet-4\",\r\n\t\tname: \"Claude Sonnet 4\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 111836,\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tcontextWindow: 127827,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gemini-2.0-flash\",\r\n\t\tversion: \"gemini-2.0-flash-001\",\r\n\t\tname: \"Gemini 2.0 Flash\",\r\n\t\tsupportsToolCalling: false,\r\n\t\tmaxInputTokens: 127827,\r\n\t},\r\n\t\"gemini-2.5-pro\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gemini-2.5-pro\",\r\n\t\tversion: \"gemini-2.5-pro-preview-03-25\",\r\n\t\tname: \"Gemini 2.5 Pro (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 108637,\r\n\t},\r\n\t\"o4-mini\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o4-mini\",\r\n\t\tversion: \"o4-mini-2025-04-16\",\r\n\t\tname: \"o4-mini (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 111452,\r\n\t},\r\n\t\"gpt-4.1\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4.1\",\r\n\t\tversion: \"gpt-4.1-2025-04-14\",\r\n\t\tname: \"GPT-4.1 (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 111452,\r\n\t},\r\n\t\"gpt-5-mini\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-5-mini\",\r\n\t\tversion: \"gpt-5-mini\",\r\n\t\tname: \"GPT-5 mini (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 108637,\r\n\t},\r\n\t\"gpt-5\": {\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-5\",\r\n\t\tversion: \"gpt-5\",\r\n\t\tname: \"GPT-5 (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 108637,\r\n\t},\r\n} as const satisfies Record<\r\n\tstring,\r\n\tModelInfo & {\r\n\t\tfamily: string\r\n\t\tversion: string\r\n\t\tname: string\r\n\t\tsupportsToolCalling: boolean\r\n\t\tmaxInputTokens: number\r\n\t}\r\n>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.x.ai/docs/api-reference\r\nexport type XAIModelId = keyof typeof xaiModels\r\n\r\nexport const xaiDefaultModelId: XAIModelId = \"grok-code-fast-1\"\r\n\r\nexport const xaiModels = {\r\n\t\"grok-code-fast-1\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 262_144,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 1.5,\r\n\t\tcacheWritesPrice: 0.02,\r\n\t\tcacheReadsPrice: 0.02,\r\n\t\tdescription: \"xAI's Grok Code Fast model with 256K context window\",\r\n\t},\r\n\t\"grok-4\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 256000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 0.75,\r\n\t\tcacheReadsPrice: 0.75,\r\n\t\tdescription: \"xAI's Grok-4 model with 256K context window\",\r\n\t},\r\n\t\"grok-3\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 0.75,\r\n\t\tcacheReadsPrice: 0.75,\r\n\t\tdescription: \"xAI's Grok-3 model with 128K context window\",\r\n\t},\r\n\t\"grok-3-fast\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 25.0,\r\n\t\tcacheWritesPrice: 1.25,\r\n\t\tcacheReadsPrice: 1.25,\r\n\t\tdescription: \"xAI's Grok-3 fast model with 128K context window\",\r\n\t},\r\n\t\"grok-3-mini\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.5,\r\n\t\tcacheWritesPrice: 0.07,\r\n\t\tcacheReadsPrice: 0.07,\r\n\t\tdescription: \"xAI's Grok-3 mini model with 128K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-3-mini-fast\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 4.0,\r\n\t\tcacheWritesPrice: 0.15,\r\n\t\tcacheReadsPrice: 0.15,\r\n\t\tdescription: \"xAI's Grok-3 mini fast model with 128K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-2-1212\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 model (version 1212) with 128K context window\",\r\n\t},\r\n\t\"grok-2-vision-1212\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 Vision model (version 1212) with image support and 32K context window\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://ai-gateway.vercel.sh/v1/\r\nexport const vercelAiGatewayDefaultModelId = \"anthropic/claude-sonnet-4\"\r\n\r\nexport const VERCEL_AI_GATEWAY_PROMPT_CACHING_MODELS = new Set([\r\n\t\"anthropic/claude-3-haiku\",\r\n\t\"anthropic/claude-3-opus\",\r\n\t\"anthropic/claude-3.5-haiku\",\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-opus-4.1\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"openai/gpt-4.1\",\r\n\t\"openai/gpt-4.1-mini\",\r\n\t\"openai/gpt-4.1-nano\",\r\n\t\"openai/gpt-4o\",\r\n\t\"openai/gpt-4o-mini\",\r\n\t\"openai/gpt-5\",\r\n\t\"openai/gpt-5-mini\",\r\n\t\"openai/gpt-5-nano\",\r\n\t\"openai/o1\",\r\n\t\"openai/o3\",\r\n\t\"openai/o3-mini\",\r\n\t\"openai/o4-mini\",\r\n])\r\n\r\nexport const VERCEL_AI_GATEWAY_VISION_ONLY_MODELS = new Set([\r\n\t\"alibaba/qwen-3-14b\",\r\n\t\"alibaba/qwen-3-235b\",\r\n\t\"alibaba/qwen-3-30b\",\r\n\t\"alibaba/qwen-3-32b\",\r\n\t\"alibaba/qwen3-coder\",\r\n\t\"amazon/nova-pro\",\r\n\t\"anthropic/claude-3.5-haiku\",\r\n\t\"google/gemini-1.5-flash-8b\",\r\n\t\"google/gemini-2.0-flash-thinking\",\r\n\t\"google/gemma-3-27b\",\r\n\t\"mistral/devstral-small\",\r\n\t\"xai/grok-vision-beta\",\r\n])\r\n\r\nexport const VERCEL_AI_GATEWAY_VISION_AND_TOOLS_MODELS = new Set([\r\n\t\"amazon/nova-lite\",\r\n\t\"anthropic/claude-3-haiku\",\r\n\t\"anthropic/claude-3-opus\",\r\n\t\"anthropic/claude-3-sonnet\",\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-opus-4.1\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"google/gemini-1.5-flash\",\r\n\t\"google/gemini-1.5-pro\",\r\n\t\"google/gemini-2.0-flash\",\r\n\t\"google/gemini-2.0-flash-lite\",\r\n\t\"google/gemini-2.0-pro\",\r\n\t\"google/gemini-2.5-flash\",\r\n\t\"google/gemini-2.5-flash-lite\",\r\n\t\"google/gemini-2.5-pro\",\r\n\t\"google/gemini-exp\",\r\n\t\"meta/llama-3.2-11b\",\r\n\t\"meta/llama-3.2-90b\",\r\n\t\"meta/llama-3.3\",\r\n\t\"meta/llama-4-maverick\",\r\n\t\"meta/llama-4-scout\",\r\n\t\"mistral/pixtral-12b\",\r\n\t\"mistral/pixtral-large\",\r\n\t\"moonshotai/kimi-k2\",\r\n\t\"openai/gpt-4-turbo\",\r\n\t\"openai/gpt-4.1\",\r\n\t\"openai/gpt-4.1-mini\",\r\n\t\"openai/gpt-4.1-nano\",\r\n\t\"openai/gpt-4.5-preview\",\r\n\t\"openai/gpt-4o\",\r\n\t\"openai/gpt-4o-mini\",\r\n\t\"openai/gpt-oss-120b\",\r\n\t\"openai/gpt-oss-20b\",\r\n\t\"openai/o3\",\r\n\t\"openai/o3-pro\",\r\n\t\"openai/o4-mini\",\r\n\t\"vercel/v0-1.0-md\",\r\n\t\"xai/grok-2-vision\",\r\n\t\"zai/glm-4.5v\",\r\n])\r\n\r\nexport const vercelAiGatewayDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 64000,\r\n\tcontextWindow: 200000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3,\r\n\toutputPrice: 15,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"Claude Sonnet 4 significantly improves on Sonnet 3.7's industry-leading capabilities, excelling in coding with a state-of-the-art 72.7% on SWE-bench. The model balances performance and efficiency for internal and external use cases, with enhanced steerability for greater control over implementations. While not matching Opus 4 in most domains, it delivers an optimal mix of capability and practicality.\",\r\n}\r\n\r\nexport const VERCEL_AI_GATEWAY_DEFAULT_TEMPERATURE = 0.7\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Z AI\r\n// https://docs.z.ai/guides/llm/glm-4.5\r\n// https://docs.z.ai/guides/overview/pricing\r\n\r\nexport type InternationalZAiModelId = keyof typeof internationalZAiModels\r\nexport const internationalZAiDefaultModelId: InternationalZAiModelId = \"glm-4.5\"\r\nexport const internationalZAiModels = {\r\n\t\"glm-4.5\": {\r\n\t\tmaxTokens: 98_304,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 2.2,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0.11,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5 is <PERSON><PERSON><PERSON>'s latest featured model. Its comprehensive capabilities in reasoning, coding, and agent reach the state-of-the-art (SOTA) level among open-source models, with a context length of up to 128k.\",\r\n\t},\r\n\t\"glm-4.5-air\": {\r\n\t\tmaxTokens: 98_304,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 1.1,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5-Air is the lightweight version of GLM-4.5. It balances performance and cost-effectiveness, and can flexibly switch to hybrid thinking models.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport type MainlandZAiModelId = keyof typeof mainlandZAiModels\r\nexport const mainlandZAiDefaultModelId: MainlandZAiModelId = \"glm-4.5\"\r\nexport const mainlandZAiModels = {\r\n\t\"glm-4.5\": {\r\n\t\tmaxTokens: 98_304,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.29,\r\n\t\toutputPrice: 1.14,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0.057,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5 is Zhipu's latest featured model. Its comprehensive capabilities in reasoning, coding, and agent reach the state-of-the-art (SOTA) level among open-source models, with a context length of up to 128k.\",\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 32_000,\r\n\t\t\t\tinputPrice: 0.21,\r\n\t\t\t\toutputPrice: 1.0,\r\n\t\t\t\tcacheReadsPrice: 0.043,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 128_000,\r\n\t\t\t\tinputPrice: 0.29,\r\n\t\t\t\toutputPrice: 1.14,\r\n\t\t\t\tcacheReadsPrice: 0.057,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 0.29,\r\n\t\t\t\toutputPrice: 1.14,\r\n\t\t\t\tcacheReadsPrice: 0.057,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"glm-4.5-air\": {\r\n\t\tmaxTokens: 98_304,\r\n\t\tcontextWindow: 131_072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheWritesPrice: 0,\r\n\t\tcacheReadsPrice: 0.02,\r\n\t\tdescription:\r\n\t\t\t\"GLM-4.5-Air is the lightweight version of GLM-4.5. It balances performance and cost-effectiveness, and can flexibly switch to hybrid thinking models.\",\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 32_000,\r\n\t\t\t\tinputPrice: 0.07,\r\n\t\t\t\toutputPrice: 0.4,\r\n\t\t\t\tcacheReadsPrice: 0.014,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 128_000,\r\n\t\t\t\tinputPrice: 0.1,\r\n\t\t\t\toutputPrice: 0.6,\r\n\t\t\t\tcacheReadsPrice: 0.02,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 0.1,\r\n\t\t\t\toutputPrice: 0.6,\r\n\t\t\t\tcacheReadsPrice: 0.02,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const ZAI_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Default fallback values for DeepInfra when model metadata is not yet loaded.\r\nexport const deepInfraDefaultModelId = \"Qwen/Qwen3-Coder-480B-A35B-Instruct-Turbo\"\r\n\r\nexport const deepInfraDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 16384,\r\n\tcontextWindow: 262144,\r\n\tsupportsImages: false,\r\n\tsupportsPromptCache: false,\r\n\tinputPrice: 0.3,\r\n\toutputPrice: 1.2,\r\n\tdescription: \"Qwen 3 Coder 480B A35B Instruct Turbo model, 256K context.\",\r\n}\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * HistoryItem\r\n */\r\n\r\nexport const historyItemSchema = z.object({\r\n\tid: z.string(),\r\n\trootTaskId: z.string().optional(),\r\n\tparentTaskId: z.string().optional(),\r\n\tnumber: z.number(),\r\n\tts: z.number(),\r\n\ttask: z.string(),\r\n\ttokensIn: z.number(),\r\n\ttokensOut: z.number(),\r\n\tcacheWrites: z.number().optional(),\r\n\tcacheReads: z.number().optional(),\r\n\ttotalCost: z.number(),\r\n\tsize: z.number().optional(),\r\n\tworkspace: z.string().optional(),\r\n\tmode: z.string().optional(),\r\n})\r\n\r\nexport type HistoryItem = z.infer<typeof historyItemSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport type { Keys, Equals, AssertEqual } from \"./type-fu.js\"\r\n\r\n/**\r\n * ExperimentId\r\n */\r\n\r\nexport const experimentIds = [\r\n\t\"powerSteering\",\r\n\t\"multiFileApplyDiff\",\r\n\t\"preventFocusDisruption\",\r\n\t\"imageGeneration\",\r\n\t\"runSlashCommand\",\r\n] as const\r\n\r\nexport const experimentIdsSchema = z.enum(experimentIds)\r\n\r\nexport type ExperimentId = z.infer<typeof experimentIdsSchema>\r\n\r\n/**\r\n * Experiments\r\n */\r\n\r\nexport const experimentsSchema = z.object({\r\n\tpowerSteering: z.boolean().optional(),\r\n\tmultiFileApplyDiff: z.boolean().optional(),\r\n\tpreventFocusDisruption: z.boolean().optional(),\r\n\timageGeneration: z.boolean().optional(),\r\n\trunSlashCommand: z.boolean().optional(),\r\n})\r\n\r\nexport type Experiments = z.infer<typeof experimentsSchema>\r\n\r\ntype _AssertExperiments = AssertEqual<Equals<ExperimentId, Keys<Experiments>>>\r\n", "import { z } from \"zod\"\r\n\r\nimport { providerNames } from \"./provider-settings.js\"\r\nimport { clineMessageSchema } from \"./message.js\"\r\n\r\n/**\r\n * TelemetrySetting\r\n */\r\n\r\nexport const telemetrySettings = [\"unset\", \"enabled\", \"disabled\"] as const\r\n\r\nexport const telemetrySettingsSchema = z.enum(telemetrySettings)\r\n\r\nexport type TelemetrySetting = z.infer<typeof telemetrySettingsSchema>\r\n\r\n/**\r\n * TelemetryEventName\r\n */\r\n\r\nexport enum TelemetryEventName {\r\n\tTASK_CREATED = \"Task Created\",\r\n\tTASK_RESTARTED = \"Task Reopened\",\r\n\tTASK_COMPLETED = \"Task Completed\",\r\n\tTASK_MESSAGE = \"Task Message\",\r\n\tTASK_CONVERSATION_MESSAGE = \"Conversation Message\",\r\n\tLLM_COMPLETION = \"LLM Completion\",\r\n\tMODE_SWITCH = \"Mode Switched\",\r\n\tMODE_SELECTOR_OPENED = \"Mode Selector Opened\",\r\n\tTOOL_USED = \"Tool Used\",\r\n\r\n\tCHECKPOINT_CREATED = \"Checkpoint Created\",\r\n\tCHECKPOINT_RESTORED = \"Checkpoint Restored\",\r\n\tCHECKPOINT_DIFFED = \"Checkpoint Diffed\",\r\n\r\n\tTAB_SHOWN = \"Tab Shown\",\r\n\tMODE_SETTINGS_CHANGED = \"Mode Setting Changed\",\r\n\tCUSTOM_MODE_CREATED = \"Custom Mode Created\",\r\n\r\n\tCONTEXT_CONDENSED = \"Context Condensed\",\r\n\tSLIDING_WINDOW_TRUNCATION = \"Sliding Window Truncation\",\r\n\r\n\tCODE_ACTION_USED = \"Code Action Used\",\r\n\tPROMPT_ENHANCED = \"Prompt Enhanced\",\r\n\r\n\tTITLE_BUTTON_CLICKED = \"Title Button Clicked\",\r\n\r\n\tAUTHENTICATION_INITIATED = \"Authentication Initiated\",\r\n\r\n\tMARKETPLACE_ITEM_INSTALLED = \"Marketplace Item Installed\",\r\n\tMARKETPLACE_ITEM_REMOVED = \"Marketplace Item Removed\",\r\n\tMARKETPLACE_TAB_VIEWED = \"Marketplace Tab Viewed\",\r\n\tMARKETPLACE_INSTALL_BUTTON_CLICKED = \"Marketplace Install Button Clicked\",\r\n\r\n\tSHARE_BUTTON_CLICKED = \"Share Button Clicked\",\r\n\tSHARE_ORGANIZATION_CLICKED = \"Share Organization Clicked\",\r\n\tSHARE_PUBLIC_CLICKED = \"Share Public Clicked\",\r\n\tSHARE_CONNECT_TO_CLOUD_CLICKED = \"Share Connect To Cloud Clicked\",\r\n\r\n\tACCOUNT_CONNECT_CLICKED = \"Account Connect Clicked\",\r\n\tACCOUNT_CONNECT_SUCCESS = \"Account Connect Success\",\r\n\tACCOUNT_LOGOUT_CLICKED = \"Account Logout Clicked\",\r\n\tACCOUNT_LOGOUT_SUCCESS = \"Account Logout Success\",\r\n\r\n\tSCHEMA_VALIDATION_ERROR = \"Schema Validation Error\",\r\n\tDIFF_APPLICATION_ERROR = \"Diff Application Error\",\r\n\tSHELL_INTEGRATION_ERROR = \"Shell Integration Error\",\r\n\tCONSECUTIVE_MISTAKE_ERROR = \"Consecutive Mistake Error\",\r\n\tCODE_INDEX_ERROR = \"Code Index Error\",\r\n}\r\n\r\n/**\r\n * TelemetryProperties\r\n */\r\n\r\nexport const staticAppPropertiesSchema = z.object({\r\n\tappName: z.string(),\r\n\tappVersion: z.string(),\r\n\tvscodeVersion: z.string(),\r\n\tplatform: z.string(),\r\n\teditorName: z.string(),\r\n\thostname: z.string().optional(),\r\n})\r\n\r\nexport type StaticAppProperties = z.infer<typeof staticAppPropertiesSchema>\r\n\r\nexport const dynamicAppPropertiesSchema = z.object({\r\n\tlanguage: z.string(),\r\n\tmode: z.string(),\r\n})\r\n\r\nexport type DynamicAppProperties = z.infer<typeof dynamicAppPropertiesSchema>\r\n\r\nexport const cloudAppPropertiesSchema = z.object({\r\n\tcloudIsAuthenticated: z.boolean().optional(),\r\n})\r\n\r\nexport type CloudAppProperties = z.infer<typeof cloudAppPropertiesSchema>\r\n\r\nexport const appPropertiesSchema = z.object({\r\n\t...staticAppPropertiesSchema.shape,\r\n\t...dynamicAppPropertiesSchema.shape,\r\n\t...cloudAppPropertiesSchema.shape,\r\n})\r\n\r\nexport type AppProperties = z.infer<typeof appPropertiesSchema>\r\n\r\nexport const taskPropertiesSchema = z.object({\r\n\ttaskId: z.string().optional(),\r\n\tapiProvider: z.enum(providerNames).optional(),\r\n\tmodelId: z.string().optional(),\r\n\tdiffStrategy: z.string().optional(),\r\n\tisSubtask: z.boolean().optional(),\r\n\ttodos: z\r\n\t\t.object({\r\n\t\t\ttotal: z.number(),\r\n\t\t\tcompleted: z.number(),\r\n\t\t\tinProgress: z.number(),\r\n\t\t\tpending: z.number(),\r\n\t\t})\r\n\t\t.optional(),\r\n})\r\n\r\nexport type TaskProperties = z.infer<typeof taskPropertiesSchema>\r\n\r\nexport const gitPropertiesSchema = z.object({\r\n\trepositoryUrl: z.string().optional(),\r\n\trepositoryName: z.string().optional(),\r\n\tdefaultBranch: z.string().optional(),\r\n})\r\n\r\nexport type GitProperties = z.infer<typeof gitPropertiesSchema>\r\n\r\nexport const telemetryPropertiesSchema = z.object({\r\n\t...appPropertiesSchema.shape,\r\n\t...taskPropertiesSchema.shape,\r\n\t...gitPropertiesSchema.shape,\r\n})\r\n\r\nexport type TelemetryProperties = z.infer<typeof telemetryPropertiesSchema>\r\n\r\n/**\r\n * TelemetryEvent\r\n */\r\n\r\nexport type TelemetryEvent = {\r\n\tevent: TelemetryEventName\r\n\t// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n\tproperties?: Record<string, any>\r\n}\r\n\r\n/**\r\n * RooCodeTelemetryEvent\r\n */\r\n\r\nexport const rooCodeTelemetryEventSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.enum([\r\n\t\t\tTelemetryEventName.TASK_CREATED,\r\n\t\t\tTelemetryEventName.TASK_RESTARTED,\r\n\t\t\tTelemetryEventName.TASK_COMPLETED,\r\n\t\t\tTelemetryEventName.TASK_CONVERSATION_MESSAGE,\r\n\t\t\tTelemetryEventName.MODE_SWITCH,\r\n\t\t\tTelemetryEventName.MODE_SELECTOR_OPENED,\r\n\t\t\tTelemetryEventName.TOOL_USED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_CREATED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_RESTORED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_DIFFED,\r\n\t\t\tTelemetryEventName.CODE_ACTION_USED,\r\n\t\t\tTelemetryEventName.PROMPT_ENHANCED,\r\n\t\t\tTelemetryEventName.TITLE_BUTTON_CLICKED,\r\n\t\t\tTelemetryEventName.AUTHENTICATION_INITIATED,\r\n\t\t\tTelemetryEventName.MARKETPLACE_ITEM_INSTALLED,\r\n\t\t\tTelemetryEventName.MARKETPLACE_ITEM_REMOVED,\r\n\t\t\tTelemetryEventName.MARKETPLACE_TAB_VIEWED,\r\n\t\t\tTelemetryEventName.MARKETPLACE_INSTALL_BUTTON_CLICKED,\r\n\t\t\tTelemetryEventName.SHARE_BUTTON_CLICKED,\r\n\t\t\tTelemetryEventName.SHARE_ORGANIZATION_CLICKED,\r\n\t\t\tTelemetryEventName.SHARE_PUBLIC_CLICKED,\r\n\t\t\tTelemetryEventName.SHARE_CONNECT_TO_CLOUD_CLICKED,\r\n\t\t\tTelemetryEventName.ACCOUNT_CONNECT_CLICKED,\r\n\t\t\tTelemetryEventName.ACCOUNT_CONNECT_SUCCESS,\r\n\t\t\tTelemetryEventName.ACCOUNT_LOGOUT_CLICKED,\r\n\t\t\tTelemetryEventName.ACCOUNT_LOGOUT_SUCCESS,\r\n\t\t\tTelemetryEventName.SCHEMA_VALIDATION_ERROR,\r\n\t\t\tTelemetryEventName.DIFF_APPLICATION_ERROR,\r\n\t\t\tTelemetryEventName.SHELL_INTEGRATION_ERROR,\r\n\t\t\tTelemetryEventName.CONSECUTIVE_MISTAKE_ERROR,\r\n\t\t\tTelemetryEventName.CODE_INDEX_ERROR,\r\n\t\t\tTelemetryEventName.CONTEXT_CONDENSED,\r\n\t\t\tTelemetryEventName.SLIDING_WINDOW_TRUNCATION,\r\n\t\t\tTelemetryEventName.TAB_SHOWN,\r\n\t\t\tTelemetryEventName.MODE_SETTINGS_CHANGED,\r\n\t\t\tTelemetryEventName.CUSTOM_MODE_CREATED,\r\n\t\t]),\r\n\t\tproperties: telemetryPropertiesSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TelemetryEventName.TASK_MESSAGE),\r\n\t\tproperties: z.object({\r\n\t\t\t...telemetryPropertiesSchema.shape,\r\n\t\t\ttaskId: z.string(),\r\n\t\t\tmessage: clineMessageSchema,\r\n\t\t}),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TelemetryEventName.LLM_COMPLETION),\r\n\t\tproperties: z.object({\r\n\t\t\t...telemetryPropertiesSchema.shape,\r\n\t\t\tinputTokens: z.number(),\r\n\t\t\toutputTokens: z.number(),\r\n\t\t\tcacheReadTokens: z.number().optional(),\r\n\t\t\tcacheWriteTokens: z.number().optional(),\r\n\t\t\tcost: z.number().optional(),\r\n\t\t}),\r\n\t}),\r\n])\r\n\r\nexport type RooCodeTelemetryEvent = z.infer<typeof rooCodeTelemetryEventSchema>\r\n\r\n/**\r\n * TelemetryEventSubscription\r\n */\r\n\r\nexport type TelemetryEventSubscription =\r\n\t| { type: \"include\"; events: TelemetryEventName[] }\r\n\t| { type: \"exclude\"; events: TelemetryEventName[] }\r\n\r\n/**\r\n * TelemetryPropertiesProvider\r\n */\r\n\r\nexport interface TelemetryPropertiesProvider {\r\n\tgetTelemetryProperties(): Promise<TelemetryProperties>\r\n}\r\n\r\n/**\r\n * TelemetryClient\r\n */\r\n\r\nexport interface TelemetryClient {\r\n\tsubscription?: TelemetryEventSubscription\r\n\r\n\tsetProvider(provider: TelemetryPropertiesProvider): void\r\n\tcapture(options: TelemetryEvent): Promise<void>\r\n\tupdateTelemetryState(didUserOptIn: boolean): void\r\n\tisTelemetryEnabled(): boolean\r\n\tshutdown(): Promise<void>\r\n}\r\n", "import { z } from \"zod\"\r\n\r\nimport { toolGroupsSchema } from \"./tool.js\"\r\n\r\n/**\r\n * GroupOptions\r\n */\r\n\r\nexport const groupOptionsSchema = z.object({\r\n\tfileRegex: z\r\n\t\t.string()\r\n\t\t.optional()\r\n\t\t.refine(\r\n\t\t\t(pattern) => {\r\n\t\t\t\tif (!pattern) {\r\n\t\t\t\t\treturn true // Optional, so empty is valid.\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tnew RegExp(pattern)\r\n\t\t\t\t\treturn true\r\n\t\t\t\t} catch {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t{ message: \"Invalid regular expression pattern\" },\r\n\t\t),\r\n\tdescription: z.string().optional(),\r\n})\r\n\r\nexport type GroupOptions = z.infer<typeof groupOptionsSchema>\r\n\r\n/**\r\n * GroupEntry\r\n */\r\n\r\nexport const groupEntrySchema = z.union([toolGroupsSchema, z.tuple([toolGroupsSchema, groupOptionsSchema])])\r\n\r\nexport type GroupEntry = z.infer<typeof groupEntrySchema>\r\n\r\n/**\r\n * ModeConfig\r\n */\r\n\r\nconst groupEntryArraySchema = z.array(groupEntrySchema).refine(\r\n\t(groups) => {\r\n\t\tconst seen = new Set()\r\n\r\n\t\treturn groups.every((group) => {\r\n\t\t\t// For tuples, check the group name (first element).\r\n\t\t\tconst groupName = Array.isArray(group) ? group[0] : group\r\n\r\n\t\t\tif (seen.has(groupName)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\r\n\t\t\tseen.add(groupName)\r\n\t\t\treturn true\r\n\t\t})\r\n\t},\r\n\t{ message: \"Duplicate groups are not allowed\" },\r\n)\r\n\r\nexport const modeConfigSchema = z.object({\r\n\tslug: z.string().regex(/^[a-zA-Z0-9-]+$/, \"Slug must contain only letters numbers and dashes\"),\r\n\tname: z.string().min(1, \"Name is required\"),\r\n\troleDefinition: z.string().min(1, \"Role definition is required\"),\r\n\twhenToUse: z.string().optional(),\r\n\tdescription: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n\tgroups: groupEntryArraySchema,\r\n\tsource: z.enum([\"global\", \"project\"]).optional(),\r\n})\r\n\r\nexport type ModeConfig = z.infer<typeof modeConfigSchema>\r\n\r\n/**\r\n * CustomModesSettings\r\n */\r\n\r\nexport const customModesSettingsSchema = z.object({\r\n\tcustomModes: z.array(modeConfigSchema).refine(\r\n\t\t(modes) => {\r\n\t\t\tconst slugs = new Set()\r\n\r\n\t\t\treturn modes.every((mode) => {\r\n\t\t\t\tif (slugs.has(mode.slug)) {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tslugs.add(mode.slug)\r\n\t\t\t\treturn true\r\n\t\t\t})\r\n\t\t},\r\n\t\t{\r\n\t\t\tmessage: \"Duplicate mode slugs are not allowed\",\r\n\t\t},\r\n\t),\r\n})\r\n\r\nexport type CustomModesSettings = z.infer<typeof customModesSettingsSchema>\r\n\r\n/**\r\n * PromptComponent\r\n */\r\n\r\nexport const promptComponentSchema = z.object({\r\n\troleDefinition: z.string().optional(),\r\n\twhenToUse: z.string().optional(),\r\n\tdescription: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n})\r\n\r\nexport type PromptComponent = z.infer<typeof promptComponentSchema>\r\n\r\n/**\r\n * CustomModePrompts\r\n */\r\n\r\nexport const customModePromptsSchema = z.record(z.string(), promptComponentSchema.optional())\r\n\r\nexport type CustomModePrompts = z.infer<typeof customModePromptsSchema>\r\n\r\n/**\r\n * CustomSupportPrompts\r\n */\r\n\r\nexport const customSupportPromptsSchema = z.record(z.string(), z.string().optional())\r\n\r\nexport type CustomSupportPrompts = z.infer<typeof customSupportPromptsSchema>\r\n\r\n/**\r\n * DEFAULT_MODES\r\n */\r\n\r\nexport const DEFAULT_MODES: readonly ModeConfig[] = [\r\n\t{\r\n\t\tslug: \"architect\",\r\n\t\tname: \"🏗️ Architect\",\r\n\t\troleDefinition:\r\n\t\t\t\"You are Roo, an experienced technical leader who is inquisitive and an excellent planner. Your goal is to gather information and get context to create a detailed plan for accomplishing the user's task, which the user will review and approve before they switch into another mode to implement the solution.\",\r\n\t\twhenToUse:\r\n\t\t\t\"Use this mode when you need to plan, design, or strategize before implementation. Perfect for breaking down complex problems, creating technical specifications, designing system architecture, or brainstorming solutions before coding.\",\r\n\t\tdescription: \"Plan and design before implementation\",\r\n\t\tgroups: [\"read\", [\"edit\", { fileRegex: \"\\\\.md$\", description: \"Markdown files only\" }], \"browser\", \"mcp\"],\r\n\t\tcustomInstructions:\r\n\t\t\t\"1. Do some information gathering (using provided tools) to get more context about the task.\\n\\n2. You should also ask the user clarifying questions to get a better understanding of the task.\\n\\n3. Once you've gained more context about the user's request, break down the task into clear, actionable steps and create a todo list using the `update_todo_list` tool. Each todo item should be:\\n   - Specific and actionable\\n   - Listed in logical execution order\\n   - Focused on a single, well-defined outcome\\n   - Clear enough that another mode could execute it independently\\n\\n   **Note:** If the `update_todo_list` tool is not available, write the plan to a markdown file (e.g., `plan.md` or `todo.md`) instead.\\n\\n4. As you gather more information or discover new requirements, update the todo list to reflect the current understanding of what needs to be accomplished.\\n\\n5. Ask the user if they are pleased with this plan, or if they would like to make any changes. Think of this as a brainstorming session where you can discuss the task and refine the todo list.\\n\\n6. Include Mermaid diagrams if they help clarify complex workflows or system architecture. Please avoid using double quotes (\\\"\\\") and parentheses () inside square brackets ([]) in Mermaid diagrams, as this can cause parsing errors.\\n\\n7. Use the switch_mode tool to request that the user switch to another mode to implement the solution.\\n\\n**IMPORTANT: Focus on creating clear, actionable todo lists rather than lengthy markdown documents. Use the todo list as your primary planning tool to track and organize the work that needs to be done.**\",\r\n\t},\r\n\t{\r\n\t\tslug: \"code\",\r\n\t\tname: \"💻 Code\",\r\n\t\troleDefinition:\r\n\t\t\t\"You are Roo, a highly skilled software engineer with extensive knowledge in many programming languages, frameworks, design patterns, and best practices.\",\r\n\t\twhenToUse:\r\n\t\t\t\"Use this mode when you need to write, modify, or refactor code. Ideal for implementing features, fixing bugs, creating new files, or making code improvements across any programming language or framework.\",\r\n\t\tdescription: \"Write, modify, and refactor code\",\r\n\t\tgroups: [\"read\", \"edit\", \"browser\", \"command\", \"mcp\"],\r\n\t},\r\n\t{\r\n\t\tslug: \"ask\",\r\n\t\tname: \"❓ Ask\",\r\n\t\troleDefinition:\r\n\t\t\t\"You are Roo, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.\",\r\n\t\twhenToUse:\r\n\t\t\t\"Use this mode when you need explanations, documentation, or answers to technical questions. Best for understanding concepts, analyzing existing code, getting recommendations, or learning about technologies without making changes.\",\r\n\t\tdescription: \"Get answers and explanations\",\r\n\t\tgroups: [\"read\", \"browser\", \"mcp\"],\r\n\t\tcustomInstructions:\r\n\t\t\t\"You can analyze code, explain concepts, and access external resources. Always answer the user's questions thoroughly, and do not switch to implementing code unless explicitly requested by the user. Include Mermaid diagrams when they clarify your response.\",\r\n\t},\r\n\t{\r\n\t\tslug: \"debug\",\r\n\t\tname: \"🪲 Debug\",\r\n\t\troleDefinition:\r\n\t\t\t\"You are Roo, an expert software debugger specializing in systematic problem diagnosis and resolution.\",\r\n\t\twhenToUse:\r\n\t\t\t\"Use this mode when you're troubleshooting issues, investigating errors, or diagnosing problems. Specialized in systematic debugging, adding logging, analyzing stack traces, and identifying root causes before applying fixes.\",\r\n\t\tdescription: \"Diagnose and fix software issues\",\r\n\t\tgroups: [\"read\", \"edit\", \"browser\", \"command\", \"mcp\"],\r\n\t\tcustomInstructions:\r\n\t\t\t\"Reflect on 5-7 different possible sources of the problem, distill those down to 1-2 most likely sources, and then add logs to validate your assumptions. Explicitly ask the user to confirm the diagnosis before fixing the problem.\",\r\n\t},\r\n\t{\r\n\t\tslug: \"orchestrator\",\r\n\t\tname: \"🪃 Orchestrator\",\r\n\t\troleDefinition:\r\n\t\t\t\"You are Roo, a strategic workflow orchestrator who coordinates complex tasks by delegating them to appropriate specialized modes. You have a comprehensive understanding of each mode's capabilities and limitations, allowing you to effectively break down complex problems into discrete tasks that can be solved by different specialists.\",\r\n\t\twhenToUse:\r\n\t\t\t\"Use this mode for complex, multi-step projects that require coordination across different specialties. Ideal when you need to break down large tasks into subtasks, manage workflows, or coordinate work that spans multiple domains or expertise areas.\",\r\n\t\tdescription: \"Coordinate tasks across multiple modes\",\r\n\t\tgroups: [],\r\n\t\tcustomInstructions:\r\n\t\t\t\"Your role is to coordinate complex workflows by delegating tasks to specialized modes. As an orchestrator, you should:\\n\\n1. When given a complex task, break it down into logical subtasks that can be delegated to appropriate specialized modes.\\n\\n2. For each subtask, use the `new_task` tool to delegate. Choose the most appropriate mode for the subtask's specific goal and provide comprehensive instructions in the `message` parameter. These instructions must include:\\n    *   All necessary context from the parent task or previous subtasks required to complete the work.\\n    *   A clearly defined scope, specifying exactly what the subtask should accomplish.\\n    *   An explicit statement that the subtask should *only* perform the work outlined in these instructions and not deviate.\\n    *   An instruction for the subtask to signal completion by using the `attempt_completion` tool, providing a concise yet thorough summary of the outcome in the `result` parameter, keeping in mind that this summary will be the source of truth used to keep track of what was completed on this project.\\n    *   A statement that these specific instructions supersede any conflicting general instructions the subtask's mode might have.\\n\\n3. Track and manage the progress of all subtasks. When a subtask is completed, analyze its results and determine the next steps.\\n\\n4. Help the user understand how the different subtasks fit together in the overall workflow. Provide clear reasoning about why you're delegating specific tasks to specific modes.\\n\\n5. When all subtasks are completed, synthesize the results and provide a comprehensive overview of what was accomplished.\\n\\n6. Ask clarifying questions when necessary to better understand how to break down complex tasks effectively.\\n\\n7. Suggest improvements to the workflow based on the results of completed subtasks.\\n\\nUse subtasks to maintain clarity. If a request significantly shifts focus or requires a different expertise (mode), consider creating a subtask rather than overloading the current one.\",\r\n\t},\r\n] as const\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * CodeAction\r\n */\r\n\r\nexport const codeActionIds = [\"explainCode\", \"fixCode\", \"improveCode\", \"addToContext\", \"newTask\"] as const\r\n\r\nexport type CodeActionId = (typeof codeActionIds)[number]\r\n\r\nexport type CodeActionName = \"EXPLAIN\" | \"FIX\" | \"IMPROVE\" | \"ADD_TO_CONTEXT\" | \"NEW_TASK\"\r\n\r\n/**\r\n * TerminalAction\r\n */\r\n\r\nexport const terminalActionIds = [\"terminalAddToContext\", \"terminalFixCommand\", \"terminalExplainCommand\"] as const\r\n\r\nexport type TerminalActionId = (typeof terminalActionIds)[number]\r\n\r\nexport type TerminalActionName = \"ADD_TO_CONTEXT\" | \"FIX\" | \"EXPLAIN\"\r\n\r\nexport type TerminalActionPromptType = `TERMINAL_${TerminalActionName}`\r\n\r\n/**\r\n * Command\r\n */\r\n\r\nexport const commandIds = [\r\n\t\"activationCompleted\",\r\n\r\n\t\"plusButtonClicked\",\r\n\t\"promptsButtonClicked\",\r\n\t\"mcpButtonClicked\",\r\n\t\"historyButtonClicked\",\r\n\t\"marketplaceButtonClicked\",\r\n\t\"popoutButtonClicked\",\r\n\t\"cloudButtonClicked\",\r\n\t\"settingsButtonClicked\",\r\n\r\n\t\"openInNewTab\",\r\n\r\n\t\"showHumanRelayDialog\",\r\n\t\"registerHumanRelayCallback\",\r\n\t\"unregisterHumanRelayCallback\",\r\n\t\"handleHumanRelayResponse\",\r\n\r\n\t\"newTask\",\r\n\r\n\t\"setCustomStoragePath\",\r\n\t\"importSettings\",\r\n\r\n\t\"focusInput\",\r\n\t\"acceptInput\",\r\n\t\"focusPanel\",\r\n] as const\r\n\r\nexport type CommandId = (typeof commandIds)[number]\r\n\r\n/**\r\n * Language\r\n */\r\n\r\nexport const languages = [\r\n\t\"ca\",\r\n\t\"de\",\r\n\t\"en\",\r\n\t\"es\",\r\n\t\"fr\",\r\n\t\"hi\",\r\n\t\"id\",\r\n\t\"it\",\r\n\t\"ja\",\r\n\t\"ko\",\r\n\t\"nl\",\r\n\t\"pl\",\r\n\t\"pt-BR\",\r\n\t\"ru\",\r\n\t\"tr\",\r\n\t\"vi\",\r\n\t\"zh-CN\",\r\n\t\"zh-TW\",\r\n] as const\r\n\r\nexport const languagesSchema = z.enum(languages)\r\n\r\nexport type Language = z.infer<typeof languagesSchema>\r\n\r\nexport const isLanguage = (value: string): value is Language => languages.includes(value as Language)\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * Schema for MCP parameter definitions\r\n */\r\nexport const mcpParameterSchema = z.object({\r\n\tname: z.string().min(1),\r\n\tkey: z.string().min(1),\r\n\tplaceholder: z.string().optional(),\r\n\toptional: z.boolean().optional().default(false),\r\n})\r\n\r\nexport type McpParameter = z.infer<typeof mcpParameterSchema>\r\n\r\n/**\r\n * Schema for MCP installation method with name\r\n */\r\nexport const mcpInstallationMethodSchema = z.object({\r\n\tname: z.string().min(1),\r\n\tcontent: z.string().min(1),\r\n\tparameters: z.array(mcpParameterSchema).optional(),\r\n\tprerequisites: z.array(z.string()).optional(),\r\n})\r\n\r\nexport type McpInstallationMethod = z.infer<typeof mcpInstallationMethodSchema>\r\n\r\n/**\r\n * Component type validation\r\n */\r\nexport const marketplaceItemTypeSchema = z.enum([\"mode\", \"mcp\"] as const)\r\n\r\nexport type MarketplaceItemType = z.infer<typeof marketplaceItemTypeSchema>\r\n\r\n/**\r\n * Base schema for common marketplace item fields\r\n */\r\nconst baseMarketplaceItemSchema = z.object({\r\n\tid: z.string().min(1),\r\n\tname: z.string().min(1, \"Name is required\"),\r\n\tdescription: z.string(),\r\n\tauthor: z.string().optional(),\r\n\tauthorUrl: z.string().url(\"Author URL must be a valid URL\").optional(),\r\n\ttags: z.array(z.string()).optional(),\r\n\tprerequisites: z.array(z.string()).optional(),\r\n})\r\n\r\n/**\r\n * Type-specific schemas for YAML parsing (without type field, added programmatically)\r\n */\r\nexport const modeMarketplaceItemSchema = baseMarketplaceItemSchema.extend({\r\n\tcontent: z.string().min(1), // YAML content for modes\r\n})\r\n\r\nexport type ModeMarketplaceItem = z.infer<typeof modeMarketplaceItemSchema>\r\n\r\nexport const mcpMarketplaceItemSchema = baseMarketplaceItemSchema.extend({\r\n\turl: z.string().url(), // Required url field\r\n\tcontent: z.union([z.string().min(1), z.array(mcpInstallationMethodSchema)]), // Single config or array of methods\r\n\tparameters: z.array(mcpParameterSchema).optional(),\r\n})\r\n\r\nexport type McpMarketplaceItem = z.infer<typeof mcpMarketplaceItemSchema>\r\n\r\n/**\r\n * Unified marketplace item schema using discriminated union\r\n */\r\nexport const marketplaceItemSchema = z.discriminatedUnion(\"type\", [\r\n\t// Mode marketplace item\r\n\tmodeMarketplaceItemSchema.extend({\r\n\t\ttype: z.literal(\"mode\"),\r\n\t}),\r\n\t// MCP marketplace item\r\n\tmcpMarketplaceItemSchema.extend({\r\n\t\ttype: z.literal(\"mcp\"),\r\n\t}),\r\n])\r\n\r\nexport type MarketplaceItem = z.infer<typeof marketplaceItemSchema>\r\n\r\n/**\r\n * Installation options for marketplace items\r\n */\r\nexport const installMarketplaceItemOptionsSchema = z.object({\r\n\ttarget: z.enum([\"global\", \"project\"]).optional().default(\"project\"),\r\n\tparameters: z.record(z.string(), z.any()).optional(),\r\n})\r\n\r\nexport type InstallMarketplaceItemOptions = z.infer<typeof installMarketplaceItemOptionsSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * Interface for follow-up data structure used in follow-up questions\r\n * This represents the data structure for follow-up questions that the LLM can ask\r\n * to gather more information needed to complete a task.\r\n */\r\nexport interface FollowUpData {\r\n\t/** The question being asked by the LLM */\r\n\tquestion?: string\r\n\t/** Array of suggested answers that the user can select */\r\n\tsuggest?: Array<SuggestionItem>\r\n}\r\n\r\n/**\r\n * Interface for a suggestion item with optional mode switching\r\n */\r\nexport interface SuggestionItem {\r\n\t/** The text of the suggestion */\r\n\tanswer: string\r\n\t/** Optional mode to switch to when selecting this suggestion */\r\n\tmode?: string\r\n}\r\n\r\n/**\r\n * Zod schema for SuggestionItem\r\n */\r\nexport const suggestionItemSchema = z.object({\r\n\tanswer: z.string(),\r\n\tmode: z.string().optional(),\r\n})\r\n\r\n/**\r\n * Zod schema for FollowUpData\r\n */\r\nexport const followUpDataSchema = z.object({\r\n\tquestion: z.string().optional(),\r\n\tsuggest: z.array(suggestionItemSchema).optional(),\r\n})\r\n\r\nexport type FollowUpDataType = z.infer<typeof followUpDataSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport { type TaskEvent, taskEventSchema } from \"./events.js\"\r\nimport { rooCodeSettingsSchema } from \"./global-settings.js\"\r\n\r\n/**\r\n * IpcMessageType\r\n */\r\n\r\nexport enum IpcMessageType {\r\n\tConnect = \"Connect\",\r\n\tDisconnect = \"Disconnect\",\r\n\tAck = \"Ack\",\r\n\tTaskCommand = \"TaskCommand\",\r\n\tTaskEvent = \"TaskEvent\",\r\n}\r\n\r\n/**\r\n * IpcOrigin\r\n */\r\n\r\nexport enum IpcOrigin {\r\n\tClient = \"client\",\r\n\tServer = \"server\",\r\n}\r\n\r\n/**\r\n * Ack\r\n */\r\n\r\nexport const ackSchema = z.object({\r\n\tclientId: z.string(),\r\n\tpid: z.number(),\r\n\tppid: z.number(),\r\n})\r\n\r\nexport type Ack = z.infer<typeof ackSchema>\r\n\r\n/**\r\n * TaskCommandName\r\n */\r\n\r\nexport enum TaskCommandName {\r\n\tStartNewTask = \"StartNewTask\",\r\n\tCancelTask = \"CancelTask\",\r\n\tCloseTask = \"CloseTask\",\r\n\tResumeTask = \"ResumeTask\",\r\n}\r\n\r\n/**\r\n * TaskCommand\r\n */\r\n\r\nexport const taskCommandSchema = z.discriminatedUnion(\"commandName\", [\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.StartNewTask),\r\n\t\tdata: z.object({\r\n\t\t\tconfiguration: rooCodeSettingsSchema,\r\n\t\t\ttext: z.string(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t\tnewTab: z.boolean().optional(),\r\n\t\t}),\r\n\t}),\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.CancelTask),\r\n\t\tdata: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.CloseTask),\r\n\t\tdata: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.ResumeTask),\r\n\t\tdata: z.string(),\r\n\t}),\r\n])\r\n\r\nexport type TaskCommand = z.infer<typeof taskCommandSchema>\r\n\r\n/**\r\n * IpcMessage\r\n */\r\n\r\nexport const ipcMessageSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.Ack),\r\n\t\torigin: z.literal(IpcOrigin.Server),\r\n\t\tdata: ackSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.TaskCommand),\r\n\t\torigin: z.literal(IpcOrigin.Client),\r\n\t\tclientId: z.string(),\r\n\t\tdata: taskCommandSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.TaskEvent),\r\n\t\torigin: z.literal(IpcOrigin.Server),\r\n\t\trelayClientId: z.string().optional(),\r\n\t\tdata: taskEventSchema,\r\n\t}),\r\n])\r\n\r\nexport type IpcMessage = z.infer<typeof ipcMessageSchema>\r\n\r\n/**\r\n * IpcClientEvents\r\n */\r\n\r\nexport type IpcClientEvents = {\r\n\t[IpcMessageType.Connect]: []\r\n\t[IpcMessageType.Disconnect]: []\r\n\t[IpcMessageType.Ack]: [data: Ack]\r\n\t[IpcMessageType.TaskCommand]: [data: TaskCommand]\r\n\t[IpcMessageType.TaskEvent]: [data: TaskEvent]\r\n}\r\n\r\n/**\r\n * IpcServerEvents\r\n */\r\n\r\nexport type IpcServerEvents = {\r\n\t[IpcMessageType.Connect]: [clientId: string]\r\n\t[IpcMessageType.Disconnect]: [clientId: string]\r\n\t[IpcMessageType.TaskCommand]: [clientId: string, data: TaskCommand]\r\n\t[IpcMessageType.TaskEvent]: [relayClientId: string | undefined, data: TaskEvent]\r\n}\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * MCP Server Use Types\r\n */\r\nexport interface McpServerUse {\r\n\ttype: string\r\n\tserverName: string\r\n\ttoolName?: string\r\n\turi?: string\r\n}\r\n\r\n/**\r\n * McpExecutionStatus\r\n */\r\n\r\nexport const mcpExecutionStatusSchema = z.discriminatedUnion(\"status\", [\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"started\"),\r\n\t\tserverName: z.string(),\r\n\t\ttoolName: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"output\"),\r\n\t\tresponse: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"completed\"),\r\n\t\tresponse: z.string().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"error\"),\r\n\t\terror: z.string().optional(),\r\n\t}),\r\n])\r\n\r\nexport type McpExecutionStatus = z.infer<typeof mcpExecutionStatusSchema>\r\n", "/**\r\n * Configuration for models that should use simplified single-file read_file tool\r\n * These models will use the simpler <read_file><path>...</path></read_file> format\r\n * instead of the more complex multi-file args format\r\n */\r\n\r\n/**\r\n * Check if a model should use single file read format\r\n * @param modelId The model ID to check\r\n * @returns true if the model should use single file reads\r\n */\r\nexport function shouldUseSingleFileRead(modelId: string): boolean {\r\n\treturn modelId.includes(\"grok-code-fast-1\")\r\n}\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * TodoStatus\r\n */\r\nexport const todoStatusSchema = z.enum([\"pending\", \"in_progress\", \"completed\"] as const)\r\n\r\nexport type TodoStatus = z.infer<typeof todoStatusSchema>\r\n\r\n/**\r\n * TodoItem\r\n */\r\nexport const todoItemSchema = z.object({\r\n\tid: z.string(),\r\n\tcontent: z.string(),\r\n\tstatus: todoStatusSchema,\r\n})\r\n\r\nexport type TodoItem = z.infer<typeof todoItemSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * CommandExecutionStatus\r\n */\r\n\r\nexport const commandExecutionStatusSchema = z.discriminatedUnion(\"status\", [\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"started\"),\r\n\t\tpid: z.number().optional(),\r\n\t\tcommand: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"output\"),\r\n\t\toutput: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"exited\"),\r\n\t\texitCode: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"fallback\"),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"timeout\"),\r\n\t}),\r\n])\r\n\r\nexport type CommandExecutionStatus = z.infer<typeof commandExecutionStatusSchema>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAAA,eAAkB;;;ACFlB,IAAAC,cAAkB;;;ACAlB,iBAAkB;AA2BX,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,iBAAiB,aAAE,KAAK,SAAS;AAcvC,IAAM,WAAW;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAIO,SAAS,UAAU,KAA+B;AACxD,SAAQ,SAAiC,SAAS,GAAG;AACtD;AAQO,IAAM,gBAAgB,CAAC,aAAa;AAIpC,SAAS,eAAe,KAAoC;AAClE,SAAQ,cAAsC,SAAS,GAAG;AAC3D;AAQO,IAAM,kBAAkB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAIO,SAAS,iBAAiB,KAAsC;AACtE,SAAQ,gBAAwC,SAAS,GAAG;AAC7D;AAuCO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,iBAAiB,aAAE,KAAK,SAAS;AAQvC,IAAM,2BAA2B,aAAE,OAAO;AAAA,EAChD,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,MAAM,aAAE,OAAO,EAAE,SAAS;AAC3B,CAAC;AAQM,IAAM,wBAAwB,aAAE,OAAO;AAAA,EAC7C,MAAM,aAAE,OAAO;AAAA,EACf,mBAAmB,aAAE,OAAO;AAAA,EAC5B,kBAAkB,aAAE,OAAO;AAAA,EAC3B,SAAS,aAAE,OAAO;AACnB,CAAC;AAQM,IAAM,qBAAqB,aAAE,OAAO;AAAA,EAC1C,IAAI,aAAE,OAAO;AAAA,EACb,MAAM,aAAE,MAAM,CAAC,aAAE,QAAQ,KAAK,GAAG,aAAE,QAAQ,KAAK,CAAC,CAAC;AAAA,EAClD,KAAK,eAAe,SAAS;AAAA,EAC7B,KAAK,eAAe,SAAS;AAAA,EAC7B,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACrC,SAAS,aAAE,QAAQ,EAAE,SAAS;AAAA,EAC9B,WAAW,aAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,0BAA0B,aAAE,OAAO,EAAE,SAAS;AAAA,EAC9C,YAAY,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,QAAQ,CAAC,EAAE,SAAS;AAAA,EACvD,gBAAgB,yBAAyB,SAAS;AAAA,EAClD,iBAAiB,sBAAsB,SAAS;AAAA,EAChD,aAAa,aAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,aAAa,aAAE,MAAM,CAAC,aAAE,QAAQ,QAAQ,GAAG,aAAE,QAAQ,WAAW,CAAC,CAAC,EAAE,SAAS;AAAA,EAC7E,YAAY,aAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,UAAU,aACR,OAAO;AAAA,IACP,MAAM,aACJ,OAAO;AAAA,MACP,sBAAsB,aAAE,OAAO,EAAE,SAAS;AAAA,MAC1C,cAAc,aAAE,OAAO,EAAE,SAAS;AAAA,MAClC,mBAAmB,aAAE,OAAO,EAAE,SAAS;AAAA,IACxC,CAAC,EACA,SAAS;AAAA,EACZ,CAAC,EACA,SAAS;AACZ,CAAC;AAQM,IAAM,mBAAmB,aAAE,OAAO;AAAA,EACxC,eAAe,aAAE,OAAO;AAAA,EACxB,gBAAgB,aAAE,OAAO;AAAA,EACzB,kBAAkB,aAAE,OAAO,EAAE,SAAS;AAAA,EACtC,iBAAiB,aAAE,OAAO,EAAE,SAAS;AAAA,EACrC,WAAW,aAAE,OAAO;AAAA,EACpB,eAAe,aAAE,OAAO;AACzB,CAAC;AAQM,IAAM,sBAAsB,aAAE,OAAO;AAAA,EAC3C,WAAW,aAAE,OAAO;AAAA,EACpB,IAAI,aAAE,OAAO;AAAA,EACb,MAAM,aAAE,OAAO;AAAA,EACf,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,SAAS;AACtC,CAAC;;;AChQD,IAAAC,cAAkB;AAMX,IAAM,aAAa,CAAC,QAAQ,QAAQ,WAAW,WAAW,OAAO,OAAO;AAExE,IAAM,mBAAmB,cAAE,KAAK,UAAU;AAQ1C,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,kBAAkB,cAAE,KAAK,SAAS;AAQxC,IAAM,kBAAkB,cAAE;AAAA,EAChC;AAAA,EACA,cAAE,OAAO;AAAA,IACR,UAAU,cAAE,OAAO;AAAA,IACnB,UAAU,cAAE,OAAO;AAAA,EACpB,CAAC;AACF;;;AF7CO,IAAK,mBAAL,kBAAKC,sBAAL;AAEN,EAAAA,kBAAA,iBAAc;AAGd,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,mBAAgB;AAChB,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,mBAAgB;AAChB,EAAAA,kBAAA,gBAAa;AACb,EAAAA,kBAAA,qBAAkB;AAClB,EAAAA,kBAAA,mBAAgB;AAChB,EAAAA,kBAAA,cAAW;AAGX,EAAAA,kBAAA,gBAAa;AACb,EAAAA,kBAAA,kBAAe;AACf,EAAAA,kBAAA,iBAAc;AAGd,EAAAA,kBAAA,aAAU;AACV,EAAAA,kBAAA,sBAAmB;AACnB,EAAAA,kBAAA,sBAAmB;AACnB,EAAAA,kBAAA,qBAAkB;AAGlB,EAAAA,kBAAA,2BAAwB;AACxB,EAAAA,kBAAA,oBAAiB;AAGjB,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,4BAAyB;AAGzB,EAAAA,kBAAA,cAAW;AACX,EAAAA,kBAAA,cAAW;AApCA,SAAAA;AAAA,GAAA;AA2CL,IAAM,sBAAsB,cAAE,OAAO;AAAA,EAC3C,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EAEpD,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,mCAA8B,GAAG,cAAE,MAAM;AAAA,IACzC,cAAE,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA,cAAE,OAAO;AAAA,MACR,WAAW,cAAE,QAAQ;AAAA,IACtB,CAAC;AAAA,EACF,CAAC;AAAA,EACD,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,mCAA8B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACtD,CAAC,6BAA2B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACnD,CAAC,uCAAgC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACxD,CAAC,mCAA8B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACtD,CAAC,yBAAyB,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EAEjD,CAAC,6BAA2B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACnD,CAAC,iCAA6B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACrD,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,CAAC,CAAC;AAAA,EAEhE,CAAC,uBAAwB,GAAG,cAAE,MAAM;AAAA,IACnC,cAAE,OAAO;AAAA,MACR,QAAQ,cAAE,OAAO;AAAA,MACjB,QAAQ,cAAE,MAAM,CAAC,cAAE,QAAQ,SAAS,GAAG,cAAE,QAAQ,SAAS,CAAC,CAAC;AAAA,MAC5D,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AAAA,EACD,CAAC,yCAAiC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,CAAC,CAAC;AAAA,EACrE,CAAC,yCAAiC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACzD,CAAC,uCAAgC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EAExD,CAAC,qCAA+B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,iBAAiB,cAAE,OAAO,CAAC,CAAC;AAAA,EACpF,CAAC,mDAAsC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,gBAAgB,CAAC;AAAA,EAEhF,CAAC,+BAA4B,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,qDAAuC,GAAG,cAAE,MAAM,CAAC,cAAE,OAAO,EAAE,MAAM,cAAE,OAAO,GAAG,UAAU,cAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAC1G,CAAC;AAQM,IAAM,kBAAkB,cAAE,mBAAmB,aAAa;AAAA;AAAA,EAEhE,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,IAC/D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA;AAAA,EAGD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,IAC/D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,mCAA8B;AAAA,IACnD,SAAS,oBAAoB,MAAM,mCAA8B;AAAA,IACjE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,IAC/D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,IAC/D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,mCAA8B;AAAA,IACnD,SAAS,oBAAoB,MAAM,mCAA8B;AAAA,IACjE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,6BAA2B;AAAA,IAChD,SAAS,oBAAoB,MAAM,6BAA2B;AAAA,IAC9D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,uCAAgC;AAAA,IACrD,SAAS,oBAAoB,MAAM,uCAAgC;AAAA,IACnE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,mCAA8B;AAAA,IACnD,SAAS,oBAAoB,MAAM,mCAA8B;AAAA,IACjE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,yBAAyB;AAAA,IAC9C,SAAS,oBAAoB,MAAM,yBAAyB;AAAA,IAC5D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA;AAAA,EAGD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,6BAA2B;AAAA,IAChD,SAAS,oBAAoB,MAAM,6BAA2B;AAAA,IAC9D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,iCAA6B;AAAA,IAClD,SAAS,oBAAoB,MAAM,iCAA6B;AAAA,IAChE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,IAC/D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA;AAAA,EAGD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,uBAAwB;AAAA,IAC7C,SAAS,oBAAoB,MAAM,uBAAwB;AAAA,IAC3D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,yCAAiC;AAAA,IACtD,SAAS,oBAAoB,MAAM,yCAAiC;AAAA,IACpE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,yCAAiC;AAAA,IACtD,SAAS,oBAAoB,MAAM,yCAAiC;AAAA,IACpE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA;AAAA,EAGD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,qCAA+B;AAAA,IACpD,SAAS,oBAAoB,MAAM,qCAA+B;AAAA,IAClE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,mDAAsC;AAAA,IAC3D,SAAS,oBAAoB,MAAM,mDAAsC;AAAA,IACzE,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC;AAAA;AAAA,EAGD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,yBAAyB;AAAA,IAC9C,SAAS,cAAE,UAAU;AAAA,IACrB,QAAQ,cAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,WAAW,cAAE,QAAQ,yBAAyB;AAAA,IAC9C,SAAS,cAAE,UAAU;AAAA,IACrB,QAAQ,cAAE,OAAO;AAAA,EAClB,CAAC;AACF,CAAC;;;AGpND,IAAAC,cAAkB;AAgGX,IAAK,aAAL,kBAAKC,gBAAL;AACN,EAAAA,YAAA,aAAU;AACV,EAAAA,YAAA,iBAAc;AACd,EAAAA,YAAA,eAAY;AACZ,EAAAA,YAAA,UAAO;AACP,EAAAA,YAAA,UAAO;AALI,SAAAA;AAAA,GAAA;AAQL,IAAM,qBAAqB,cAAE,OAAO;AAAA,EAC1C,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AACtC,CAAC;;;AC3GD,IAAAC,eAAkB;;;ACAlB,IAAAC,cAAkB;;;ACAlB,IAAAC,cAAkB;AAMX,IAAM,mBAAmB,CAAC,OAAO,UAAU,MAAM;AAEjD,IAAM,yBAAyB,cAAE,KAAK,gBAAgB;AAQtD,IAAM,mCAAmC,cAAE,MAAM,CAAC,wBAAwB,cAAE,QAAQ,SAAS,CAAC,CAAC;AAQ/F,IAAM,kBAAkB,CAAC,OAAO,UAAU,MAAM;AAEhD,IAAM,wBAAwB,cAAE,KAAK,eAAe;AAOpD,IAAM,eAAe,CAAC,WAAW,QAAQ,UAAU;AACnD,IAAM,oBAAoB,cAAE,KAAK,YAAY;AAO7C,IAAM,kBAAkB,CAAC,cAAc,eAAe,aAAa,mBAAmB;AAEtF,IAAM,wBAAwB,cAAE,KAAK,eAAe;AAIpD,IAAM,mBAAmB,CAAC,UAChC,gBAAgB,SAAS,KAAuB;AAM1C,IAAM,kBAAkB,cAAE,OAAO;AAAA,EACvC,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC9B,mBAAmB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACtC,eAAe,cAAE,OAAO;AAAA,EACxB,gBAAgB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,qBAAqB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qBAAqB,cAAE,QAAQ;AAAA;AAAA,EAE/B,mBAAmB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACxC,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,EAE9C,qBAAqB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,qBAAqB,cAAE,MAAM,qBAAqB,EAAE,SAAS;AAAA,EAC7D,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,gBAAgB,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,OAAO,cACL;AAAA,IACA,cAAE,OAAO;AAAA,MACR,MAAM,kBAAkB,SAAS;AAAA;AAAA,MACjC,eAAe,cAAE,OAAO;AAAA,MACxB,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,MAChC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,MACjC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,MACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,EACF,EACC,SAAS;AACZ,CAAC;;;AC/FD,IAAAC,cAAkB;AAKX,IAAM,0BAA0B;AAAA,EACtC,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,0BAA0B;AAAA,EAC1B,mBAAmB;AACpB;AAMO,IAAM,4BAA4B,cAAE,OAAO;AAAA,EACjD,sBAAsB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC3C,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,+BAA+B,cAC7B,KAAK,CAAC,UAAU,UAAU,qBAAqB,UAAU,WAAW,mBAAmB,CAAC,EACxF,SAAS;AAAA,EACX,8BAA8B,cAAE,OAAO,EAAE,SAAS;AAAA,EAClD,8BAA8B,cAAE,OAAO,EAAE,SAAS;AAAA,EAClD,qCAAqC,cAAE,OAAO,EAAE,SAAS;AAAA,EACzD,6BAA6B,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EAC/D,+BAA+B,cAC7B,OAAO,EACP,IAAI,wBAAwB,kBAAkB,EAC9C,IAAI,wBAAwB,kBAAkB,EAC9C,SAAS;AAAA;AAAA,EAEX,sCAAsC,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1D,6CAA6C,cAAE,OAAO,EAAE,SAAS;AAClE,CAAC;AAQM,IAAM,4BAA4B,cAAE,OAAO;AAAA,EACjD,QAAQ,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC3E,QAAQ,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC3E,qBAAqB,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EACxF,QAAQ,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC3E,SAAS,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC5E,qBAAqB,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,WAAW,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AACzF,CAAC;AAQM,IAAM,8BAA8B,cAAE,OAAO;AAAA,EACnD,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,sCAAsC,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1D,qCAAqC,cAAE,OAAO,EAAE,SAAS;AAAA,EACzD,6CAA6C,cAAE,OAAO,EAAE,SAAS;AAAA,EACjE,2BAA2B,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/C,4BAA4B,cAAE,OAAO,EAAE,SAAS;AAAA,EAChD,oCAAoC,cAAE,OAAO,EAAE,SAAS;AACzD,CAAC;;;ACjEM,IAAM,0BAA4C;AAElD,IAAM,kBAAkB;AAAA,EAC9B,4BAA4B;AAAA,IAC3B,WAAW;AAAA;AAAA,IACX,eAAe;AAAA;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA;AAAA,IAEzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA;AAAA,QACf,YAAY;AAAA;AAAA,QACZ,aAAa;AAAA;AAAA,QACb,kBAAkB;AAAA;AAAA,QAClB,iBAAiB;AAAA;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,EAClB;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,EAClB;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AACD;AAEO,IAAM,+BAA+B;;;ACnHrC,IAAM,wBAAwC;AAE9C,IAAM,oCAAoD;AAM1D,IAAM,gBAAgB;AAAA,EAC5B,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,4CAA4C;AAAA,IAC3C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oCAAoC;AAAA,IACnC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,qDAAqD;AAAA,IACpD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,+BAA+B;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,qCAAqC;AAAA,IACpC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,qCAAqC;AAAA,IACpC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACD;AAEO,IAAM,8BAA8B;AAEpC,IAAM,qBAAqB;AAE3B,IAAM,0BAA0B;AAKhC,IAAM,gCAAyD;AAAA;AAAA,EAErE,CAAC,WAAW,KAAK;AAAA;AAAA,EAEjB,CAAC,OAAO,KAAK;AAAA;AAAA,EAEb,CAAC,OAAO,KAAK;AAAA;AAAA,EAEb,CAAC,OAAO,OAAO;AAAA;AAAA,EAEf,CAAC,OAAO,KAAK;AAAA;AAAA,EAEb,CAAC,OAAO,KAAK;AACd;AAIO,IAAM,kBAAkB;AAAA,EAC9B,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,kBAAkB,OAAO,iBAAiB;AAAA,EACnD,EAAE,OAAO,kBAAkB,OAAO,iBAAiB;AAAA,EACnD,EAAE,OAAO,kBAAkB,OAAO,iBAAiB;AAAA,EACnD,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,kBAAkB,OAAO,iBAAiB;AAAA,EACnD,EAAE,OAAO,kBAAkB,OAAO,iBAAiB;AAAA,EACnD,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,iBAAiB,OAAO,gBAAgB;AAAA,EACjD,EAAE,OAAO,iBAAiB,OAAO,gBAAgB;AAClD,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AAExC,IAAM,mCAAmC;;;ACvbzC,IAAM,yBAA0C;AAEhD,IAAM,iBAAiB;AAAA,EAC7B,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACb,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AACD;;;ACxCO,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA;AAAA,IACX,eAAe;AAAA;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gDAAgD;AAAA,IAC/C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mDAAmD;AAAA,IAClD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oCAAoC;AAAA,IACnC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;AClTA,IAAM,sBAAsB;AAYrB,SAAS,0BAA0B,WAA2B;AAEpE,SAAO,UAAU,QAAQ,qBAAqB,KAAK;AACpD;AAIO,IAAM,2BAA8C;AACpD,IAAM,wCAAwC;AAa9C,SAAS,qBAAqB,aAAgC,YAAY,OAAe;AAC/F,SAAO,YAAY,0BAA0B,WAAW,IAAI;AAC7D;AAEO,IAAM,mBAAmB;AAAA,EAC/B,4BAA4B;AAAA,IAC3B,GAAG,gBAAgB,0BAA0B;AAAA,IAC7C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,4BAA4B;AAAA,IAC3B,GAAG,gBAAgB,0BAA0B;AAAA,IAC7C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,GAAG,gBAAgB,wBAAwB;AAAA,IAC3C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,GAAG,gBAAgB,4BAA4B;AAAA,IAC/C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,GAAG,gBAAgB,4BAA4B;AAAA,IAC/C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,6BAA6B;AAAA,IAC5B,GAAG,gBAAgB,2BAA2B;AAAA,IAC9C,gBAAgB;AAAA,IAChB,qBAAqB;AAAA;AAAA,IACrB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AACD;;;ACrFO,IAAM,yBAA0C;AAEhD,IAAM,iBAAiB;AAAA,EAC7B,iBAAiB;AAAA,IAChB,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AACD;AAEO,IAAM,gCAAgC;;;AC9BtC,IAAM,uBAAuB;AAE7B,IAAM,eAAe;AAAA,EAC3B,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AACD;AAEO,IAAM,yBAAoC,aAAa,oBAAoB;AAE3E,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;;;AClC7B,IAAM,oBAAoB;AAAA,EAChC,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,+BAA+B;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;AAEO,IAAM,4BAAgD;;;AC1CtD,IAAM,0BAA4C;AAElD,IAAM,kBAAkB;AAAA,EAC9B,mDAAmD;AAAA,IAClD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aACC;AAAA,EACF;AAAA,EACA,8CAA8C;AAAA,IAC7C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,2DAA2D;AAAA,IAC1D,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,4DAA4D;AAAA,IAC3D,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,8CAA8C;AAAA,IAC7C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,qCAAqC;AAAA,IACpC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AACD;;;AC1HO,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,EAC1B;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,mBAAmB;AAAA,IAClB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,EACpB;AACD;;;ACnSO,IAAM,0BAA4C;AAIlD,IAAM,kBAAkB;AAAA,EAC9B,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,EAC1B;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,mBAAmB;AAAA,IAClB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,EACpB;AACD;;;ACzSO,IAAM,sBAAsB;AAE5B,IAAM,wBAAmC;AAAA,EAC/C,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;AAEO,IAAM,4BAA4B;;;ACFlC,IAAM,qBAAkC;AAExC,IAAM,aAAa;AAAA;AAAA,EAEzB,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iDAAiD;AAAA,IAChD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,+BAA+B;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,oCAAoC;AAAA,IACnC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aACC;AAAA,EACF;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AACD;;;ACjIO,IAAM,iCAAiC;AACvC,IAAM,kCAAkC;AACxC,IAAM,qCAAqC;AAG3C,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,oCAAoC;AAG1C,IAAM,sBAAsB;AAC5B,IAAM,6BAA6B,MAAO,KAAK;;;ACR/C,IAAM,+BAAsD;AAE5D,IAAM,+BAA+B;AAErC,IAAM,iCAAiC,MAAO,KAAK;AAEnD,IAAM,uBAAuB;AAAA,EACnC,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,aAAa;AAAA,EACd;AAAA,EACA,qDAAqD;AAAA,IACpD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,aAAa;AAAA,EACd;AAAA,EACA,sDAAsD;AAAA,IACrD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,aAAa;AAAA,EACd;AACD;;;ACxCO,IAAM,wBAAwB;AAE9B,IAAM,0BAAqC;AAAA,EACjD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAClB;AAEO,IAAM,8BAA8B,oBAAI,IAAI;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;;;AClDM,IAAM,+BAA+B;AAIrC,IAAM,yBAAyB;AAC/B,IAAM,2BAAsC;AAAA,EAClD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aAAa;AACd;;;ACbO,IAAM,wBAAwC;AAE9C,IAAM,gBAAgB;AAAA,EAC5B,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACD;AAEO,IAAM,8BAA8B;;;AC7EpC,IAAM,yBAA0C;AAEhD,IAAM,iBAAiB;AAAA,EAC7B,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aACC;AAAA,EACF;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AACD;AAEO,IAAM,+BAA+B;;;ACvCrC,IAAM,uBAAuB;AAC7B,IAAM,yBAAoC;AAAA,EAChD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aAAa;AACd;;;ACXO,IAAM,6BAAkD;AAExD,IAAM,qBAAqB;AAAA,EACjC,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,mBAAmB;AAAA,EACpB;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA;AAAA,IAEb,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,QAAQ,eAAe,KAAQ,YAAY,OAAO,aAAa,GAAK,iBAAiB,OAAO;AAAA,MACpG,EAAE,MAAM,YAAY,eAAe,KAAQ,YAAY,KAAK,aAAa,IAAM,iBAAiB,KAAK;AAAA,IACtG;AAAA,EACD;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,QAAQ,eAAe,KAAQ,YAAY,OAAO,aAAa,GAAK,iBAAiB,OAAO;AAAA,MACpG,EAAE,MAAM,YAAY,eAAe,KAAQ,YAAY,MAAM,aAAa,KAAK,iBAAiB,MAAM;AAAA,IACvG;AAAA,EACD;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,OAAO,CAAC,EAAE,MAAM,QAAQ,eAAe,KAAQ,YAAY,OAAO,aAAa,KAAK,iBAAiB,MAAO,CAAC;AAAA,EAC9G;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,YAAY,eAAe,SAAW,YAAY,KAAK,aAAa,IAAM,iBAAiB,MAAM;AAAA,IAC1G;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,YAAY,eAAe,SAAW,YAAY,KAAK,aAAa,KAAK,iBAAiB,MAAM;AAAA,IACzG;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,YAAY,eAAe,SAAW,YAAY,KAAK,aAAa,KAAK,iBAAiB,KAAK;AAAA,IACxG;AAAA,EACD;AAAA,EACA,IAAI;AAAA,IACH,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,QAAQ,eAAe,KAAS,YAAY,GAAK,aAAa,GAAK,iBAAiB,KAAK;AAAA,MACjG,EAAE,MAAM,YAAY,eAAe,KAAS,YAAY,KAAK,aAAa,IAAM,iBAAiB,MAAM;AAAA,IACxG;AAAA,EACD;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,QAAQ,eAAe,KAAS,YAAY,MAAM,aAAa,KAAK,iBAAiB,MAAM;AAAA,MACnG,EAAE,MAAM,YAAY,eAAe,KAAS,YAAY,GAAK,aAAa,GAAK,iBAAiB,IAAI;AAAA,IACrG;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,IAAI;AAAA,IACH,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,cAAc;AAAA,IACb,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,YAAY,eAAe,OAAS,YAAY,MAAM,aAAa,IAAM,iBAAiB,MAAM;AAAA,IACzG;AAAA,EACD;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,OAAO;AAAA,MACN,EAAE,MAAM,YAAY,eAAe,OAAS,YAAY,MAAM,aAAa,GAAK,iBAAiB,MAAM;AAAA,IACxG;AAAA,EACD;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,aACC;AAAA,EACF;AACD;AAEO,IAAM,8BAAyC;AAAA,EACrD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AACd;AAIO,IAAM,+BAA+B;AAErC,IAAM,oCAAoC;AAC1C,IAAM,2BAA2B;AAEjC,IAAM,iCAAiC;;;AChTvC,IAAM,2BAA2B;AAEjC,IAAM,6BAAwC;AAAA,EACpD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;AAEO,IAAM,mCAAmC;AAEzC,IAAM,oCAAoC,oBAAI,IAAI;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAGM,IAAM,kCAAkC,oBAAI,IAAI;AAAA,EACtD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AASM,IAAM,+CAA+C,oBAAI,IAAI;AAAA,EACnE;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,sCAAsC,oBAAI,IAAI;AAAA,EAC1D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA,EAGA;AAAA,EACA;AACD,CAAC;;;ACxFM,IAAM,yBAA0C;AAEhD,IAAM,iBAAiB;AAAA,EAC7B,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AACD;;;ACzBO,IAAM,yBAAyB;AAE/B,IAAM,2BAAsC;AAAA,EAClD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;;;ACbO,IAAM,oBAAgC;AAEtC,IAAM,YAAY;AAAA,EACxB,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aACC;AAAA,EACF;AACD;;;ACLO,IAAM,0BAA4C;AAElD,IAAM,kBAAkB;AAAA,EAC9B,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,+BAA+B;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,aAAa;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;ACvFO,IAAM,wBAAwB;AAE9B,IAAM,0BAAqC;AAAA,EACjD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAClB;;;ACRO,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,EAC1B;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,EAC1B;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;AAEO,IAAM,iBAAiB;AAAA,EAC7B,EAAE,OAAO,UAAU,OAAO,SAAS;AAAA,EACnC,EAAE,OAAO,eAAe,OAAO,cAAc;AAAA,EAC7C,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,EACzC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,2BAA2B,OAAO,0BAA0B;AAAA,EACrE,EAAE,OAAO,2BAA2B,OAAO,0BAA0B;AAAA,EACrE,EAAE,OAAO,sBAAsB,OAAO,qBAAqB;AAAA,EAC3D,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,cAAc,OAAO,aAAa;AAAA,EAC3C,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,eAAe,OAAO,cAAc;AAAA,EAC7C,EAAE,OAAO,eAAe,OAAO,cAAc;AAAA,EAC7C,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AAAA,EACrD,EAAE,OAAO,wBAAwB,OAAO,uBAAuB;AAAA,EAC/D,EAAE,OAAO,wBAAwB,OAAO,uBAAuB;AAAA,EAC/D,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,eAAe,OAAO,cAAc;AAAA,EAC7C,EAAE,OAAO,iBAAiB,OAAO,gBAAgB;AAClD;;;AC9XO,IAAM,0BAA4C;AAGlD,IAAM,kBAAkB;AAAA,EAC9B,iBAAiB;AAAA,IAChB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,eAAe;AAAA,IACd,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,sBAAsB;AAAA,IACrB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,IAAI;AAAA,IACH,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,qBAAqB;AAAA,IACpB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,mBAAmB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,wBAAwB;AAAA,IACvB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,cAAc;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AACD;;;ACvLO,IAAM,oBAAgC;AAEtC,IAAM,YAAY;AAAA,EACxB,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;AC3FO,IAAM,gCAAgC;AAEtC,IAAM,0CAA0C,oBAAI,IAAI;AAAA,EAC9D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,uCAAuC,oBAAI,IAAI;AAAA,EAC3D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,4CAA4C,oBAAI,IAAI;AAAA,EAChE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,kCAA6C;AAAA,EACzD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;AAEO,IAAM,wCAAwC;;;AC9F9C,IAAM,iCAA0D;AAChE,IAAM,yBAAyB;AAAA,EACrC,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aACC;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aACC;AAAA,EACF;AACD;AAGO,IAAM,4BAAgD;AACtD,IAAM,oBAAoB;AAAA,EAChC,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aACC;AAAA,IACD,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aACC;AAAA,IACD,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AACD;AAEO,IAAM,0BAA0B;;;ACrGhC,IAAM,0BAA0B;AAEhC,IAAM,4BAAuC;AAAA,EACnD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACd;;;AnCqBO,IAAM,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,sBAAsB,cAAE,KAAK,aAAa;AAQhD,IAAM,8BAA8B,cAAE,OAAO;AAAA,EACnD,IAAI,cAAE,OAAO;AAAA,EACb,MAAM,cAAE,OAAO;AAAA,EACf,aAAa,oBAAoB,SAAS;AAAA,EAC1C,SAAS,cAAE,OAAO,EAAE,SAAS;AAC9B,CAAC;AAWM,IAAM,oCAAoC;AAEjD,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC3C,kBAAkB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,aAAa,cAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,iBAAiB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAAA,EACzC,kBAAkB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,yBAAyB,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA;AAAA,EAGpD,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,iBAAiB,iCAAiC,SAAS;AAAA,EAC3D,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAG5C,WAAW,sBAAsB,SAAS;AAC3C,CAAC;AAGD,IAAM,gCAAgC,2BAA2B,OAAO;AAAA,EACvE,YAAY,cAAE,OAAO,EAAE,SAAS;AACjC,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,wBAAwB,cAAE,QAAQ,EAAE,SAAS;AAAA;AAC9C,CAAC;AAED,IAAM,mBAAmB,8BAA8B,OAAO;AAAA,EAC7D,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,2BAA2B,cAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,GAAM,EAAE,SAAS;AACzE,CAAC;AAED,IAAM,cAAc,2BAA2B,OAAO;AAAA,EACrD,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,aAAa,cAAE,OAAO,EAAE,SAAS;AAClC,CAAC;AAED,IAAM,mBAAmB,2BAA2B,OAAO;AAAA,EAC1D,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,4BAA4B,cAAE,OAAO,EAAE,SAAS;AAAA,EAChD,iCAAiC,cAAE,QAAQ,EAAE,SAAS;AACvD,CAAC;AAED,IAAM,gBAAgB,8BAA8B,OAAO;AAAA,EAC1D,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,4BAA4B,cAAE,QAAQ,EAAE,SAAS;AAAA,EACjD,mBAAmB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACxC,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,eAAe,cAAE,QAAQ,EAAE,SAAS;AAAA,EACpC,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,cAAc,cAAE,QAAQ,EAAE,SAAS;AAAA,EACnC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,2BAA2B,cAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,qBAAqB,cAAE,QAAQ,EAAE,SAAS;AAAA;AAC3C,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,kBAAkB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,iBAAiB,cAAE,QAAQ,EAAE,SAAS;AACvC,CAAC;AAED,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,oBAAoB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,uBAAuB,gBAAgB,QAAQ;AAAA,EAC/C,gBAAgB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,wBAAwB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC7C,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EACtC,eAAe,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,CAAC,EAAE,SAAS;AAC1D,CAAC;AAED,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,cAAc,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,uBAAuB,cACrB,OAAO;AAAA,IACP,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,IAC5B,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,IAC5B,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,IAC7B,IAAI,cAAE,OAAO,EAAE,SAAS;AAAA,EACzB,CAAC,EACA,SAAS;AACZ,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,sBAAsB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,oCAAoC,cAAE,QAAQ,EAAE,SAAS;AAC1D,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAAA,EACzC,kBAAkB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,iBAAiB,cAAE,QAAQ,EAAE,SAAS;AACvC,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,oBAAoB,cAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAED,IAAM,qBAAqB,8BAA8B,OAAO;AAAA,EAC/D,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA;AAAA,EAGzC,yBAAyB,kBAAkB,SAAS;AACrD,CAAC;AAED,IAAM,gBAAgB,8BAA8B,OAAO;AAAA,EAC1D,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAC1C,CAAC;AAED,IAAM,iBAAiB,8BAA8B,OAAO;AAAA,EAC3D,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AACvC,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,cAAc,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAED,IAAM,iBAAiB,8BAA8B,OAAO;AAAA,EAC3D,iBAAiB,cACf,MAAM,CAAC,cAAE,QAAQ,4BAA4B,GAAG,cAAE,QAAQ,4BAA4B,CAAC,CAAC,EACxF,SAAS;AAAA,EACX,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,gBAAgB,2BAA2B,OAAO;AAAA,EACvD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAED,IAAM,mBAAmB;AAEzB,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,QAAQ,cAAE,QAAQ,EAAE,SAAS;AAC9B,CAAC;AAED,IAAM,YAAY,8BAA8B,OAAO;AAAA,EACtD,WAAW,cAAE,OAAO,EAAE,SAAS;AAChC,CAAC;AAED,IAAM,aAAa,8BAA8B,OAAO;AAAA,EACvD,YAAY,cAAE,OAAO,EAAE,SAAS;AACjC,CAAC;AAED,IAAM,oBAAoB,2BAA2B,OAAO;AAAA,EAC3D,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,8BAA8B,cAAE,OAAO,EAAE,SAAS;AACnD,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,cAAc,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAED,IAAM,gBAAgB,2BAA2B,OAAO;AAAA,EACvD,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAC7C,CAAC;AAED,IAAM,iBAAiB,8BAA8B,OAAO;AAAA,EAC3D,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,iBAAiB,cAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAED,IAAM,YAAY,8BAA8B,OAAO;AAAA,EACtD,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,YAAY,cAAE,MAAM,CAAC,cAAE,QAAQ,OAAO,GAAG,cAAE,QAAQ,eAAe,CAAC,CAAC,EAAE,SAAS;AAChF,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,iBAAiB,cAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAED,IAAM,oBAAoB,8BAA8B,OAAO;AAAA,EAC9D,mBAAmB,cAAE,OAAO,EAAE,SAAS;AACxC,CAAC;AAED,IAAM,uBAAuB,8BAA8B,OAAO;AAAA,EACjE,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,sBAAsB,cAAE,OAAO,EAAE,SAAS;AAC3C,CAAC;AAED,IAAM,iBAAiB,8BAA8B,OAAO;AAAA,EAC3D,mBAAmB,cAAE,OAAO,EAAE,SAAS;AACxC,CAAC;AAED,IAAM,YAAY,8BAA8B,OAAO;AAAA;AAEvD,CAAC;AAED,IAAM,wBAAwB,2BAA2B,OAAO;AAAA,EAC/D,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAC7C,CAAC;AAED,IAAM,gBAAgB,cAAE,OAAO;AAAA,EAC9B,aAAa,cAAE,UAAU;AAC1B,CAAC;AAEM,IAAM,sCAAsC,cAAE,mBAAmB,eAAe;AAAA,EACtF,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,iBAAiB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,aAAa,EAAE,CAAC,CAAC;AAAA,EAC1E,YAAY,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,OAAO,EAAE,CAAC,CAAC;AAAA,EAC/D,iBAAiB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,YAAY,EAAE,CAAC,CAAC;AAAA,EACzE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACtE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,YAAY,EAAE,CAAC,CAAC;AAAA,EACxE,mBAAmB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,eAAe,EAAE,CAAC,CAAC;AAAA,EAC9E,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,iBAAiB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,aAAa,EAAE,CAAC,CAAC;AAAA,EAC1E,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EAClE,UAAU,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,KAAK,EAAE,CAAC,CAAC;AAAA,EAC3D,WAAW,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,MAAM,EAAE,CAAC,CAAC;AAAA,EAC7D,kBAAkB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,aAAa,EAAE,CAAC,CAAC;AAAA,EAC3E,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,UAAU,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,KAAK,EAAE,CAAC,CAAC;AAAA,EAC3D,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,kBAAkB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,aAAa,EAAE,CAAC,CAAC;AAAA,EAC3E,qBAAqB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,iBAAiB,EAAE,CAAC,CAAC;AAAA,EAClF,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACtE,UAAU,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,KAAK,EAAE,CAAC,CAAC;AAAA,EAC3D,sBAAsB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,mBAAmB,EAAE,CAAC,CAAC;AAAA,EACrF;AACD,CAAC;AAEM,IAAM,yBAAyB,cAAE,OAAO;AAAA,EAC9C,aAAa,oBAAoB,SAAS;AAAA,EAC1C,GAAG,gBAAgB;AAAA,EACnB,GAAG,iBAAiB;AAAA,EACpB,GAAG,YAAY;AAAA,EACf,GAAG,iBAAiB;AAAA,EACpB,GAAG,cAAc;AAAA,EACjB,GAAG,aAAa;AAAA,EAChB,GAAG,aAAa;AAAA,EAChB,GAAG,aAAa;AAAA,EAChB,GAAG,eAAe;AAAA,EAClB,GAAG,eAAe;AAAA,EAClB,GAAG,aAAa;AAAA,EAChB,GAAG,gBAAgB;AAAA,EACnB,GAAG,mBAAmB;AAAA,EACtB,GAAG,cAAc;AAAA,EACjB,GAAG,eAAe;AAAA,EAClB,GAAG,gBAAgB;AAAA,EACnB,GAAG,aAAa;AAAA,EAChB,GAAG,eAAe;AAAA,EAClB,GAAG,cAAc;AAAA,EACjB,GAAG,eAAe;AAAA,EAClB,GAAG,iBAAiB;AAAA,EACpB,GAAG,aAAa;AAAA,EAChB,GAAG,UAAU;AAAA,EACb,GAAG,WAAW;AAAA,EACd,GAAG,kBAAkB;AAAA,EACrB,GAAG,aAAa;AAAA,EAChB,GAAG,cAAc;AAAA,EACjB,GAAG,eAAe;AAAA,EAClB,GAAG,gBAAgB;AAAA,EACnB,GAAG,UAAU;AAAA,EACb,GAAG,gBAAgB;AAAA,EACnB,GAAG,kBAAkB;AAAA,EACrB,GAAG,qBAAqB;AAAA,EACxB,GAAG,eAAe;AAAA,EAClB,GAAG,UAAU;AAAA,EACb,GAAG,sBAAsB;AAAA,EACzB,GAAG,4BAA4B;AAChC,CAAC;AAIM,IAAM,+BAA+B,uBAAuB,OAAO,EAAE,IAAI,cAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAEhG,IAAM,4CAA4C,oCAAoC;AAAA,EAC5F,cAAE,OAAO,EAAE,IAAI,cAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AACvC;AAIO,IAAM,yBAAyB,uBAAuB,MAAM,EAAE;AAE9D,IAAM,gBAAmD;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,aAAa,CAAC,aAAmD;AAC7E,QAAM,aAAa,cAAc,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC;AAC5D,SAAO,aAAc,SAAS,UAAU,IAAe;AACxD;AAGO,IAAM,4BAA4C,CAAC,aAAa,eAAe,SAAS;AAExF,IAAM,iBAAiB,CAAC,UAAoC,YAA6C;AAC/G,MAAI,YAAY,0BAA0B,SAAS,QAAQ,GAAG;AAC7D,WAAO;AAAA,EACR;AAEA,MAAI,YAAY,aAAa,YAAY,WAAW,QAAQ,YAAY,EAAE,SAAS,QAAQ,GAAG;AAC7F,WAAO;AAAA,EACR;AAGA,MAAI,YAAY,aAAa,uBAAuB,WAAW,QAAQ,YAAY,EAAE,WAAW,YAAY,GAAG;AAC9G,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAEO,IAAM,qBAGT;AAAA,EACH,WAAW;AAAA,IACV,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,eAAe;AAAA,EACpC;AAAA,EACA,SAAS;AAAA,IACR,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,aAAa;AAAA,EAClC;AAAA,EACA,UAAU;AAAA,IACT,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,cAAc;AAAA,EACnC;AAAA,EACA,QAAQ;AAAA,IACP,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,YAAY;AAAA,EACjC;AAAA,EACA,eAAe,EAAE,IAAI,eAAe,OAAO,eAAe,QAAQ,OAAO,KAAK,gBAAgB,EAAE;AAAA,EAChG,UAAU;AAAA,IACT,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,cAAc;AAAA,EACnC;AAAA,EACA,QAAQ,EAAE,IAAI,UAAU,OAAO,UAAU,QAAQ,OAAO,KAAK,YAAY,EAAE;AAAA,EAC3E,aAAa;AAAA,IACZ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,iBAAiB;AAAA,EACtC;AAAA,EACA,WAAW;AAAA,IACV,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,eAAe;AAAA,EACpC;AAAA,EACA,QAAQ;AAAA,IACP,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,YAAY;AAAA,EACjC;AAAA,EACA,cAAc;AAAA,IACb,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,eAAe;AAAA,EACpC;AAAA,EACA,MAAM,EAAE,IAAI,QAAQ,OAAO,QAAQ,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,EACnE,mBAAmB;AAAA,IAClB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,oBAAoB;AAAA,EACzC;AAAA,EACA,SAAS;AAAA,IACR,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,aAAa;AAAA,EAClC;AAAA,EACA,UAAU;AAAA,IACT,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,cAAc;AAAA,EACnC;AAAA,EACA,iBAAiB;AAAA,IAChB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,kBAAkB;AAAA,EACvC;AAAA,EACA,aAAa,EAAE,IAAI,aAAa,OAAO,aAAa,QAAQ,OAAO,KAAK,cAAc,EAAE;AAAA,EACxF,KAAK,EAAE,IAAI,OAAO,OAAO,OAAO,QAAQ,OAAO,KAAK,SAAS,EAAE;AAAA,EAC/D,WAAW;AAAA,IACV,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,eAAe;AAAA,EACpC;AAAA,EACA,QAAQ;AAAA,IACP,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,YAAY;AAAA,EACjC;AAAA,EACA,aAAa;AAAA,IACZ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ,OAAO,KAAK,eAAe;AAAA,EACpC;AAAA,EACA,KAAK,EAAE,IAAI,OAAO,OAAO,cAAc,QAAQ,OAAO,KAAK,SAAS,EAAE;AAAA,EACtE,KAAK,EAAE,IAAI,OAAO,OAAO,OAAO,QAAQ,OAAO,KAAK,sBAAsB,EAAE;AAAA;AAAA,EAG5E,OAAO,EAAE,IAAI,SAAS,OAAO,SAAS,QAAQ,CAAC,EAAE;AAAA,EACjD,aAAa,EAAE,IAAI,eAAe,OAAO,gBAAgB,QAAQ,CAAC,EAAE;AAAA,EACpE,SAAS,EAAE,IAAI,WAAW,OAAO,WAAW,QAAQ,CAAC,EAAE;AAAA,EACvD,YAAY,EAAE,IAAI,cAAc,OAAO,cAAc,QAAQ,CAAC,EAAE;AAAA,EAChE,UAAU,EAAE,IAAI,YAAY,OAAO,YAAY,QAAQ,CAAC,EAAE;AAAA,EAC1D,SAAS,EAAE,IAAI,WAAW,OAAO,WAAW,QAAQ,CAAC,EAAE;AAAA,EACvD,WAAW,EAAE,IAAI,aAAa,OAAO,aAAa,QAAQ,CAAC,EAAE;AAAA,EAC7D,qBAAqB,EAAE,IAAI,qBAAqB,OAAO,qBAAqB,QAAQ,CAAC,EAAE;AACxF;AAEO,IAAM,mBAAmB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAIO,IAAM,oBAAoB,CAAC,QACjC,iBAAiB,SAAS,GAAsB;;;AoCtlBjD,IAAAC,cAAkB;AAMX,IAAM,oBAAoB,cAAE,OAAO;AAAA,EACzC,IAAI,cAAE,OAAO;AAAA,EACb,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,QAAQ,cAAE,OAAO;AAAA,EACjB,IAAI,cAAE,OAAO;AAAA,EACb,MAAM,cAAE,OAAO;AAAA,EACf,UAAU,cAAE,OAAO;AAAA,EACnB,WAAW,cAAE,OAAO;AAAA,EACpB,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,WAAW,cAAE,OAAO;AAAA,EACpB,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,MAAM,cAAE,OAAO,EAAE,SAAS;AAC3B,CAAC;;;ACrBD,IAAAC,cAAkB;AAQX,IAAM,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,sBAAsB,cAAE,KAAK,aAAa;AAQhD,IAAM,oBAAoB,cAAE,OAAO;AAAA,EACzC,eAAe,cAAE,QAAQ,EAAE,SAAS;AAAA,EACpC,oBAAoB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,wBAAwB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC7C,iBAAiB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,iBAAiB,cAAE,QAAQ,EAAE,SAAS;AACvC,CAAC;;;AC9BD,IAAAC,eAAkB;AASX,IAAM,oBAAoB,CAAC,SAAS,WAAW,UAAU;AAEzD,IAAM,0BAA0B,eAAE,KAAK,iBAAiB;AAQxD,IAAK,qBAAL,kBAAKC,wBAAL;AACN,EAAAA,oBAAA,kBAAe;AACf,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,kBAAe;AACf,EAAAA,oBAAA,+BAA4B;AAC5B,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,iBAAc;AACd,EAAAA,oBAAA,0BAAuB;AACvB,EAAAA,oBAAA,eAAY;AAEZ,EAAAA,oBAAA,wBAAqB;AACrB,EAAAA,oBAAA,yBAAsB;AACtB,EAAAA,oBAAA,uBAAoB;AAEpB,EAAAA,oBAAA,eAAY;AACZ,EAAAA,oBAAA,2BAAwB;AACxB,EAAAA,oBAAA,yBAAsB;AAEtB,EAAAA,oBAAA,uBAAoB;AACpB,EAAAA,oBAAA,+BAA4B;AAE5B,EAAAA,oBAAA,sBAAmB;AACnB,EAAAA,oBAAA,qBAAkB;AAElB,EAAAA,oBAAA,0BAAuB;AAEvB,EAAAA,oBAAA,8BAA2B;AAE3B,EAAAA,oBAAA,gCAA6B;AAC7B,EAAAA,oBAAA,8BAA2B;AAC3B,EAAAA,oBAAA,4BAAyB;AACzB,EAAAA,oBAAA,wCAAqC;AAErC,EAAAA,oBAAA,0BAAuB;AACvB,EAAAA,oBAAA,gCAA6B;AAC7B,EAAAA,oBAAA,0BAAuB;AACvB,EAAAA,oBAAA,oCAAiC;AAEjC,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,4BAAyB;AACzB,EAAAA,oBAAA,4BAAyB;AAEzB,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,4BAAyB;AACzB,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,+BAA4B;AAC5B,EAAAA,oBAAA,sBAAmB;AAhDR,SAAAA;AAAA,GAAA;AAuDL,IAAM,4BAA4B,eAAE,OAAO;AAAA,EACjD,SAAS,eAAE,OAAO;AAAA,EAClB,YAAY,eAAE,OAAO;AAAA,EACrB,eAAe,eAAE,OAAO;AAAA,EACxB,UAAU,eAAE,OAAO;AAAA,EACnB,YAAY,eAAE,OAAO;AAAA,EACrB,UAAU,eAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAIM,IAAM,6BAA6B,eAAE,OAAO;AAAA,EAClD,UAAU,eAAE,OAAO;AAAA,EACnB,MAAM,eAAE,OAAO;AAChB,CAAC;AAIM,IAAM,2BAA2B,eAAE,OAAO;AAAA,EAChD,sBAAsB,eAAE,QAAQ,EAAE,SAAS;AAC5C,CAAC;AAIM,IAAM,sBAAsB,eAAE,OAAO;AAAA,EAC3C,GAAG,0BAA0B;AAAA,EAC7B,GAAG,2BAA2B;AAAA,EAC9B,GAAG,yBAAyB;AAC7B,CAAC;AAIM,IAAM,uBAAuB,eAAE,OAAO;AAAA,EAC5C,QAAQ,eAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,aAAa,eAAE,KAAK,aAAa,EAAE,SAAS;AAAA,EAC5C,SAAS,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,cAAc,eAAE,OAAO,EAAE,SAAS;AAAA,EAClC,WAAW,eAAE,QAAQ,EAAE,SAAS;AAAA,EAChC,OAAO,eACL,OAAO;AAAA,IACP,OAAO,eAAE,OAAO;AAAA,IAChB,WAAW,eAAE,OAAO;AAAA,IACpB,YAAY,eAAE,OAAO;AAAA,IACrB,SAAS,eAAE,OAAO;AAAA,EACnB,CAAC,EACA,SAAS;AACZ,CAAC;AAIM,IAAM,sBAAsB,eAAE,OAAO;AAAA,EAC3C,eAAe,eAAE,OAAO,EAAE,SAAS;AAAA,EACnC,gBAAgB,eAAE,OAAO,EAAE,SAAS;AAAA,EACpC,eAAe,eAAE,OAAO,EAAE,SAAS;AACpC,CAAC;AAIM,IAAM,4BAA4B,eAAE,OAAO;AAAA,EACjD,GAAG,oBAAoB;AAAA,EACvB,GAAG,qBAAqB;AAAA,EACxB,GAAG,oBAAoB;AACxB,CAAC;AAkBM,IAAM,8BAA8B,eAAE,mBAAmB,QAAQ;AAAA,EACvE,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAAA,IACD,YAAY;AAAA,EACb,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,iCAA+B;AAAA,IAC/C,YAAY,eAAE,OAAO;AAAA,MACpB,GAAG,0BAA0B;AAAA,MAC7B,QAAQ,eAAE,OAAO;AAAA,MACjB,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,qCAAiC;AAAA,IACjD,YAAY,eAAE,OAAO;AAAA,MACpB,GAAG,0BAA0B;AAAA,MAC7B,aAAa,eAAE,OAAO;AAAA,MACtB,cAAc,eAAE,OAAO;AAAA,MACvB,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,MACrC,kBAAkB,eAAE,OAAO,EAAE,SAAS;AAAA,MACtC,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,IAC3B,CAAC;AAAA,EACF,CAAC;AACF,CAAC;;;ACvND,IAAAC,eAAkB;AAQX,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,WAAW,eACT,OAAO,EACP,SAAS,EACT;AAAA,IACA,CAAC,YAAY;AACZ,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AAEA,UAAI;AACH,YAAI,OAAO,OAAO;AAClB,eAAO;AAAA,MACR,QAAQ;AACP,eAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,EAAE,SAAS,qCAAqC;AAAA,EACjD;AAAA,EACD,aAAa,eAAE,OAAO,EAAE,SAAS;AAClC,CAAC;AAQM,IAAM,mBAAmB,eAAE,MAAM,CAAC,kBAAkB,eAAE,MAAM,CAAC,kBAAkB,kBAAkB,CAAC,CAAC,CAAC;AAQ3G,IAAM,wBAAwB,eAAE,MAAM,gBAAgB,EAAE;AAAA,EACvD,CAAC,WAAW;AACX,UAAM,OAAO,oBAAI,IAAI;AAErB,WAAO,OAAO,MAAM,CAAC,UAAU;AAE9B,YAAM,YAAY,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AAEpD,UAAI,KAAK,IAAI,SAAS,GAAG;AACxB,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,SAAS;AAClB,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EACA,EAAE,SAAS,mCAAmC;AAC/C;AAEO,IAAM,mBAAmB,eAAE,OAAO;AAAA,EACxC,MAAM,eAAE,OAAO,EAAE,MAAM,mBAAmB,mDAAmD;AAAA,EAC7F,MAAM,eAAE,OAAO,EAAE,IAAI,GAAG,kBAAkB;AAAA,EAC1C,gBAAgB,eAAE,OAAO,EAAE,IAAI,GAAG,6BAA6B;AAAA,EAC/D,WAAW,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EACjC,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,QAAQ;AAAA,EACR,QAAQ,eAAE,KAAK,CAAC,UAAU,SAAS,CAAC,EAAE,SAAS;AAChD,CAAC;AAQM,IAAM,4BAA4B,eAAE,OAAO;AAAA,EACjD,aAAa,eAAE,MAAM,gBAAgB,EAAE;AAAA,IACtC,CAAC,UAAU;AACV,YAAM,QAAQ,oBAAI,IAAI;AAEtB,aAAO,MAAM,MAAM,CAAC,SAAS;AAC5B,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG;AACzB,iBAAO;AAAA,QACR;AAEA,cAAM,IAAI,KAAK,IAAI;AACnB,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACA;AAAA,MACC,SAAS;AAAA,IACV;AAAA,EACD;AACD,CAAC;AAQM,IAAM,wBAAwB,eAAE,OAAO;AAAA,EAC7C,gBAAgB,eAAE,OAAO,EAAE,SAAS;AAAA,EACpC,WAAW,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EACjC,oBAAoB,eAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAQM,IAAM,0BAA0B,eAAE,OAAO,eAAE,OAAO,GAAG,sBAAsB,SAAS,CAAC;AAQrF,IAAM,6BAA6B,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,OAAO,EAAE,SAAS,CAAC;AAQ7E,IAAM,gBAAuC;AAAA,EACnD;AAAA,IACC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBACC;AAAA,IACD,WACC;AAAA,IACD,aAAa;AAAA,IACb,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,UAAU,aAAa,sBAAsB,CAAC,GAAG,WAAW,KAAK;AAAA,IACxG,oBACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBACC;AAAA,IACD,WACC;AAAA,IACD,aAAa;AAAA,IACb,QAAQ,CAAC,QAAQ,QAAQ,WAAW,WAAW,KAAK;AAAA,EACrD;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBACC;AAAA,IACD,WACC;AAAA,IACD,aAAa;AAAA,IACb,QAAQ,CAAC,QAAQ,WAAW,KAAK;AAAA,IACjC,oBACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBACC;AAAA,IACD,WACC;AAAA,IACD,aAAa;AAAA,IACb,QAAQ,CAAC,QAAQ,QAAQ,WAAW,WAAW,KAAK;AAAA,IACpD,oBACC;AAAA,EACF;AAAA,EACA;AAAA,IACC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBACC;AAAA,IACD,WACC;AAAA,IACD,aAAa;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,oBACC;AAAA,EACF;AACD;;;AClMA,IAAAC,eAAkB;AAMX,IAAM,gBAAgB,CAAC,eAAe,WAAW,eAAe,gBAAgB,SAAS;AAUzF,IAAM,oBAAoB,CAAC,wBAAwB,sBAAsB,wBAAwB;AAYjG,IAAM,aAAa;AAAA,EACzB;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AACD;AAQO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,kBAAkB,eAAE,KAAK,SAAS;AAIxC,IAAM,aAAa,CAAC,UAAqC,UAAU,SAAS,KAAiB;;;AzClE7F,IAAM,yBAAyB;AAO/B,IAAM,0CAA0C;AAMhD,IAAM,uBAAuB,eAAE,OAAO;AAAA,EAC5C,sBAAsB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,mBAAmB,eAAE,MAAM,2BAA2B,EAAE,SAAS;AAAA,EACjE,kBAAkB,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,QAAQ,CAAC,EAAE,SAAS;AAAA,EAE7D,yBAAyB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7C,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,aAAa,eAAE,MAAM,iBAAiB,EAAE,SAAS;AAAA;AAAA,EAGjD,uBAAuB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,wCAAwC,eAAE,OAAO,EAAE,SAAS;AAAA,EAE5D,uBAAuB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA,EAE5C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qCAAqC,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1D,kBAAkB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,kCAAkC,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvD,2BAA2B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,cAAc,eAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EACzC,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,gBAAgB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,8BAA8B,eAAE,QAAQ,EAAE,SAAS;AAAA,EACnD,8BAA8B,eAAE,OAAO,EAAE,SAAS;AAAA,EAClD,2BAA2B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,iBAAiB,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC9C,gBAAgB,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC7C,yBAAyB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7C,yBAAyB,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACtD,gCAAgC,eAAE,QAAQ,EAAE,SAAS;AAAA,EACrD,oBAAoB,eAAE,OAAO,EAAE,QAAQ;AAAA,EACvC,gBAAgB,eAAE,OAAO,EAAE,QAAQ;AAAA,EACnC,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,4BAA4B,eAAE,OAAO,EAAE,SAAS;AAAA,EAChD,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,2BAA2B,eAAE,QAAQ,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhD,uBAAuB,eAAE,OAAO,EAAE,SAAS;AAAA,EAE3C,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,sBAAsB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC3C,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EAEzC,mBAAmB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAExC,YAAY,eAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,cAAc,eAAE,QAAQ,EAAE,SAAS;AAAA,EACnC,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EAEjC,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,eAAE,OAAO,EAAE,SAAS;AAAA,EACtC,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EAEvC,yBAAyB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7C,8BAA8B,eAAE,OAAO,EAAE,SAAS;AAAA,EAClD,iCAAiC,eAAE,OAAO,EAAE,SAAS;AAAA,EACrD,kCAAkC,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvD,sBAAsB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,2BAA2B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,6BAA6B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAElD,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAEzC,kBAAkB,eAAE,OAAO,EAAE,SAAS;AAAA,EACtC,aAAa,eAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,aAAa,kBAAkB,SAAS;AAAA,EAExC,qBAAqB,0BAA0B,SAAS;AAAA,EACxD,qBAAqB,0BAA0B,SAAS;AAAA,EAExD,UAAU,gBAAgB,SAAS;AAAA,EAEnC,kBAAkB,wBAAwB,SAAS;AAAA,EAEnD,YAAY,eAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAE9C,sBAAsB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAE3C,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,gBAAgB,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC1D,aAAa,eAAE,MAAM,gBAAgB,EAAE,SAAS;AAAA,EAChD,mBAAmB,wBAAwB,SAAS;AAAA,EACpD,sBAAsB,2BAA2B,SAAS;AAAA,EAC1D,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,6BAA6B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAClD,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,mBAAmB,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC7D,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,oBAAoB,eAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAIM,IAAM,uBAAuB,qBAAqB,MAAM,EAAE;AAM1D,IAAM,wBAAwB,uBAAuB,MAAM,oBAAoB;AAO/E,IAAM,oBAAoB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAGO,IAAM,qBAAqB;AAAA,EACjC;AAAA;AACD;AAWO,IAAM,mBAAmB,CAAC,QAChC,kBAAkB,SAAS,GAAwB,KAAK,mBAAmB,SAAS,GAAsB;AAQpG,IAAM,oBAAoB,CAAC,GAAG,sBAAsB,GAAG,sBAAsB,EAAE;AAAA,EACrF,CAAC,QAA+B,CAAC,iBAAiB,GAAG;AACtD;AAEO,IAAM,mBAAmB,CAAC,QAChC,kBAAkB,SAAS,GAAwB;AAO7C,IAAM,iBAAkC;AAAA,EAC9C,aAAa;AAAA,EACb,iCAAiC;AAAA,EAEjC,yBAAyB;AAAA,EAEzB,kBAAkB,CAAC;AAAA,EAEnB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,iBAAiB,CAAC,GAAG;AAAA,EACrB,yBAAyB;AAAA,EACzB,yBAAyB,CAAC;AAAA,EAC1B,gCAAgC;AAAA,EAEhC,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EAEtB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AAAA,EAEb,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAElC,oBAAoB;AAAA,EAEpB,aAAa;AAAA,EACb,qBAAqB;AAAA,EAErB,mBAAmB;AAAA,EAEnB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,iBAAiB;AAAA;AAAA,EAEjB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EAEvB,UAAU;AAAA,EACV,kBAAkB;AAAA,EAElB,YAAY;AAAA,EAEZ,sBAAsB;AAAA,EAEtB,MAAM;AAAA;AAAA,EAEN,aAAa,CAAC;AACf;AAEO,IAAM,gBAAgB,IAAI,KAAK;;;A0CrUtC,IAAAC,eAAkB;AAKX,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,MAAM,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,KAAK,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACrB,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EACjC,UAAU,eAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,KAAK;AAC/C,CAAC;AAOM,IAAM,8BAA8B,eAAE,OAAO;AAAA,EACnD,MAAM,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,SAAS,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACzB,YAAY,eAAE,MAAM,kBAAkB,EAAE,SAAS;AAAA,EACjD,eAAe,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAC7C,CAAC;AAOM,IAAM,4BAA4B,eAAE,KAAK,CAAC,QAAQ,KAAK,CAAU;AAOxE,IAAM,4BAA4B,eAAE,OAAO;AAAA,EAC1C,IAAI,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACpB,MAAM,eAAE,OAAO,EAAE,IAAI,GAAG,kBAAkB;AAAA,EAC1C,aAAa,eAAE,OAAO;AAAA,EACtB,QAAQ,eAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,WAAW,eAAE,OAAO,EAAE,IAAI,gCAAgC,EAAE,SAAS;AAAA,EACrE,MAAM,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACnC,eAAe,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAC7C,CAAC;AAKM,IAAM,4BAA4B,0BAA0B,OAAO;AAAA,EACzE,SAAS,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA;AAC1B,CAAC;AAIM,IAAM,2BAA2B,0BAA0B,OAAO;AAAA,EACxE,KAAK,eAAE,OAAO,EAAE,IAAI;AAAA;AAAA,EACpB,SAAS,eAAE,MAAM,CAAC,eAAE,OAAO,EAAE,IAAI,CAAC,GAAG,eAAE,MAAM,2BAA2B,CAAC,CAAC;AAAA;AAAA,EAC1E,YAAY,eAAE,MAAM,kBAAkB,EAAE,SAAS;AAClD,CAAC;AAOM,IAAM,wBAAwB,eAAE,mBAAmB,QAAQ;AAAA;AAAA,EAEjE,0BAA0B,OAAO;AAAA,IAChC,MAAM,eAAE,QAAQ,MAAM;AAAA,EACvB,CAAC;AAAA;AAAA,EAED,yBAAyB,OAAO;AAAA,IAC/B,MAAM,eAAE,QAAQ,KAAK;AAAA,EACtB,CAAC;AACF,CAAC;AAOM,IAAM,sCAAsC,eAAE,OAAO;AAAA,EAC3D,QAAQ,eAAE,KAAK,CAAC,UAAU,SAAS,CAAC,EAAE,SAAS,EAAE,QAAQ,SAAS;AAAA,EAClE,YAAY,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,IAAI,CAAC,EAAE,SAAS;AACpD,CAAC;;;A/CRM,IAAM,8BAA8B,eAAE,OAAO;AAAA,EACnD,UAAU,eAAE,QAAQ;AAAA,EACpB,WAAW,eAAE;AAAA,IACZ,eAAE,OAAO;AAAA,MACR,UAAU,eAAE,QAAQ;AAAA,MACpB,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,EACF;AACD,CAAC;AAQM,IAAM,oCAAoC,qBAC/C,KAAK;AAAA,EACL,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,yBAAyB;AAC1B,CAAC,EAEA;AAAA,EACA,eAAE,OAAO;AAAA,IACR,oBAAoB,eAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS;AAAA,IAC5D,iBAAiB,eAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,IACnD,mBAAmB,eAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS;AAAA,IAC3D,sBAAsB,eAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS;AAAA,IAC9D,yBAAyB,eAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS;AAAA,IACjE,iCAAiC,eAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS;AAAA,EAC1E,CAAC;AACF;AAQM,IAAM,kCAAkC,eAAE,OAAO;AAAA,EACvD,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,mBAAmB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACxC,yBAAyB,eAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS;AAAA,EAC9D,0BAA0B,eAAE,QAAQ,EAAE,SAAS;AAChD,CAAC;AAQM,IAAM,6BAA6B,eAAE,OAAO;AAAA,EAClD,SAAS,eAAE,OAAO;AAAA,EAClB,eAAe,gCAAgC,SAAS;AAAA,EACxD,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACzC,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,MAAM,eAAE,MAAM,wBAAwB,EAAE,SAAS;AAAA,EACjD,kBAAkB,eAAE,OAAO,eAAE,OAAO,GAAG,4BAA4B,EAAE,SAAS;AAC/E,CAAC;AAQM,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAC7C,CAAC;AAIM,IAAM,2BAA2B,eAAE,OAAO;AAAA,EAChD,wBAAwB,eAAE,QAAQ,EAAE,SAAS;AAC9C,CAAC;AAIM,IAAM,yBAAyB,eAAE,OAAO;AAAA,EAC9C,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS,eAAE,OAAO;AACnB,CAAC;AAQM,IAAM,yBAAgD;AAAA,EAC5D,UAAU;AAAA,EACV,WAAW,CAAC;AACb;AAEO,IAAM,uBAA6C;AAAA,EACzD,SAAS;AAAA,EACT,eAAe;AAAA,IACd,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,EAC3B;AAAA,EACA,iBAAiB,CAAC;AAAA,EAClB,WAAW;AACZ;AAYO,IAAM,sBAAsB,eAAE,OAAO;AAAA,EAC3C,SAAS,eAAE,QAAQ;AAAA,EACnB,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,OAAO,eAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,YAAY,eAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,WAAW,eAAE,OAAO,EAAE,SAAS;AAChC,CAAC;AAmHM,IAAK,kBAAL,kBAAKC,qBAAL;AACN,EAAAA,iBAAA,kBAAe;AACf,EAAAA,iBAAA,gBAAa;AACb,EAAAA,iBAAA,eAAY;AACZ,EAAAA,iBAAA,cAAW;AACX,EAAAA,iBAAA,YAAS;AALE,SAAAA;AAAA,GAAA;AAuBL,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAMpC,IAAM,sBAAsB,eAAE,OAAO;AAAA,EACpC,QAAQ,eAAE,OAAO;AAAA,EACjB,YAAY,eAAE,WAAW,UAAU;AAAA,EACnC,SAAS,mBAAmB,SAAS;AAAA,EACrC,gBAAgB,eAAE,MAAM,mBAAmB,EAAE,SAAS;AAAA,EACtD,cAAc,eAAE,OAAO,EAAE,SAAS;AAAA,EAClC,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,iBAAiB,SAAS;AAAA,EACtC,GAAG,mBAAmB;AACvB,CAAC;AAQM,IAAM,0BAA0B,eAAE,OAAO;AAAA,EAC/C,YAAY,eAAE,OAAO;AAAA,EACrB,QAAQ,eAAE,OAAO;AAAA,EACjB,eAAe,eAAE,OAAO;AAAA,EACxB,eAAe;AAAA,EACf,eAAe,oBAAoB,SAAS;AAAA,EAC5C,eAAe,eAAE,OAAO,OAAO;AAAA,EAC/B,MAAM;AAAA,EACN,SAAS,mBAAmB,SAAS;AAAA,EACrC,aAAa,eAAE,MAAM,eAAE,OAAO,CAAC;AAAA,EAC/B,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,OAAO,eAAE,MAAM,eAAE,OAAO,EAAE,MAAM,eAAE,OAAO,GAAG,MAAM,eAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC1E,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,eAAE,MAAM,eAAE,OAAO,EAAE,MAAM,eAAE,OAAO,GAAG,UAAU,eAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,SAAS;AACrG,CAAC;AAQM,IAAK,4BAAL,CAAKC,8BAAL;AACN,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AAEA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AAEA,EAAAA,oDAAA;AAEA,EAAAA,oDAAA;AAEA,EAAAA,oDAAA;AACA,EAAAA,oDAAA;AAEA,EAAAA,0BAAA,wBAAqB;AACrB,EAAAA,0BAAA,0BAAuB;AACvB,EAAAA,0BAAA,sBAAmB;AAzBR,SAAAA;AAAA,GAAA;AA4BL,IAAM,6BAA6B,eAAE,mBAAmB,QAAQ;AAAA,EACtE,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,aAAa;AAAA,IACtD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,aAAa;AAAA,IACtD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,UAAU;AAAA,IACnD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,eAAe;AAAA,IACxD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,aAAa;AAAA,IACtD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,QAAQ;AAAA,IACjD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EAED,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,UAAU;AAAA,IACnD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,YAAY;AAAA,IACrD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EAED,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,eAAe;AAAA,IACxD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EAED,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,qBAAqB;AAAA,IAC9D,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EAED,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,WAAW;AAAA,IACpD,UAAU;AAAA,IACV,MAAM,eAAE,OAAO;AAAA,IACf,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,yBAAyB,sBAAsB;AAAA,IAC/D,UAAU;AAAA,IACV,iBAAiB,eAAE,OAAO,EAAE,MAAM,eAAE,OAAO,GAAG,UAAU,eAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/E,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EAED,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,8CAA2C;AAAA,IAC3D,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,kDAA6C;AAAA,IAC7D,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,0CAAyC;AAAA,IACzD,UAAU;AAAA,IACV,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AACF,CAAC;AAQM,IAAK,6BAAL,kBAAKC,gCAAL;AACN,EAAAA,4BAAA,eAAY;AACZ,EAAAA,4BAAA,cAAW;AACX,EAAAA,4BAAA,gBAAa;AAHF,SAAAA;AAAA,GAAA;AAML,IAAM,+BAA+B,eAAE,mBAAmB,QAAQ;AAAA,EACxE,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,4BAAoC;AAAA,IACpD,YAAY,eAAE,OAAO;AAAA,IACrB,SAAS,eAAE,OAAO;AAAA,MACjB,MAAM,eAAE,OAAO;AAAA,MACf,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,MACrC,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,MAC1B,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,0BAAmC;AAAA,IACnD,YAAY,eAAE,OAAO;AAAA,IACrB,SAAS,eAAE,OAAO,EAAE,QAAQ,eAAE,OAAO,EAAE,CAAC;AAAA,IACxC,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,8BAAqC;AAAA,IACrD,YAAY,eAAE,OAAO;AAAA,IACrB,SAAS,eAAE,OAAO,EAAE,QAAQ,eAAE,OAAO,EAAE,CAAC;AAAA,IACxC,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AACF,CAAC;AAQM,IAAK,uBAAL,CAAKC,yBAAL;AACN,EAAAA,0CAAA;AACA,EAAAA,0CAAA;AACA,EAAAA,0CAAA;AAHW,SAAAA;AAAA,GAAA;AAML,IAAM,wBAAwB,eAAE,mBAAmB,QAAQ;AAAA,EACjE,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,oBAAoB,OAAO;AAAA,IAC3C,QAAQ,eAAE,OAAO;AAAA,IACjB,QAAQ,eAAE,OAAO;AAAA,IACjB,SAAS;AAAA,EACV,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,oBAAoB,gBAAgB;AAAA,IACpD,QAAQ,eAAE,OAAO;AAAA,IACjB,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,oBAAoB,eAAe;AAAA,IACnD,QAAQ,eAAE,OAAO;AAAA,EAClB,CAAC;AACF,CAAC;AAQM,IAAK,wBAAL,kBAAKC,2BAAL;AACN,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,gBAAa;AACb,EAAAA,uBAAA,aAAU;AAHC,SAAAA;AAAA,GAAA;AAML,IAAM,0BAA0B,eAAE,mBAAmB,QAAQ;AAAA,EACnE,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,uBAA6B;AAAA,IAC7C,QAAQ,eAAE,OAAO;AAAA,IACjB,SAAS,eAAE,OAAO;AAAA,MACjB,MAAM,eAAE,OAAO;AAAA,MACf,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,MACrC,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,MAC1B,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,8BAAgC;AAAA,IAChD,QAAQ,eAAE,OAAO;AAAA,IACjB,SAAS,eAAE,OAAO;AAAA,MACjB,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,MAC1B,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,wBAA6B;AAAA,IAC7C,QAAQ,eAAE,OAAO;AAAA,IACjB,SAAS,eAAE,OAAO;AAAA,MACjB,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,MAC1B,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,eAAE,OAAO;AAAA,EACrB,CAAC;AACF,CAAC;AAQM,IAAK,wBAAL,kBAAKC,2BAAL;AACN,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,cAAW;AACX,EAAAA,uBAAA,gBAAa;AAEb,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,WAAQ;AACR,EAAAA,uBAAA,mBAAgB;AAEhB,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,qBAAkB;AAZP,SAAAA;AAAA,GAAA;AAmBL,IAAK,mBAAL,kBAAKC,sBAAL;AACN,EAAAA,kBAAA,UAAO;AACP,EAAAA,kBAAA,WAAQ;AAER,EAAAA,kBAAA,WAAQ;AACR,EAAAA,kBAAA,mBAAgB;AAEhB,EAAAA,kBAAA,aAAU;AACV,EAAAA,kBAAA,qBAAkB;AARP,SAAAA;AAAA,GAAA;;;AgDhqBZ,IAAAC,eAAkB;AA2BX,IAAM,uBAAuB,eAAE,OAAO;AAAA,EAC5C,QAAQ,eAAE,OAAO;AAAA,EACjB,MAAM,eAAE,OAAO,EAAE,SAAS;AAC3B,CAAC;AAKM,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,SAAS,eAAE,MAAM,oBAAoB,EAAE,SAAS;AACjD,CAAC;;;ACtCD,IAAAC,eAAkB;AASX,IAAK,iBAAL,kBAAKC,oBAAL;AACN,EAAAA,gBAAA,aAAU;AACV,EAAAA,gBAAA,gBAAa;AACb,EAAAA,gBAAA,SAAM;AACN,EAAAA,gBAAA,iBAAc;AACd,EAAAA,gBAAA,eAAY;AALD,SAAAA;AAAA,GAAA;AAYL,IAAK,YAAL,kBAAKC,eAAL;AACN,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,YAAS;AAFE,SAAAA;AAAA,GAAA;AASL,IAAM,YAAY,eAAE,OAAO;AAAA,EACjC,UAAU,eAAE,OAAO;AAAA,EACnB,KAAK,eAAE,OAAO;AAAA,EACd,MAAM,eAAE,OAAO;AAChB,CAAC;AAQM,IAAK,kBAAL,kBAAKC,qBAAL;AACN,EAAAA,iBAAA,kBAAe;AACf,EAAAA,iBAAA,gBAAa;AACb,EAAAA,iBAAA,eAAY;AACZ,EAAAA,iBAAA,gBAAa;AAJF,SAAAA;AAAA,GAAA;AAWL,IAAM,oBAAoB,eAAE,mBAAmB,eAAe;AAAA,EACpE,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,iCAA4B;AAAA,IACnD,MAAM,eAAE,OAAO;AAAA,MACd,eAAe;AAAA,MACf,MAAM,eAAE,OAAO;AAAA,MACf,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,MACrC,QAAQ,eAAE,QAAQ,EAAE,SAAS;AAAA,IAC9B,CAAC;AAAA,EACF,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,6BAA0B;AAAA,IACjD,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,2BAAyB;AAAA,IAChD,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,6BAA0B;AAAA,IACjD,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AACF,CAAC;AAQM,IAAM,mBAAmB,eAAE,mBAAmB,QAAQ;AAAA,EAC5D,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,eAAkB;AAAA,IAClC,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,MAAM;AAAA,EACP,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,+BAA0B;AAAA,IAC1C,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,UAAU,eAAE,OAAO;AAAA,IACnB,MAAM;AAAA,EACP,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,2BAAwB;AAAA,IACxC,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,eAAe,eAAE,OAAO,EAAE,SAAS;AAAA,IACnC,MAAM;AAAA,EACP,CAAC;AACF,CAAC;;;ACrGD,IAAAC,eAAkB;AAgBX,IAAM,2BAA2B,eAAE,mBAAmB,UAAU;AAAA,EACtE,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,SAAS;AAAA,IAC3B,YAAY,eAAE,OAAO;AAAA,IACrB,UAAU,eAAE,OAAO;AAAA,EACpB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,QAAQ;AAAA,IAC1B,UAAU,eAAE,OAAO;AAAA,EACpB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,WAAW;AAAA,IAC7B,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,OAAO;AAAA,IACzB,OAAO,eAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,CAAC;AACF,CAAC;;;AC3BM,SAAS,wBAAwB,SAA0B;AACjE,SAAO,QAAQ,SAAS,kBAAkB;AAC3C;;;ACbA,IAAAC,eAAkB;AAKX,IAAM,mBAAmB,eAAE,KAAK,CAAC,WAAW,eAAe,WAAW,CAAU;AAOhF,IAAM,iBAAiB,eAAE,OAAO;AAAA,EACtC,IAAI,eAAE,OAAO;AAAA,EACb,SAAS,eAAE,OAAO;AAAA,EAClB,QAAQ;AACT,CAAC;;;AChBD,IAAAC,eAAkB;AAMX,IAAM,+BAA+B,eAAE,mBAAmB,UAAU;AAAA,EAC1E,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,SAAS;AAAA,IAC3B,KAAK,eAAE,OAAO,EAAE,SAAS;AAAA,IACzB,SAAS,eAAE,OAAO;AAAA,EACnB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,QAAQ;AAAA,IAC1B,QAAQ,eAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,QAAQ;AAAA,IAC1B,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,UAAU;AAAA,EAC7B,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,SAAS;AAAA,EAC5B,CAAC;AACF,CAAC;", "names": ["import_zod", "import_zod", "import_zod", "RooCodeEventName", "import_zod", "TaskStatus", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "TelemetryEventName", "import_zod", "import_zod", "import_zod", "ConnectionState", "ExtensionBridgeEventName", "ExtensionBridgeCommandName", "TaskBridgeEventName", "TaskBridgeCommandName", "ExtensionSocketEvents", "TaskSocketEvents", "import_zod", "import_zod", "IpcMessageType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TaskCommandName", "import_zod", "import_zod", "import_zod"]}