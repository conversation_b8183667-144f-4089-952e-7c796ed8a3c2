{"extension": {"name": "Roo Code", "description": "<PERSON><PERSON> compleet ontwi<PERSON><PERSON><PERSON><PERSON> van AI-agenten in je editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "mrd"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! Je hebt {{count}} meldingen.", "items": {"zero": "Geen items", "one": "Eén item", "other": "{{count}} items"}, "confirmation": {"reset_state": "Weet je zeker dat je alle status en geheime opslag in de extensie wilt resetten? Dit kan niet ongedaan worden gemaakt.", "delete_config_profile": "Weet je zeker dat je dit configuratieprofiel wilt verwijderen?", "delete_custom_mode_with_rules": "Weet je zeker dat je deze {scope}-modus wilt verwijderen?\n\nDit verwijdert ook de bijbehorende regelsmap op:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Ongeldig data-URI-formaat", "error_copying_image": "Fout bij kop<PERSON><PERSON><PERSON>: {{errorMessage}}", "error_saving_image": "Fout bij <PERSON><PERSON><PERSON> van <PERSON>: {{errorMessage}}", "error_opening_image": "Fout bij openen van afbeelding: {{error}}", "could_not_open_file": "Kon bestand niet openen: {{errorMessage}}", "could_not_open_file_generic": "Kon bestand niet openen!", "checkpoint_timeout": "Time-out bij het hers<PERSON><PERSON> van checkpoint.", "checkpoint_failed": "Herstellen van checkpoint mislukt.", "git_not_installed": "Git is vereist voor de checkpoint-functie. Installeer Git om checkpoints in te schakelen.", "no_workspace": "Open eerst een projectmap", "update_support_prompt": "Bijwerken van ondersteuningsprompt mislukt", "reset_support_prompt": "<PERSON><PERSON><PERSON> van ondersteuningsprompt mislukt", "enhance_prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> van prompt mislukt", "get_system_prompt": "<PERSON><PERSON><PERSON> van <PERSON> mislukt", "search_commits": "<PERSON><PERSON> naar commits mislukt", "save_api_config": "<PERSON><PERSON><PERSON> van API-configuratie mis<PERSON>t", "create_api_config": "Aanmaken van API-configuratie mis<PERSON>t", "rename_api_config": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "load_api_config": "<PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "delete_api_config": "Verwijderen van API-configuratie mislukt", "list_api_config": "<PERSON><PERSON><PERSON> met API-configuraties mislukt", "update_server_timeout": "Bijwerken van server-timeout mislukt", "hmr_not_running": "Lokale ontwikkelserver d<PERSON>ait niet, HMR werkt niet. Voer 'npm run dev' uit voordat je de extensie start om HMR in te schakelen.", "retrieve_current_mode": "Fout: <PERSON><PERSON><PERSON> van h<PERSON> modus uit status mislukt.", "failed_delete_repo": "Verwijderen van gekoppelde schaduwrepository of branch mislukt: {{error}}", "failed_remove_directory": "Verwij<PERSON><PERSON> van taakmap mislukt: {{error}}", "custom_storage_path_unusable": "Aangepast opslagpad \"{{path}}\" is onb<PERSON><PERSON><PERSON>ar, standaardpad wordt gebruikt", "cannot_access_path": "Kan pad {{path}} niet openen: {{error}}", "settings_import_failed": "Importeren van instellingen mislukt: {{error}}.", "mistake_limit_guidance": "Dit kan duiden op een fout in het denkproces van het model of het onvermogen om een tool correct te gebruiken, wat kan worden verminderd met gebruikersbegeleiding (bijv. \"<PERSON><PERSON>r de taak op te delen in kleinere stappen\").", "violated_organization_allowlist": "Taak uitvoeren mislukt: het huidige profiel is niet compatibel met de instellingen van uw organisatie", "condense_failed": "Comprim<PERSON><PERSON> van <PERSON> mislukt", "condense_not_enough_messages": "<PERSON>et genoeg berichten om context te comprimeren", "condensed_recently": "Context is recent gecomprimeerd; deze poging wordt overgeslagen", "condense_handler_invalid": "API-handler voor het comprimeren van context is ongeldig", "condense_context_grew": "Contextgrootte nam toe tijdens comprimeren; deze poging wordt overgeslagen", "url_timeout": "De website deed er te lang over om te laden (timeout). Dit kan komen door een trage verbinding, een zware website of tijdelijke onbeschikbaarheid. Je kunt het later opnieuw proberen of controleren of de URL correct is.", "url_not_found": "Het websiteadres kon niet worden gevonden. Controleer of de URL correct is en probeer opnieuw.", "no_internet": "Geen internetverbinding. Controleer je netwerkverbinding en probeer opnieuw.", "url_forbidden": "Toegang tot deze website is verboden. De site kan geautomatiseerde toegang blokkeren of authenticatie vereisen.", "url_page_not_found": "De pagina werd niet gevonden. Controleer of de URL correct is.", "url_request_aborted": "Het verzoek om de URL op te halen is afgebroken. Dit kan gebeuren als de site geautomatiseerde toegang blo<PERSON>, authenticatie vereist of als er een netwerkprobleem is. <PERSON>beer het opnieuw of controleer of de URL toegankelijk is in een normale browser.", "url_fetch_failed": "Fout bij <PERSON><PERSON><PERSON>inhou<PERSON>: {{error}}", "url_fetch_error_with_url": "Fout bij <PERSON><PERSON><PERSON> van in<PERSON> voor {{url}}: {{error}}", "command_timeout": "Time-out bij u<PERSON><PERSON><PERSON><PERSON> van commando na {{seconds}} seconden", "share_task_failed": "<PERSON><PERSON> van ta<PERSON> mislukt", "share_no_active_task": "<PERSON><PERSON> actieve taak om te delen", "share_auth_required": "Authenticatie vereist. Log in om taken te delen.", "share_not_enabled": "Taken delen is niet ingeschakeld voor deze organisatie.", "share_task_not_found": "<PERSON><PERSON> niet gevonden of toegang geweigerd.", "mode_import_failed": "Importeren van modus mislukt: {{error}}", "delete_rules_folder_failed": "Kan regelmap niet verwijderen: {{rulesFolderPath}}. Fout: {{error}}", "command_not_found": "Opdracht '{{name}}' niet gevonden", "open_command_file": "Kan opdrachtbestand niet openen", "delete_command": "Kan op<PERSON>cht niet verwijderen", "no_workspace_for_project_command": "Geen werkruimtemap gevonden voor projectopdracht", "command_already_exists": "Opdracht \"{{commandName}}\" bestaat al", "create_command_failed": "Kan opdracht niet aan<PERSON>ken", "command_template_content": "---\ndescription: \"Korte beschrijving van wat deze opdracht doet\"\n---\n\nDit is een nieuwe slash-opdracht. Bewerk dit bestand om het opdrachtgedrag aan te passen.", "claudeCode": {"processExited": "Claude Code proces beë<PERSON>igd met code {{exitCode}}.", "errorOutput": "Foutuitvoer: {{output}}", "processExitedWithError": "Claude Code proces beëindigd met code {{exitCode}}. Foutuitvoer: {{output}}", "stoppedWithReason": "<PERSON> gestopt om reden: {{reason}}", "apiKeyModelPlanMismatch": "API keys and subscription plans allow different models. Make sure the selected model is included in your plan.", "notFound": "Claude Code executable '{{claudePath}}' not found.\n\nPlease install Claude Code CLI:\n1. Visit {{installationUrl}} to download Claude Code\n2. Follow the installation instructions for your operating system\n3. Ensure the 'claude' command is available in your PATH\n4. Alternatively, configure a custom path in Roo settings under 'Claude Code Path'\n\nOriginal error: {{originalError}}"}, "gemini": {"generate_stream": "Fout bij het genereren van contexts<PERSON>am door Gemini: {{error}}", "generate_complete_prompt": "Fout bij het voltooien door Gemini: {{error}}", "sources": "Bronnen:"}, "cerebras": {"authenticationFailed": "Cerebras API-authenticatie mislukt. Controleer of je API-sleutel geldig is en niet verlopen.", "accessForbidden": "Cerebras API-toegang geweigerd. Je API-sleutel heeft mogelijk geen toegang tot het gevraagde model of de functie.", "rateLimitExceeded": "Cerebras API-snelheidslimiet overschreden. Wacht voordat je een ander verzoek doet.", "serverError": "Cerebras API-serverfout ({{status}}). Probeer het later opnieuw.", "genericError": "Cerebras API-fout ({{status}}): {{message}}", "noResponseBody": "Cerebras API-fout: <PERSON><PERSON> resp<PERSON>", "completionError": "Cerebras-voltooiingsfout: {{error}}"}, "roo": {"authenticationRequired": "Roo provider vereist cloud authenticatie. Log in bij Roo Code Cloud."}, "api": {"invalidKeyInvalidChars": "API-sleutel bevat ongeldige karakters."}}, "warnings": {"no_terminal_content": "<PERSON><PERSON> <PERSON><PERSON> gese<PERSON>d", "missing_task_files": "De bestanden van deze taak ontbreken. Wil je deze uit de takenlijst verwijderen?", "auto_import_failed": "Automatisch importeren van RooCode-instellingen mislukt: {{error}}"}, "info": {"no_changes": "Geen wijzigingen gevonden.", "clipboard_copy": "Systeemprompt succesvol gekopieerd naar klembord", "history_cleanup": "{{count}} taak/taken met ontbrekende bestanden uit geschiedenis verwijderd.", "custom_storage_path_set": "Aangepast opslagpad ingesteld: {{path}}", "default_storage_path": "Terug naar standaard opslagpad", "settings_imported": "Instellingen succesvol geïmporteerd.", "auto_import_success": "RooCode-instellingen automatisch geïmporteerd van {{filename}}", "share_link_copied": "Deellink gekopieerd naar klembord", "image_copied_to_clipboard": "Afbeelding data-URI gekopieerd naar klembord", "image_saved": "Afbeelding opgeslagen naar {{path}}", "organization_share_link_copied": "Organisatie deel-link gekopieerd naar klembord!", "public_share_link_copied": "Openbare deel-link gekopieerd naar klembord!", "mode_exported": "Modus '{{mode}}' succesvol geëxporteerd", "mode_imported": "Modus succesvol geïmporteerd"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "remove": "Verwijderen", "keep": "Behouden"}, "buttons": {"save": "Opsla<PERSON>", "edit": "Bewerken", "learn_more": "Meer informatie"}, "tasks": {"canceled": "Taakfout: gestopt en geannuleerd door gebruiker.", "deleted": "Taakfout: gestopt en verwijderd door gebruiker.", "incomplete": "Taak #{{taskNumber}} (Onvolledig)", "no_messages": "Taak #{{taskNumber}} (<PERSON><PERSON>)"}, "storage": {"prompt_custom_path": "Voer een aangepast opslagpad voor gespreksgeschiedenis in, laat leeg voor standaardlocatie", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Voer een absoluut pad in (bijv. D:\\RooCodeStorage of /home/<USER>/storage)", "enter_valid_path": "<PERSON>oer een geldig pad in"}, "input": {"task_prompt": "Wat moet Roo doen?", "task_placeholder": "<PERSON>p hier je taak"}, "settings": {"providers": {"groqApiKey": "Groq API-sleutel", "getGroqApiKey": "Groq API-sleutel ophalen", "claudeCode": {"pathLabel": "<PERSON> Pad", "description": "Optioneel pad naar je <PERSON> Code CLI. Standaard 'claude' indien niet ingesteld.", "placeholder": "Standaard: claude"}}}, "customModes": {"errors": {"yamlParseError": "Ongeldige YAML in .roomodes bestand op regel {{line}}. Controleer:\n• Juiste inspringing (gebruik spaties, geen tabs)\n• Overeenkomende aanhalingstekens en haakjes\n• Geldige YAML syntaxis", "schemaValidationError": "Ongeldig aangepaste modi formaat in .roomodes:\n{{issues}}", "invalidFormat": "Ongeldig aangepaste modi formaat. Zorg ervoor dat je instellingen het juiste YAML formaat volgen.", "updateFailed": "Aangepaste modus bijwerken mislukt: {{error}}", "deleteFailed": "Aangepaste modus verwijderen mislukt: {{error}}", "resetFailed": "Aangepaste modi resetten mislukt: {{error}}", "modeNotFound": "Schrijffout: <PERSON><PERSON> niet gevo<PERSON>", "noWorkspaceForProject": "Geen workspace map gevonden voor projectspecifieke modus", "rulesCleanupFailed": "Modus succesvol verwijder<PERSON>, maar het verwijderen van de re<PERSON>smap op {{rulesFolderPath}} is mislukt. Je moet deze mogelijk handmatig verwijderen."}, "scope": {"project": "project", "global": "globaal"}}, "marketplace": {"mode": {"rulesCleanupFailed": "Modus succesvol verwijder<PERSON>, maar het verwijderen van de re<PERSON>smap op {{rulesFolderPath}} is mislukt. Je moet deze mogelijk handmatig verwijderen."}}, "mdm": {"errors": {"cloud_auth_required": "Je organisatie vereist Roo Code Cloud-authenticatie. Log in om door te gaan.", "organization_mismatch": "<PERSON> moet geauthe<PERSON><PERSON><PERSON>jn met het Roo Code Cloud-account van je organisatie.", "verification_failed": "Kan organisatie-authenticatie niet veri<PERSON>."}, "info": {"organization_requires_auth": "Je organisatie vereist authenticatie."}}, "prompts": {"deleteMode": {"title": "Aangepaste modus verwijderen", "description": "Weet je zeker dat je deze {{scope}}-modus wilt verwijderen? Dit zal ook de bijbehorende regelsmap op {{rulesFolderPath}} verwijderen", "descriptionNoRules": "Weet je zeker dat je deze aangepaste modus wilt verwijderen?", "confirm": "Verwijderen"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Voorkom taakafronding wanneer er onvolledige todos in de todolijst staan"}}}