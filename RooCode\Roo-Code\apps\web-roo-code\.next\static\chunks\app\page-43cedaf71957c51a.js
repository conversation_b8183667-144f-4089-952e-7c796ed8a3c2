(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{18322:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var a=r(67111),n=r(51046);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},29301:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(47093),n=r(34545),o=r(6255),s=r(2447),l=r(18322);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:s,asChild:d=!1,...c}=e,u=d?o.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:n,size:s,className:r})),ref:t,...c})});d.displayName="Button"},33731:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ChartContainer:()=>m,ChartLegend:()=>p,ChartLegendContent:()=>v,ChartStyle:()=>f,ChartTooltip:()=>h,ChartTooltipContent:()=>g});var a=r(47093),n=r(34545),o=r(57772),s=r(96517),l=r(58090),i=r(18322);let d={light:"",dark:".dark"},c=n.createContext(null);function u(){let e=n.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let m=n.forwardRef((e,t)=>{let{id:r,className:s,children:l,config:d,...u}=e,m=n.useId(),h="chart-".concat(r||m.replace(/:/g,""));return(0,a.jsx)(c.Provider,{value:{config:d},children:(0,a.jsxs)("div",{"data-chart":h,ref:t,className:(0,i.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",s),...u,children:[(0,a.jsx)(f,{id:h,config:d}),(0,a.jsx)(o.u,{children:l})]})})});m.displayName="Chart";let f=e=>{let{id:t,config:r}=e,n=Object.entries(r).filter(e=>{let[,t]=e;return t.theme||t.color});return n.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(d).map(e=>{let[r,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(n.map(e=>{var t;let[a,n]=e,o=(null===(t=n.theme)||void 0===t?void 0:t[r])||n.color;return o?"  --color-".concat(a,": ").concat(o,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},h=s.m,g=n.forwardRef((e,t)=>{let{active:r,payload:o,className:s,indicator:l="dot",hideLabel:d=!1,hideIndicator:c=!1,label:m,labelFormatter:f,labelClassName:h,formatter:g,color:p,nameKey:v,labelKey:b}=e,{config:y}=u(),j=n.useMemo(()=>{var e;if(d||!(null==o?void 0:o.length))return null;let[t]=o,r="".concat(b||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),n=x(y,t,r),s=b||"string"!=typeof m?null==n?void 0:n.label:(null===(e=y[m])||void 0===e?void 0:e.label)||m;return f?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",h),children:f(s,o)}):s?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",h),children:s}):null},[m,f,o,d,h,y,b]);if(!r||!(null==o?void 0:o.length))return null;let N=1===o.length&&"dot"!==l;return(0,a.jsxs)("div",{ref:t,className:(0,i.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",s),children:[N?null:j,(0,a.jsx)("div",{className:"grid gap-1.5",children:o.map((e,t)=>{let r="".concat(v||e.name||e.dataKey||"value"),n=x(y,e,r),o=p||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,i.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===l&&"items-center"),children:g&&(null==e?void 0:e.value)!==void 0&&e.name?g(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==n?void 0:n.icon)?(0,a.jsx)(n.icon,{}):!c&&(0,a.jsx)("div",{className:(0,i.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===l,"w-1":"line"===l,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===l,"my-0.5":N&&"dashed"===l}),style:{"--color-bg":o,"--color-border":o}}),(0,a.jsxs)("div",{className:(0,i.cn)("flex flex-1 justify-between leading-none",N?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[N?j:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==n?void 0:n.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});g.displayName="ChartTooltip";let p=l.s,v=n.forwardRef((e,t)=>{let{className:r,hideIcon:n=!1,payload:o,verticalAlign:s="bottom",nameKey:l}=e,{config:d}=u();return(null==o?void 0:o.length)?(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center justify-center gap-4","top"===s?"pb-3":"pt-3",r),children:o.map(e=>{let t="".concat(l||e.dataKey||"value"),r=x(d,e,t);return(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==r?void 0:r.icon)&&!n?(0,a.jsx)(r.icon,{}):(0,a.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==r?void 0:r.label]},e.value)})}):null});function x(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,n=r;return r in t&&"string"==typeof t[r]?n=t[r]:a&&r in a&&"string"==typeof a[r]&&(n=a[r]),n in e?e[n]:e[r]}v.displayName="ChartLegend"},52496:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Dialog:()=>i,DialogClose:()=>u,DialogContent:()=>f,DialogDescription:()=>v,DialogFooter:()=>g,DialogHeader:()=>h,DialogOverlay:()=>m,DialogPortal:()=>c,DialogTitle:()=>p,DialogTrigger:()=>d});var a=r(47093),n=r(34545),o=r(81517),s=r(7676),l=r(18322);let i=o.bL,d=o.l9,c=o.ZL,u=o.bm,m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n})});m.displayName=o.hJ.displayName;let f=n.forwardRef((e,t)=>{let{className:r,children:n,...i}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(o.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...i,children:[n,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=o.UC.displayName;let h=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};h.displayName="DialogHeader";let g=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};g.displayName="DialogFooter";let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",r),...n})});p.displayName=o.hE.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});v.displayName=o.VY.displayName},64979:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ScrollButton:()=>o});var a=r(47093),n=r(28923);function o(e){let{targetId:t,children:r,className:o="",onClick:s}=e,l=(0,n.useRouter)(),i=(0,n.usePathname)();return(0,a.jsx)("button",{onClick:()=>{if("/"===i){let e=document.getElementById(t);null==e||e.scrollIntoView({behavior:"smooth"})}else l.push("/#".concat(t));null==s||s()},className:o,children:r})}},91900:(e,t,r)=>{Promise.resolve().then(r.bind(r,21925)),Promise.resolve().then(r.bind(r,33001)),Promise.resolve().then(r.bind(r,99697)),Promise.resolve().then(r.bind(r,48503)),Promise.resolve().then(r.bind(r,7615)),Promise.resolve().then(r.bind(r,69157)),Promise.resolve().then(r.bind(r,64518)),Promise.resolve().then(r.bind(r,70678)),Promise.resolve().then(r.bind(r,52032)),Promise.resolve().then(r.bind(r,21470)),Promise.resolve().then(r.bind(r,33731)),Promise.resolve().then(r.bind(r,52496)),Promise.resolve().then(r.bind(r,64979))}},e=>{var t=t=>e(e.s=t);e.O(0,[858,687,156,959,957,335,550,358],()=>t(91900)),_N_E=e.O()}]);