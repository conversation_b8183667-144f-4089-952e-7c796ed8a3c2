{"extension": {"name": "Roo Code", "description": "आपके एडिटर में AI डेवलपर्स की पूरी टीम।"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "स्वागत है, {{name}}! आपके पास {{count}} सूचनाएँ हैं।", "items": {"zero": "कोई आइटम नहीं", "one": "एक आइटम", "other": "{{count}} आइटम"}, "confirmation": {"reset_state": "क्या आप वाकई एक्सटेंशन में सभी स्टेट और गुप्त स्टोरेज रीसेट करना चाहते हैं? इसे पूर्ववत नहीं किया जा सकता है।", "delete_config_profile": "क्या आप वाकई इस कॉन्फ़िगरेशन प्रोफ़ाइल को हटाना चाहते हैं?", "delete_custom_mode_with_rules": "क्या आप वाकई इस {scope} मोड को हटाना चाहते हैं?\n\nयह संबंधित नियम फ़ोल्डर को भी यहाँ हटा देगा:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "अमान्य डेटा URI फॉर्मेट", "error_copying_image": "छवि कॉपी करने में त्रुटि: {{errorMessage}}", "error_saving_image": "छवि सहेजने में त्रुटि: {{errorMessage}}", "error_opening_image": "छवि खोलने में त्रुटि: {{error}}", "could_not_open_file": "फ़ाइल नहीं खोली जा सकी: {{errorMessage}}", "could_not_open_file_generic": "फ़ाइल नहीं खोली जा सकी!", "checkpoint_timeout": "चेकपॉइंट को पुनर्स्थापित करने का प्रयास करते समय टाइमआउट हो गया।", "checkpoint_failed": "चेकपॉइंट पुनर्स्थापित करने में विफल।", "git_not_installed": "चेकपॉइंट सुविधा के लिए Git आवश्यक है। कृपया चेकपॉइंट সক্ষম करने के लिए Git इंस्टॉल करें।", "no_workspace": "कृपया पहले प्रोजेक्ट फ़ोल्डर खोलें", "update_support_prompt": "सपोर्ट प्रॉम्प्ट अपडेट करने में विफल", "reset_support_prompt": "सपोर्ट प्रॉम्प्ट रीसेट करने में विफल", "enhance_prompt": "प्रॉम्प्ट को बेहतर बनाने में विफल", "get_system_prompt": "सिस्टम प्रॉम्प्ट प्राप्त करने में विफल", "search_commits": "कमिट्स खोजने में विफल", "save_api_config": "API कॉन्फ़िगरेशन सहेजने में विफल", "create_api_config": "API कॉन्फ़िगरेशन बनाने में विफल", "rename_api_config": "API कॉन्फ़िगरेशन का नाम बदलने में विफल", "load_api_config": "API कॉन्फ़िगरेशन लोड करने में विफल", "delete_api_config": "API कॉन्फ़िगरेशन हटाने में विफल", "list_api_config": "API कॉन्फ़िगरेशन की सूची प्राप्त करने में विफल", "update_server_timeout": "सर्वर टाइमआउट अपडेट करने में विफल", "hmr_not_running": "स्थानीय विकास सर्वर चल नहीं रहा है, HMR काम नहीं करेगा। कृपया HMR सक्षम करने के लिए एक्सटेंशन लॉन्च करने से पहले 'npm run dev' चलाएँ।", "retrieve_current_mode": "स्टेट से वर्तमान मोड प्राप्त करने में त्रुटि।", "failed_delete_repo": "संबंधित शैडो रिपॉजिटरी या ब्रांच हटाने में विफल: {{error}}", "failed_remove_directory": "टास्क डायरेक्टरी हटाने में विफल: {{error}}", "custom_storage_path_unusable": "कस्टम स्टोरेज पाथ \"{{path}}\" उपयोग योग्य नहीं है, डिफ़ॉल्ट पाथ का उपयोग किया जाएगा", "cannot_access_path": "पाथ {{path}} तक पहुंच नहीं पा रहे हैं: {{error}}", "settings_import_failed": "सेटिंग्स इम्पोर्ट करने में विफल: {{error}}।", "mistake_limit_guidance": "यह मॉडल की सोच प्रक्रिया में विफलता या किसी टूल का सही उपयोग न कर पाने का संकेत हो सकता है, जिसे उपयोगकर्ता के मार्गदर्शन से ठीक किया जा सकता है (जैसे \"कार्य को छोटे चरणों में बांटने की कोशिश करें\")।", "violated_organization_allowlist": "कार्य चलाने में विफल: वर्तमान प्रोफ़ाइल आपके संगठन की सेटिंग्स के साथ संगत नहीं है", "condense_failed": "संदर्भ को संक्षिप्त करने में विफल", "condense_not_enough_messages": "संदर्भ को संक्षिप्त करने के लिए पर्याप्त संदेश नहीं हैं", "condensed_recently": "संदर्भ हाल ही में संक्षिप्त किया गया था; इस प्रयास को छोड़ा जा रहा है", "condense_handler_invalid": "संदर्भ को संक्षिप्त करने के लिए API हैंडलर अमान्य है", "condense_context_grew": "संक्षिप्तीकरण के दौरान संदर्भ का आकार बढ़ गया; इस प्रयास को छोड़ा जा रहा है", "url_timeout": "वेबसाइट लोड होने में बहुत समय लगा (टाइमआउट)। यह धीमे कनेक्शन, भारी वेबसाइट या अस्थायी रूप से अनुपलब्ध होने के कारण हो सकता है। आप बाद में फिर से कोशिश कर सकते हैं या जांच सकते हैं कि URL सही है या नहीं।", "url_not_found": "वेबसाइट का पता नहीं मिल सका। कृपया जांचें कि URL सही है और फिर से कोशिश करें।", "no_internet": "इंटरनेट कनेक्शन नहीं है। कृपया अपना नेटवर्क कनेक्शन जांचें और फिर से कोशिश करें।", "url_forbidden": "इस वेबसाइट तक पहुंच प्रतिबंधित है। साइट स्वचालित पहुंच को ब्लॉक कर सकती है या प्रमाणीकरण की आवश्यकता हो सकती है।", "url_page_not_found": "पेज नहीं मिला। कृपया जांचें कि URL सही है।", "url_request_aborted": "URL प्राप्त करने का अनुरोध रद्द कर दिया गया। यह तब हो सकता है जब साइट स्वचालित पहुंच को ब्लॉक करती है, प्रमाणीकरण की आवश्यकता होती है या नेटवर्क समस्या है। कृपया फिर से प्रयास करें या जांचें कि URL सामान्य ब्राउज़र में सुलभ है या नहीं।", "url_fetch_failed": "URL सामग्री प्राप्त करने में त्रुटि: {{error}}", "url_fetch_error_with_url": "{{url}} के लिए सामग्री प्राप्त करने में त्रुटि: {{error}}", "command_timeout": "कमांड निष्पादन {{seconds}} सेकंड के बाद समय समाप्त हो गया", "share_task_failed": "कार्य साझा करने में विफल। कृपया पुनः प्रयास करें।", "share_no_active_task": "साझा करने के लिए कोई सक्रिय कार्य नहीं", "share_auth_required": "प्रमाणीकरण आवश्यक है। कार्य साझा करने के लिए कृपया साइन इन करें।", "share_not_enabled": "इस संगठन के लिए कार्य साझाकरण सक्षम नहीं है।", "share_task_not_found": "कार्य नहीं मिला या पहुंच अस्वीकृत।", "mode_import_failed": "मोड आयात करने में विफल: {{error}}", "delete_rules_folder_failed": "नियम फ़ोल्डर हटाने में विफल: {{rulesFolderPath}}। त्रुटि: {{error}}", "command_not_found": "कमांड '{{name}}' नहीं मिला", "open_command_file": "कमांड फ़ाइल खोलने में विफल", "delete_command": "कमांड हटाने में विफल", "no_workspace_for_project_command": "प्रोजेक्ट कमांड के लिए वर्कस्पेस फ़ोल्डर नहीं मिला", "command_already_exists": "कमांड \"{{commandName}}\" पहले से मौजूद है", "create_command_failed": "कमांड बनाने में विफल", "command_template_content": "---\ndescription: \"इस कमांड के कार्य का संक्षिप्त विवरण\"\n---\n\nयह एक नया स्लैश कमांड है। कमांड व्यवहार को कस्टमाइज़ करने के लिए इस फ़ाइल को संपादित करें।", "claudeCode": {"processExited": "<PERSON> Code प्रक्रिया कोड {{exitCode}} के साथ समाप्त हुई।", "errorOutput": "त्रुटि आउटपुट: {{output}}", "processExitedWithError": "<PERSON> Code प्रक्रिया कोड {{exitCode}} के साथ समाप्त हुई। त्रुटि आउटपुट: {{output}}", "stoppedWithReason": "Claude Code इस कारण से रुका: {{reason}}", "apiKeyModelPlanMismatch": "API keys and subscription plans allow different models. Make sure the selected model is included in your plan.", "notFound": "Claude Code executable '{{claudePath}}' not found.\n\nPlease install Claude Code CLI:\n1. Visit {{installationUrl}} to download Claude Code\n2. Follow the installation instructions for your operating system\n3. Ensure the 'claude' command is available in your PATH\n4. Alternatively, configure a custom path in Roo settings under 'Claude Code Path'\n\nOriginal error: {{originalError}}"}, "gemini": {"generate_stream": "जेमिनी जनरेट कॉन्टेक्स्ट स्ट्रीम त्रुटि: {{error}}", "generate_complete_prompt": "जेमिनी समापन त्रुटि: {{error}}", "sources": "स्रोत:"}, "cerebras": {"authenticationFailed": "Cerebras API प्रमाणीकरण विफल हुआ। कृपया जांचें कि आपकी API कुंजी वैध है और समाप्त नहीं हुई है।", "accessForbidden": "Cerebras API पहुंच निषेध। आपकी API कुंजी का अनुरोधित मॉडल या सुविधा तक पहुंच नहीं हो सकती है।", "rateLimitExceeded": "Cerebras API दर सीमा पार हो गई। कृपया दूसरा अनुरोध करने से पहले प्रतीक्षा करें।", "serverError": "Cerebras API सर्वर त्रुटि ({{status}})। कृपया बाद में पुनः प्रयास करें।", "genericError": "Cerebras API त्रुटि ({{status}}): {{message}}", "noResponseBody": "Cerebras API त्रुटि: कोई प्रतिक्रिया मुख्य भाग नहीं", "completionError": "Cerebras पूर्णता त्रुटि: {{error}}"}, "roo": {"authenticationRequired": "Roo प्रदाता को क्लाउड प्रमाणीकरण की आवश्यकता है। कृपया Roo Code Cloud में साइन इन करें।"}, "api": {"invalidKeyInvalidChars": "API कुंजी में अमान्य वर्ण हैं।"}}, "warnings": {"no_terminal_content": "कोई टर्मिनल सामग्री चयनित नहीं", "missing_task_files": "इस टास्क की फाइलें गायब हैं। क्या आप इसे टास्क सूची से हटाना चाहते हैं?", "auto_import_failed": "RooCode सेटिंग्स का स्वचालित आयात विफल: {{error}}"}, "info": {"no_changes": "कोई परिवर्तन नहीं मिला।", "clipboard_copy": "सिस्टम प्रॉम्प्ट क्लिपबोर्ड पर सफलतापूर्वक कॉपी किया गया", "history_cleanup": "इतिहास से गायब फाइलों वाले {{count}} टास्क साफ किए गए।", "custom_storage_path_set": "कस्टम स्टोरेज पाथ सेट किया गया: {{path}}", "default_storage_path": "डिफ़ॉल्ट स्टोरेज पाथ का उपयोग पुनः शुरू किया गया", "settings_imported": "सेटिंग्स सफलतापूर्वक इम्पोर्ट की गईं।", "auto_import_success": "RooCode सेटिंग्स {{filename}} से स्वचालित रूप से आयात की गईं", "share_link_copied": "साझा लिंक क्लिपबोर्ड पर कॉपी किया गया", "image_copied_to_clipboard": "छवि डेटा URI क्लिपबोर्ड में कॉपी की गई", "image_saved": "छवि {{path}} में सहेजी गई", "organization_share_link_copied": "संगठन साझाकरण लिंक क्लिपबोर्ड में कॉपी किया गया!", "public_share_link_copied": "सार्वजनिक साझाकरण लिंक क्लिपबोर्ड में कॉपी किया गया!", "mode_exported": "मोड '{{mode}}' सफलतापूर्वक निर्यात किया गया", "mode_imported": "मोड सफलतापूर्वक आयात किया गया"}, "answers": {"yes": "हां", "no": "नहीं", "remove": "हटाएं", "keep": "रखें"}, "buttons": {"save": "सहेजें", "edit": "संपादित करें", "learn_more": "और अधिक जानें"}, "tasks": {"canceled": "टास्क त्रुटि: इसे उपयोगकर्ता द्वारा रोका और रद्द किया गया था।", "deleted": "टास्क विफलता: इसे उपयोगकर्ता द्वारा रोका और हटाया गया था।", "incomplete": "टास्क #{{taskNumber}} (अधूरा)", "no_messages": "टास्क #{{taskNumber}} (कोई संदेश नहीं)"}, "storage": {"prompt_custom_path": "वार्तालाप इतिहास के लिए कस्टम स्टोरेज पाथ दर्ज करें, डिफ़ॉल्ट स्थान का उपयोग करने के लिए खाली छोड़ दें", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "कृपया एक पूर्ण पाथ दर्ज करें (उदाहरण: D:\\RooCodeStorage या /home/<USER>/storage)", "enter_valid_path": "कृपया एक वैध पाथ दर्ज करें"}, "input": {"task_prompt": "Roo को क्या करना है?", "task_placeholder": "अपना कार्य यहाँ लिखें"}, "settings": {"providers": {"groqApiKey": "ग्रोक एपीआई कुंजी", "getGroqApiKey": "ग्रोक एपीआई कुंजी प्राप्त करें", "claudeCode": {"pathLabel": "क्लाउड कोड पाथ", "description": "आपके क्लाउड कोड CLI का वैकल्पिक पाथ। सेट न होने पर डिफ़ॉल्ट रूप से 'claude'।", "placeholder": "डिफ़ॉल्ट: claude"}}}, "customModes": {"errors": {"yamlParseError": ".roomodes फ़ाइल में लाइन {{line}} पर अमान्य YAML। कृपया जांचें:\n• सही इंडेंटेशन (टैब नहीं, स्पेस का उपयोग करें)\n• मैचिंग कोट्स और ब्रैकेट्स\n• वैध YAML सिंटैक्स", "schemaValidationError": ".roomodes में अमान्य कस्टम मोड फॉर्मेट:\n{{issues}}", "invalidFormat": "अमान्य कस्टम मोड फॉर्मेट। कृपया सुनिश्चित करें कि आपकी सेटिंग्स सही YAML फॉर्मेट का पालन करती हैं।", "updateFailed": "कस्टम मोड अपडेट विफल: {{error}}", "deleteFailed": "कस्टम मोड डिलीट विफल: {{error}}", "resetFailed": "कस्टम मोड रीसेट विफल: {{error}}", "modeNotFound": "लेखन त्रुटि: मोड नहीं मिला", "noWorkspaceForProject": "प्रोजेक्ट-विशिष्ट मोड के लिए वर्कस्पेस फ़ोल्डर नहीं मिला", "rulesCleanupFailed": "मोड सफलतापूर्वक हटा दिया गया, लेकिन {{rulesFolderPath}} पर नियम फ़ोल्डर को हटाने में विफल रहा। आपको इसे मैन्युअल रूप से हटाना पड़ सकता है।"}, "scope": {"project": "परियोजना", "global": "वैश्विक"}}, "marketplace": {"mode": {"rulesCleanupFailed": "मोड सफलतापूर्वक हटा दिया गया, लेकिन {{rulesFolderPath}} पर नियम फ़ोल्डर को हटाने में विफल रहा। आपको इसे मैन्युअल रूप से हटाना पड़ सकता है।"}}, "mdm": {"errors": {"cloud_auth_required": "आपके संगठन को Roo Code Cloud प्रमाणीकरण की आवश्यकता है। कृपया जारी रखने के लिए साइन इन करें।", "organization_mismatch": "आपको अपने संगठन के Roo Code Cloud खाते से प्रमाणित होना होगा।", "verification_failed": "संगठन प्रमाणीकरण सत्यापित करने में असमर्थ।"}, "info": {"organization_requires_auth": "आपके संगठन को प्रमाणीकरण की आवश्यकता है।"}}, "prompts": {"deleteMode": {"title": "कस्टम मोड हटाएं", "description": "क्या आप वाकई इस {{scope}} मोड को हटाना चाहते हैं? यह संबंधित नियम फ़ोल्डर को भी {{rulesFolderPath}} पर हटा देगा", "descriptionNoRules": "क्या आप वाकई इस कस्टम मोड को हटाना चाहते हैं?", "confirm": "हटाएं"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "जब टूडू सूची में अधूरे टूडू हों तो कार्य पूर्ण होने से रोकें"}}}