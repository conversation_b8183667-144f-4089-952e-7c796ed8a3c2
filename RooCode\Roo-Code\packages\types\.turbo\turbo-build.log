
> @roo-code/types@0.0.0 build C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\types
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.5.0
[34mCLI[39m Using tsup config: C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\types\tsup.config.ts
[34mCLI[39m Target: es2022
[34mCLI[39m Cleaning output folder
[34mCJS[39m Build start
[34mESM[39m Build start
[32mESM[39m [1mdist\index.js     [22m[32m188.26 KB[39m
[32mESM[39m [1mdist\index.js.map [22m[32m348.72 KB[39m
[32mESM[39m ⚡️ Build success in 207ms
[32mCJS[39m [1mdist\index.cjs     [22m[32m212.90 KB[39m
[32mCJS[39m [1mdist\index.cjs.map [22m[32m350.03 KB[39m
[32mCJS[39m ⚡️ Build success in 238ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 10786ms
[32mDTS[39m [1mdist\index.d.cts [22m[32m2.09 MB[39m
[32mDTS[39m [1mdist\index.d.ts  [22m[32m2.09 MB[39m
