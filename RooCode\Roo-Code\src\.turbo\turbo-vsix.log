 WARN  Unsupported engine: wanted: {"node":"20.19.2"} (current: {"node":"v22.16.0","pnpm":"10.8.1"})

> roo-cline-custom@3.27.0 vsix C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src
> mkdirp ../bin && vsce package --no-dependencies --out ../bin

Executing prepublish script 'npm run vscode:prepublish'...

> roo-cline-custom@3.27.0 vscode:prepublish
> node esbuild.mjs --production

[extension] Cleaning dist directory: C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist
[esbuild-problem-matcher#onStart]
[copyPaths] Copied ../README.md to README.md
[copyPaths] Copied ../CHANGELOG.md to CHANGELOG.md
[copyPaths] Copied ../LICENSE to LICENSE
[copyPaths] Copied 911 files from node_modules/vscode-material-icons/generated to assets/vscode-material-icons
[copyPaths] Copied 3 files from ../webview-ui/audio to webview-ui/audio
[copyWasms] Copied tiktoken WASMs to C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist
[copyWasms] Copied tiktoken WASMs to C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist\workers
[copyWasms] Copied tree-sitter.wasm to C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist
[copyWasms] Copied 35 tree-sitter language wasms to C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist
[copyLocales] Copied 90 locale files to C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\src\dist\i18n\locales
[esbuild-problem-matcher#onEnd]
 WARNING  This extension consists of 1717 files, out of which 286 are JavaScript files. For performance reasons, you should bundle your extension: https://aka.ms/vscode-bundle-extension. You should also exclude unnecessary files by adding them to your .vscodeignore: https://aka.ms/vscode-vscodeignore.

 INFO  Files included in the VSIX:
roo-cline-custom-3.27.0.vsix
├─ [Content_Types].xml 
├─ extension.vsixmanifest 
└─ extension/
   ├─ LICENSE.txt [11.27 KB]
   ├─ changelog.md [105.05 KB]
   ├─ package.json [14.93 KB]
   ├─ package.nls.ca.json [3.81 KB]
   ├─ package.nls.de.json [3.77 KB]
   ├─ package.nls.es.json [3.87 KB]
   ├─ package.nls.fr.json [3.94 KB]
   ├─ package.nls.hi.json [6.39 KB]
   ├─ package.nls.id.json [3.55 KB]
   ├─ package.nls.it.json [3.84 KB]
   ├─ package.nls.ja.json [4.17 KB]
   ├─ package.nls.json [3.65 KB]
   ├─ package.nls.ko.json [3.73 KB]
   ├─ package.nls.nl.json [3.69 KB]
   ├─ package.nls.pl.json [3.8 KB]
   ├─ package.nls.pt-BR.json [3.84 KB]
   ├─ package.nls.ru.json [5.29 KB]
   ├─ package.nls.tr.json [3.68 KB]
   ├─ package.nls.vi.json [4.19 KB]
   ├─ package.nls.zh-CN.json [3.3 KB]
   ├─ package.nls.zh-TW.json [3.3 KB]
   ├─ readme.md [60.88 KB]
   ├─ assets/ (921 files) [1.38 MB]
   ├─ dist/ (132 files) [86.79 MB]
   ├─ integrations/ (8 files) [57.6 KB]
   └─ webview-ui/ (632 files) [45.75 MB]

=> Run vsce ls --tree to see all included files.

 DONE  Packaged: ..\bin\roo-cline-custom-3.27.0.vsix (1717 files, 27.13 MB)
