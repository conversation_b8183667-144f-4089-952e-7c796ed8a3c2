(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[352],{45291:(e,s,r)=>{"use strict";r.d(s,{ContactForm:()=>c});var n=r(47093),t=r(34545),o=r(39860),i=r(91554),a=r(50481);let l=o.z.object({name:o.z.string().min(1,"Name is required"),company:o.z.string().min(1,"Company is required"),email:o.z.string().email("Invalid email address"),website:o.z.string().url("Invalid website URL").or(o.z.string().length(0)),engineerCount:o.z.enum(["1-10","11-50","51-200","201-500","501-1000","1000+"]),formType:o.z.enum(["early-access","demo"]),_honeypot:o.z.string().optional()});function c(e){let{formType:s,buttonText:r,buttonClassName:c}=e,[d,m]=(0,t.useState)(!1),[u,h]=(0,t.useState)(!1),[p,x]=(0,t.useState)({}),[b,g]=(0,t.useState)("idle"),f=(0,t.useRef)(null),y=a.env.NEXT_PUBLIC_BASIN_ENDPOINT||"";y||console.warn("NEXT_PUBLIC_BASIN_ENDPOINT is not configured. Form submissions will not work.");let j=async e=>{e.preventDefault(),h(!0),x({}),g("idle");let r=e.currentTarget,n=new FormData(r),t={name:n.get("name"),company:n.get("company"),email:n.get("email"),website:n.get("website"),engineerCount:n.get("engineerCount"),formType:s,_honeypot:n.get("_honeypot")};try{l.parse(t);try{let e=await fetch(y,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},mode:"cors",body:JSON.stringify(t)});if(e.ok)try{let s=await e.json();s&&(!0===s.success||"success"===s.status)?(g("success"),r&&r.reset()):(console.error("Basin error:",s),g("error"))}catch(e){console.error("Error parsing JSON response:",e),g("success"),r&&r.reset()}else{try{let s=await e.json();console.error("Basin API error:",e.status,s)}catch(s){console.error("Basin returned error status:",e.status)}g("error")}}catch(e){console.error("Error submitting form:",e),g("error")}}catch(e){if(e instanceof o.z.ZodError){let s={};e.errors.forEach(e=>{e.path[0]&&(s[e.path[0]]=e.message)}),x(s)}else g("error")}finally{h(!1)}};return(0,n.jsxs)(i.lG,{open:d,onOpenChange:m,children:[(0,n.jsx)(i.zM,{asChild:!0,children:(0,n.jsx)(i.$n,{className:c||"",children:r})}),(0,n.jsxs)(i.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(i.c7,{children:[(0,n.jsx)(i.L3,{children:"early-access"===s?"Become an Early Access Partner":"Request a Demo"}),(0,n.jsx)(i.rr,{children:"early-access"===s?"Fill out the form below to collaborate in shaping Roo Code's enterprise solution.":"Fill out the form below to see Roo Code's enterprise capabilities in action."})]}),"success"===b?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center py-6",children:[(0,n.jsx)("div",{className:"mb-4 rounded-full bg-green-100 p-3 text-green-600 dark:bg-green-900/20 dark:text-green-400",children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,n.jsx)("h3",{className:"mb-2 text-xl font-bold",children:"Thank You!"}),(0,n.jsx)("p",{className:"text-center text-muted-foreground",children:"Your information has been submitted successfully. Our team will be in touch with you shortly."}),(0,n.jsx)(i.$n,{className:"mt-4",onClick:()=>m(!1),children:"Close"})]}):(0,n.jsxs)("form",{ref:f,onSubmit:j,className:"space-y-4","data-basin-form":!0,children:[(0,n.jsx)("input",{type:"text",name:"_honeypot",className:"hidden",style:{display:"none"}}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{htmlFor:"name",className:"text-sm font-medium",children:["Name ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{id:"name",name:"name",type:"text",className:"w-full rounded-md border ".concat(p.name?"border-red-500":"border-input"," bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"),placeholder:"Your name"}),p.name&&(0,n.jsx)("p",{className:"text-xs text-red-500",children:p.name})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{htmlFor:"company",className:"text-sm font-medium",children:["Company ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{id:"company",name:"company",type:"text",className:"w-full rounded-md border ".concat(p.company?"border-red-500":"border-input"," bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"),placeholder:"Your company"}),p.company&&(0,n.jsx)("p",{className:"text-xs text-red-500",children:p.company})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{htmlFor:"email",className:"text-sm font-medium",children:["Email ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{id:"email",name:"email",type:"email",className:"w-full rounded-md border ".concat(p.email?"border-red-500":"border-input"," bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"),placeholder:"<EMAIL>"}),p.email&&(0,n.jsx)("p",{className:"text-xs text-red-500",children:p.email})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"website",className:"text-sm font-medium",children:"Website"}),(0,n.jsx)("input",{id:"website",name:"website",type:"url",className:"w-full rounded-md border ".concat(p.website?"border-red-500":"border-input"," bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"),placeholder:"https://example.com"}),p.website&&(0,n.jsx)("p",{className:"text-xs text-red-500",children:p.website})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{htmlFor:"engineerCount",className:"text-sm font-medium",children:["Number of Software Engineers ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)("select",{id:"engineerCount",name:"engineerCount",className:"w-full rounded-md border ".concat(p.engineerCount?"border-red-500":"border-input"," bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"),defaultValue:"1-10",children:[(0,n.jsx)("option",{value:"1-10",children:"1-10"}),(0,n.jsx)("option",{value:"11-50",children:"11-50"}),(0,n.jsx)("option",{value:"51-200",children:"51-200"}),(0,n.jsx)("option",{value:"201-500",children:"201-500"}),(0,n.jsx)("option",{value:"501-1000",children:"501-1000"}),(0,n.jsx)("option",{value:"1000+",children:"1000+"})]}),p.engineerCount&&(0,n.jsx)("p",{className:"text-xs text-red-500",children:p.engineerCount})]}),"error"===b&&(0,n.jsx)("div",{className:"rounded-md bg-red-50 p-3 text-sm text-red-500 dark:bg-red-900/20",children:"There was an error submitting your request. Please try again later."}),(0,n.jsxs)(i.Es,{children:[(0,n.jsx)(i.$n,{type:"button",variant:"outline",onClick:()=>m(!1),children:"Cancel"}),(0,n.jsx)(i.$n,{type:"submit",disabled:u,children:u?"Submitting...":"Submit"})]})]})]})]})}},95276:(e,s,r)=>{Promise.resolve().then(r.bind(r,21925)),Promise.resolve().then(r.bind(r,45291)),Promise.resolve().then(r.bind(r,33001)),Promise.resolve().then(r.bind(r,99697)),Promise.resolve().then(r.bind(r,48503)),Promise.resolve().then(r.bind(r,7615)),Promise.resolve().then(r.bind(r,69157)),Promise.resolve().then(r.bind(r,64518)),Promise.resolve().then(r.bind(r,70678)),Promise.resolve().then(r.bind(r,52032)),Promise.resolve().then(r.bind(r,21470)),Promise.resolve().then(r.bind(r,33731)),Promise.resolve().then(r.bind(r,52496)),Promise.resolve().then(r.bind(r,64979))}},e=>{var s=s=>e(e.s=s);e.O(0,[858,687,156,959,860,554,957,335,550,358],()=>s(95276)),_N_E=e.O()}]);