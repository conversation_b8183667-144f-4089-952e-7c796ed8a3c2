{"unknownError": "알 수 없는 오류", "authenticationFailed": "임베딩 생성 실패: 인증에 실패했습니다. API 키를 확인하세요.", "failedWithStatus": "{{attempts}}번 시도 후 임베딩 생성 실패: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "{{attempts}}번 시도 후 임베딩 생성 실패: {{errorMessage}}", "failedMaxAttempts": "{{attempts}}번 시도 후 임베딩 생성 실패", "textExceedsTokenLimit": "인덱스 {{index}}의 텍스트가 최대 토큰 제한({{itemTokens}} > {{maxTokens}})을 초과했습니다. 건너뜁니다.", "rateLimitRetry": "속도 제한에 도달했습니다. {{delayMs}}ms 후에 다시 시도합니다(시도 {{attempt}}/{{maxRetries}}).", "ollama": {"couldNotReadErrorBody": "오류 본문을 읽을 수 없습니다", "requestFailed": "Ollama API 요청이 실패했습니다. 상태 {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Ollama API에서 잘못된 응답 구조: \"embeddings\" 배열을 찾을 수 없거나 배열이 아닙니다.", "embeddingFailed": "Ollama 임베딩 실패: {{message}}", "serviceNotRunning": "Ollama 서비스가 {{baseUrl}}에서 실행되고 있지 않습니다", "serviceUnavailable": "Ollama 서비스를 사용할 수 없습니다 (상태: {{status}})", "modelNotFound": "Ollama 모델을 찾을 수 없습니다: {{modelId}}", "modelNotEmbeddingCapable": "Ollama 모델은 임베딩이 불가능합니다: {{modelId}}", "hostNotFound": "Ollama 호스트를 찾을 수 없습니다: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "파일 {{filePath}} 처리 중 알 수 없는 오류", "unknownErrorDeletingPoints": "{{filePath}}의 포인트 삭제 중 알 수 없는 오류", "failedToProcessBatchWithError": "{{maxRetries}}번 시도 후 배치 처리 실패: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Qdrant 벡터 데이터베이스에 연결하지 못했습니다. Qdrant가 실행 중이고 {{qdrantUrl}}에서 접근 가능한지 확인하세요. 오류: {{errorMessage}}", "vectorDimensionMismatch": "새 모델의 벡터 인덱스를 업데이트하지 못했습니다. 인덱스를 지우고 다시 시작해 보세요. 세부 정보: {{errorMessage}}"}, "validation": {"authenticationFailed": "인증에 실패했습니다. 설정에서 API 키를 확인하세요.", "connectionFailed": "임베더 서비스에 연결하지 못했습니다. 연결 설정을 확인하고 서비스가 실행 중인지 확인하세요.", "modelNotAvailable": "지정된 모델을 사용할 수 없습니다. 모델 구성을 확인하세요.", "configurationError": "잘못된 임베더 구성입니다. 설정을 검토하세요.", "serviceUnavailable": "임베더 서비스를 사용할 수 없습니다. 실행 중이고 액세스 가능한지 확인하세요.", "invalidEndpoint": "잘못된 API 엔드포인트입니다. URL 구성을 확인하세요.", "invalidEmbedderConfig": "잘못된 임베더 구성입니다. 설정을 확인하세요.", "invalidApiKey": "잘못된 API 키입니다. API 키 구성을 확인하세요.", "invalidBaseUrl": "잘못된 기본 URL입니다. URL 구성을 확인하세요.", "invalidModel": "잘못된 모델입니다. 모델 구성을 확인하세요.", "invalidResponse": "임베더 서비스에서 잘못된 응답이 왔습니다. 구성을 확인하세요.", "apiKeyRequired": "이 임베더에는 API 키가 필요합니다", "baseUrlRequired": "이 임베더에는 기본 URL이 필요합니다"}, "serviceFactory": {"openAiConfigMissing": "임베더 생성을 위한 OpenAI 구성이 누락되었습니다", "ollamaConfigMissing": "임베더 생성을 위한 Ollama 구성이 누락되었습니다", "openAiCompatibleConfigMissing": "임베더 생성을 위한 OpenAI 호환 구성이 누락되었습니다", "geminiConfigMissing": "임베더 생성을 위한 Gemini 구성이 누락되었습니다", "mistralConfigMissing": "임베더 생성을 위한 Mistral 구성이 없습니다", "vercelAiGatewayConfigMissing": "임베더 생성을 위한 Vercel AI Gateway 구성이 없습니다", "invalidEmbedderType": "잘못된 임베더 유형이 구성되었습니다: {{embed<PERSON><PERSON><PERSON><PERSON>}}", "vectorDimensionNotDeterminedOpenAiCompatible": "프로바이더 '{{provider}}'의 모델 '{{modelId}}'에 대한 벡터 차원을 결정할 수 없습니다. OpenAI 호환 프로바이더 설정에서 '임베딩 차원'이 올바르게 설정되어 있는지 확인하세요.", "vectorDimensionNotDetermined": "프로바이더 '{{provider}}'의 모델 '{{modelId}}'에 대한 벡터 차원을 결정할 수 없습니다. 모델 프로필 또는 구성을 확인하세요.", "qdrantUrlMissing": "벡터 저장소 생성을 위한 Qdrant URL이 누락되었습니다", "codeIndexingNotConfigured": "서비스를 생성할 수 없습니다: 코드 인덱싱이 올바르게 구성되지 않았습니다"}, "orchestrator": {"indexingFailedNoBlocks": "인덱싱 실패: 코드 블록이 성공적으로 인덱싱되지 않았습니다. 이는 일반적으로 임베더 구성 문제를 나타냅니다.", "indexingFailedCritical": "인덱싱 실패: 처리할 파일을 찾았음에도 불구하고 코드 블록이 성공적으로 인덱싱되지 않았습니다. 이는 중요한 임베더 오류를 나타냅니다.", "fileWatcherStarted": "파일 감시자가 시작되었습니다.", "fileWatcherStopped": "파일 감시자가 중지되었습니다.", "failedDuringInitialScan": "초기 스캔 중 실패: {{errorMessage}}", "unknownError": "알 수 없는 오류", "indexingRequiresWorkspace": "인덱싱에는 열린 워크스페이스 폴더가 필요합니다"}}