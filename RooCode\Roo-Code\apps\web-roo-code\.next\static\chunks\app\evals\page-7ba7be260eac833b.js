(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[434],{3302:(e,t,o)=>{"use strict";o.d(t,{p:()=>i});var s=o(9893),n=o(34545);function i(){let{resolvedTheme:e,theme:t}=(0,s.D)(),[o,i]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{i(!0)},[]),o&&"light"===(e||t))?"/Roo-Code-Logo-Horiz-blk.svg":"/Roo-Code-Logo-Horiz-white.svg"}},35131:(e,t,o)=>{Promise.resolve().then(o.bind(o,87204))},87204:(e,t,o)=>{"use strict";o.d(t,{Evals:()=>_});var s=o(47093),n=o(34545);let i=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}),r=e=>null==e?"-":i.format(e),l=e=>{if(null==e)return"-";let t=Math.floor(e/1e3),o=Math.floor(t/3600),s=Math.floor(t%3600/60),n=t%60,i=[];return o>0&&i.push("".concat(o,"h")),s>0&&i.push("".concat(s,"m")),(n>0||0===i.length)&&i.push("".concat(n,"s")),i.join(" ")},a=e=>Math.round(100*e),c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return null==e?"-":e<1e3?e.toString():e<1e6?"".concat((e/1e3).toFixed(t),"K"):e<1e9?"".concat((e/1e6).toFixed(t),"M"):"".concat((e/1e9).toFixed(t),"B")};o(3302);var d=o(39860),u=o(49365);let h=e=>e?1e6*parseFloat(e):void 0,x=d.z.object({id:d.z.string(),name:d.z.string(),description:d.z.string(),created:d.z.number(),context_length:d.z.number(),pricing:d.z.object({prompt:d.z.string().optional(),completion:d.z.string().optional()}),top_provider:d.z.object({max_completion_tokens:d.z.number().nullish()}).optional(),architecture:d.z.object({input_modalities:d.z.array(d.z.string()).nullish(),output_modalities:d.z.array(d.z.string()).nullish()}).optional()}),m=async()=>{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)return console.error("Failed to fetch OpenRouter models"),{};let t=d.z.object({data:d.z.array(x)}).safeParse(await e.json());return t.success?t.data.data.filter(e=>{var t,o;return!(null===(o=e.architecture)||void 0===o?void 0:null===(t=o.output_modalities)||void 0===t?void 0:t.includes("image"))}).sort((e,t)=>e.name.localeCompare(t.name)).map(e=>{var t,o,s,n,i,r,l;return{...e,modelInfo:{maxTokens:null!==(r=null===(t=e.top_provider)||void 0===t?void 0:t.max_completion_tokens)&&void 0!==r?r:void 0,contextWindow:e.context_length,inputPrice:h(null===(o=e.pricing)||void 0===o?void 0:o.prompt),outputPrice:h(null===(s=e.pricing)||void 0===s?void 0:s.completion),description:e.description,supportsPromptCache:!1,supportsImages:null!==(l=null===(i=e.architecture)||void 0===i?void 0:null===(n=i.input_modalities)||void 0===n?void 0:n.includes("image"))&&void 0!==l&&l,supportsThinking:!1,tiers:[]}}}).reduce((e,t)=>(e[t.id]=t,e),{}):(console.error(t.error),{})},p=()=>(0,u.I)({queryKey:["getOpenRouterModels"],queryFn:m});var v=o(91554),j=o(72995),g=o(14689),f=o(88726),b=o(14734),N=o(88273),y=o(41751),M=o(72639);let k=e=>{let{tableData:t}=e,o=(0,n.useMemo)(()=>t.filter(e=>{let{cost:t}=e;return t<50}),[t]),i=(0,n.useMemo)(()=>o.reduce((e,t)=>({...e,[t.label]:t}),{}),[o]),l=(0,n.useMemo)(()=>{let e={},t=[],s=(e,t)=>{let o=Math.abs(e.cost-t.cost),s=Math.abs(e.score-t.score);if(o>8||s>10)return!1;if(e.position===t.position&&o<4&&s<5)return!0;let n="top"===e.position,i="bottom"===e.position,r="top"===t.position,l="bottom"===t.position;return(!!n&&!!r||!!i&&!!l)&&!!(o<4)&&!!(s<2.5)},n=(e,t)=>{for(let s of o){if(s.label===e.label)continue;let o=Math.abs(e.cost-s.cost),n=Math.abs(e.score-s.score);switch(t){case"top":if(o<3&&s.score>e.score&&s.score-e.score<6)return!0;break;case"bottom":if(o<3&&s.score<e.score&&e.score-s.score<6)return!0;break;case"left":if(n<3&&s.cost<e.cost&&e.cost-s.cost<4)return!0;break;case"right":if(n<3&&s.cost>e.cost&&s.cost-e.cost<4)return!0}}return!1};return[...o].sort((e,t)=>{let o=t.score-e.score;return Math.abs(o)>1?o:e.cost-t.cost}).forEach(o=>{let i="top";for(let e of["top","bottom","right","left"]){let r=!1;for(let n of t)if(s({cost:o.cost,score:o.score,position:e},{cost:n.cost,score:n.score,position:n.position})){r=!0;break}let l=n(o,e);if(!r&&!l){i=e;break}}e[o.label]=i,t.push({cost:o.cost,score:o.score,label:o.label,position:i})}),e},[o]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"pt-4 pb-8 font-mono",children:"Cost x Score"}),(0,s.jsx)(v.at,{config:i,className:"h-[500px] w-full",children:(0,s.jsxs)(j.t,{margin:{top:0,right:0,bottom:0,left:20},children:[(0,s.jsx)(g.W,{type:"number",dataKey:"cost",name:"Cost",domain:[e=>5*Math.round((e-5)/5),e=>5*Math.round((e+5)/5)],tickFormatter:e=>r(e)}),(0,s.jsx)(f.h,{type:"number",dataKey:"score",name:"Score",domain:[e=>Math.max(0,5*Math.round((e-5)/5)),e=>Math.min(100,5*Math.round((e+5)/5))],tickFormatter:e=>"".concat(e,"%")}),(0,s.jsx)(v.II,{content:e=>{let{active:t,payload:o}=e;if(!t||!o||!o.length||!o[0])return null;let{label:n,cost:i,score:l}=o[0].payload;return(0,s.jsxs)("div",{className:"bg-background border rounded-sm p-2 shadow-sm text-left",children:[(0,s.jsx)("div",{className:"border-b pb-1",children:n}),(0,s.jsxs)("div",{className:"pt-1",children:[(0,s.jsxs)("div",{children:["Score: ",(0,s.jsxs)("span",{className:"font-mono",children:[Math.round(l),"%"]})]}),(0,s.jsxs)("div",{children:["Cost: ",(0,s.jsx)("span",{className:"font-mono",children:r(i)})]})]})]})}}),(0,s.jsx)(b.Y,{component:w}),o.map((e,t)=>(0,s.jsx)(N.X,{name:e.label,data:[e],fill:S(t,o.length),children:(0,s.jsx)(y.Z,{dataKey:"label",content:t=>z(t,l[e.label]||"top")})},e.label))]})}),(0,s.jsx)("div",{className:"py-4 text-xs opacity-50",children:"(Note: Models with a cost of $50 or more are excluded from the scatter plot.)"})]})},w=e=>(0,s.jsx)(M.F,{width:e.width,height:e.height,x:e.width/2+35,y:e.height/2-15,top:0,left:0,stroke:"currentColor",opacity:.1}),z=(e,t)=>{let{x:o,y:n,value:i}=e,r=0,l=0,a="middle",c="auto";switch(t){case"top":l=-8,a="middle",c="auto";break;case"bottom":l=15,a="middle",c="hanging";break;case"left":r=-8,l=5,a="end",c="middle";break;case"right":r=15,l=5,a="start",c="middle"}return(0,s.jsx)("text",{x:o+r,y:n+l,fontSize:"11",fontWeight:"500",fill:"currentColor",opacity:"0.8",textAnchor:a,dominantBaseline:c,style:{pointerEvents:"none",maxWidth:"".concat(80,"px"),overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;return e.length<=t?e:e.substring(0,t-1)+"…"}(i)})},S=(e,t)=>"hsl(".concat(Math.round(360*e/t),", ").concat(70,"%, ").concat(50,"%)");function _(e){let{runs:t}=e,{data:o}=p(),i=(0,n.useMemo)(()=>t.map(e=>{var t,s,n,i,r,l,a,c,d,u;let h=null==o?void 0:null===(t=o[null!==(s=e.modelId)&&void 0!==s?s:""])||void 0===t?void 0:t.modelInfo;return{...e,label:e.name||e.description||e.model,cost:e.taskMetrics.cost,description:null!==(i=null!==(n=e.description)&&void 0!==n?n:null==h?void 0:h.description)&&void 0!==i?i:null,contextWindow:null!==(l=null!==(r=e.contextWindow)&&void 0!==r?r:null==h?void 0:h.contextWindow)&&void 0!==l?l:null,inputPrice:null!==(c=null!==(a=e.inputPrice)&&void 0!==a?a:null==h?void 0:h.inputPrice)&&void 0!==c?c:null,outputPrice:null!==(u=null!==(d=e.outputPrice)&&void 0!==d?d:null==h?void 0:h.outputPrice)&&void 0!==u?u:null}}),[t,o]);return(0,s.jsxs)("div",{className:"mx-auto flex max-w-screen-lg flex-col gap-8 p-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{children:["Roo Code tests each frontier model against"," ",(0,s.jsx)("a",{href:"https://github.com/RooCodeInc/Roo-Code-Evals",className:"underline",children:"a suite of hundreds of exercises"})," ","across 5 programming languages with varying difficulty. These results can help you find the right price-to-intelligence ratio for your use case."]}),(0,s.jsxs)("div",{children:["Want to see the results for a model we haven't tested yet? Ping us in"," ",(0,s.jsx)("a",{href:"https://discord.gg/roocode",className:"underline",children:"Discord"}),"."]})]}),(0,s.jsxs)(v.XI,{className:"border",children:[(0,s.jsxs)(v.A0,{children:[(0,s.jsxs)(v.Hj,{children:[(0,s.jsx)(v.nd,{colSpan:2,className:"border-r text-center",children:"Model"}),(0,s.jsx)(v.nd,{colSpan:3,className:"border-r text-center",children:"Metrics"}),(0,s.jsx)(v.nd,{colSpan:6,className:"text-center",children:"Scores"})]}),(0,s.jsxs)(v.Hj,{children:[(0,s.jsxs)(v.nd,{children:["Name",(0,s.jsx)("div",{className:"text-xs opacity-50",children:"Context Window"})]}),(0,s.jsxs)(v.nd,{className:"border-r",children:["Price",(0,s.jsx)("div",{className:"text-xs opacity-50",children:"In / Out"})]}),(0,s.jsx)(v.nd,{children:"Duration"}),(0,s.jsxs)(v.nd,{children:["Tokens",(0,s.jsx)("div",{className:"text-xs opacity-50",children:"In / Out"})]}),(0,s.jsxs)(v.nd,{className:"border-r",children:["Cost",(0,s.jsx)("div",{className:"text-xs opacity-50",children:"USD"})]}),(0,s.jsx)(v.nd,{children:(0,s.jsx)("i",{className:"devicon-go-plain text-lg",title:"Go"})}),(0,s.jsx)(v.nd,{children:(0,s.jsx)("i",{className:"devicon-java-plain text-lg",title:"Java"})}),(0,s.jsx)(v.nd,{children:(0,s.jsx)("i",{className:"devicon-javascript-plain text-lg",title:"JavaScript"})}),(0,s.jsx)(v.nd,{children:(0,s.jsx)("i",{className:"devicon-python-plain text-lg",title:"Python"})}),(0,s.jsx)(v.nd,{children:(0,s.jsx)("i",{className:"devicon-rust-original text-lg",title:"Rust"})}),(0,s.jsx)(v.nd,{children:"Total"})]})]}),(0,s.jsx)(v.BF,{className:"font-mono",children:i.map(e=>{var t,o,n,i,d,u,h,x,m,p,j;return(0,s.jsxs)(v.Hj,{children:[(0,s.jsxs)(v.nA,{title:null!==(u=e.description)&&void 0!==u?u:void 0,children:[(0,s.jsx)("div",{className:"font-sans",children:e.label}),(0,s.jsx)("div",{className:"text-xs opacity-50",children:c(e.contextWindow)})]}),(0,s.jsx)(v.nA,{className:"border-r",children:(0,s.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,s.jsx)("div",{children:r(e.inputPrice)}),(0,s.jsx)("div",{className:"opacity-25",children:"/"}),(0,s.jsx)("div",{children:r(e.outputPrice)})]})}),(0,s.jsx)(v.nA,{className:"font-mono",children:l(e.taskMetrics.duration)}),(0,s.jsx)(v.nA,{children:(0,s.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,s.jsx)("div",{children:c(e.taskMetrics.tokensIn)}),(0,s.jsx)("div",{className:"opacity-25",children:"/"}),(0,s.jsx)("div",{children:c(e.taskMetrics.tokensOut)})]})}),(0,s.jsx)(v.nA,{className:"border-r",children:r(e.taskMetrics.cost)}),(0,s.jsxs)(v.nA,{className:"text-muted-foreground",children:[a(null!==(h=null===(t=e.languageScores)||void 0===t?void 0:t.go)&&void 0!==h?h:0),"%"]}),(0,s.jsxs)(v.nA,{className:"text-muted-foreground",children:[a(null!==(x=null===(o=e.languageScores)||void 0===o?void 0:o.java)&&void 0!==x?x:0),"%"]}),(0,s.jsxs)(v.nA,{className:"text-muted-foreground",children:[a(null!==(m=null===(n=e.languageScores)||void 0===n?void 0:n.javascript)&&void 0!==m?m:0),"%"]}),(0,s.jsxs)(v.nA,{className:"text-muted-foreground",children:[a(null!==(p=null===(i=e.languageScores)||void 0===i?void 0:i.python)&&void 0!==p?p:0),"%"]}),(0,s.jsxs)(v.nA,{className:"text-muted-foreground",children:[a(null!==(j=null===(d=e.languageScores)||void 0===d?void 0:d.rust)&&void 0!==j?j:0),"%"]}),(0,s.jsxs)(v.nA,{className:"font-bold",children:[e.score,"%"]})]},e.id)})}),(0,s.jsx)(v.r6,{children:(0,s.jsx)(k,{tableData:i})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[687,860,440,488,554,335,550,358],()=>t(35131)),_N_E=e.O()}]);