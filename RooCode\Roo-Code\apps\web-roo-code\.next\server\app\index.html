<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/0341f4bb96c92710.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c8e217c742f0746a.js"/><script src="/_next/static/chunks/206c0df1-50f53d661b5e5bf5.js" async=""></script><script src="/_next/static/chunks/550-cc2f0cda2d11cae3.js" async=""></script><script src="/_next/static/chunks/main-app-332fbccecf75e190.js" async=""></script><script src="/_next/static/chunks/aba955ec-9833060e0c799abe.js" async=""></script><script src="/_next/static/chunks/e5c963a3-f3448fec3e69041a.js" async=""></script><script src="/_next/static/chunks/88e8e2ac-7a1619a6e98006b4.js" async=""></script><script src="/_next/static/chunks/2ae967de-afd0847aa04941df.js" async=""></script><script src="/_next/static/chunks/5d6f4545-30a8b6f9fd887cd0.js" async=""></script><script src="/_next/static/chunks/687-4648d3c56bec30f0.js" async=""></script><script src="/_next/static/chunks/156-01d88aff08204544.js" async=""></script><script src="/_next/static/chunks/440-dfc1b1b6bb027c9d.js" async=""></script><script src="/_next/static/chunks/954-09ef0ac74eac9fe3.js" async=""></script><script src="/_next/static/chunks/554-efe1799434544e26.js" async=""></script><script src="/_next/static/chunks/app/layout-8fb8194cc9ba0975.js" async=""></script><script src="/_next/static/chunks/959-f08c3b6982f621d2.js" async=""></script><script src="/_next/static/chunks/957-18ce6710bd363011.js" async=""></script><script src="/_next/static/chunks/app/page-43cedaf71957c51a.js" async=""></script><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=AW-17391954825" as="script"/><title>Roo Code – Your AI-Powered Dev Team in VS Code</title><meta name="description" content="Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility."/><meta name="application-name" content="Roo Code"/><meta name="keywords" content="Roo Code,AI coding agent,VS Code extension,AI pair programmer,software development,agentic coding,code refactoring,debugging"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="category" content="technology"/><link rel="canonical" href="https://roocode.com"/><meta property="og:title" content="Roo Code – Your AI-Powered Dev Team in VS Code"/><meta property="og:description" content="Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility."/><meta property="og:url" content="https://roocode.com"/><meta property="og:site_name" content="Roo Code"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="https://roocode.com/android-chrome-512x512.png"/><meta property="og:image:width" content="512"/><meta property="og:image:height" content="512"/><meta property="og:image:alt" content="Roo Code Logo"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Roo Code – Your AI-Powered Dev Team in VS Code"/><meta name="twitter:description" content="Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility."/><meta name="twitter:image" content="https://roocode.com/android-chrome-512x512.png"/><link rel="icon" href="/favicon.ico"/><link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png"/><link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png"/><link rel="apple-touch-icon" href="/apple-touch-icon.png"/><link rel="android-chrome-192x192" href="/android-chrome-192x192.png" sizes="192x192" type="image/png"/><link rel="android-chrome-512x512" href="/android-chrome-512x512.png" sizes="512x512" type="image/png"/><link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div itemScope="" itemType="https://schema.org/WebSite"><link itemProp="url" href="https://roocode.com"/><meta itemProp="name" content="Roo Code"/></div><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><script>((e,t,r,n,i,o,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?i.map(e=>o[e]||e):i;r?(l.classList.remove(...n),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),r=t,s&&u.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("class","theme","dark",null,["light","dark"],null,false,true)</script><div class="flex min-h-screen flex-col bg-background text-foreground"><header class="sticky top-0 z-50 border-b border-border bg-background/80 backdrop-blur-md"><div class="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8"><div class="flex items-center"><a class="flex items-center" href="/"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-8 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></a></div><nav class="hidden text-sm font-medium md:flex md:items-center md:space-x-3 xl:space-x-8"><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Features</button><button class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden">Testimonials</button><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/evals">Evals</a><a class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://docs.roocode.com" target="_blank" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Docs</a><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Community</a><a href="https://app.roocode.com" target="_blank" rel="noopener noreferrer" class="text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground">Cloud</a></nav><div class="hidden md:flex md:items-center md:space-x-4"><div class="flex flex-row space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button><a target="_blank" class="hidden items-center gap-1.5 text-sm font-medium text-muted-foreground hover:text-foreground md:flex" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>19.4k</span></a></div><a target="_blank" class="hidden items-center gap-1.5 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground transition-all duration-200 hover:bg-primary/80 hover:shadow-lg hover:scale-105 md:flex" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="-mr-[2px] mt-[1px] h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>Install <span class="font-black max-lg:text-xs">·</span></span><span>827.8k</span></a></div><button aria-expanded="false" class="flex items-center justify-center rounded-full p-2 transition-colors hover:bg-accent md:hidden" aria-label="Toggle mobile menu"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 20 20" aria-hidden="true" class="h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg></button></div><div class="absolute left-0 right-0 top-16 z-50 transform border-b border-border bg-background shadow-lg backdrop-blur-none transition-all duration-200 md:hidden pointer-events-none -translate-y-2 opacity-0"><nav class="flex flex-col py-2"><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Features</button><button class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Testimonials</button><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/evals">Evals</a><a class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="/enterprise">Enterprise</a><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Security</a><a href="https://docs.roocode.com" target="_blank" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Docs</a><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Community</a><a href="https://app.roocode.com" target="_blank" rel="noopener noreferrer" class="w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground">Cloud</a><hr class="mx-8 my-2 border-t border-border/50"/><div class="flex items-center justify-center gap-8 px-8 py-3"><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://github.com/RooCodeInc/Roo-Code"><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg><span>19.4k</span></a><div class="flex items-center rounded-md p-2 transition-colors hover:bg-accent"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9" disabled=""><svg stroke="currentColor" fill="none" stroke-width="0" viewBox="0 0 15 15" class="h-4 w-4" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z" fill="currentColor"></path></svg></button></div><a target="_blank" class="inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="h-5 w-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span>827.8k</span></a></div></nav></div></header><main class="flex-1"><section class="relative flex h-[calc(125vh-theme(spacing.12))] items-center overflow-hidden md:h-[calc(100svh-theme(spacing.12))] lg:h-[calc(100vh-theme(spacing.12))]"><canvas class="absolute inset-0 h-full w-full" style="z-index:0"></canvas><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="grid gap-8 md:gap-12 lg:grid-cols-2 lg:gap-16"><div class="flex flex-col justify-center space-y-6 sm:space-y-8"><div><h1 class="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl"><span class="block">Your</span><span class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent" style="opacity:0;transform:translateY(20px)">AI-Powered</span><span class="block">Dev Team, Right in Your Editor.</span></h1><p class="mt-4 max-w-md text-base text-muted-foreground sm:mt-6 sm:text-lg">Supercharge your editor with AI that<!-- --> <span class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent" style="opacity:0;transform:translateY(20px)">understands your codebase</span>, streamlines development, and helps you write, refactor, and debug with ease.</p></div><div class="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground shadow h-10 rounded-md px-8 w-full hover:bg-gray-200 dark:bg-white dark:text-black sm:w-auto"><a href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline" target="_blank" class="flex w-full items-center justify-center">Install Roo Code<svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></a></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 shadow-sm hover:text-accent-foreground h-10 rounded-md px-8 w-full sm:w-auto bg-white/20 dark:bg-white/10 backdrop-blur-sm border border-black/40 dark:border-white/30 hover:border-blue-400 hover:bg-white/30 dark:hover:bg-white/20 hover:shadow-[0_0_20px_rgba(59,130,246,0.5)] transition-all duration-300"><a href="https://roocode.com/enterprise" target="_blank" class="flex w-full items-center justify-center">For Enterprise</a></button></div></div><div class="relative mt-8 flex items-center justify-center lg:mt-0"><div class="absolute inset-0 flex items-center justify-center"><div class="h-[250px] w-[250px] rounded-full bg-blue-500/20 blur-[100px] sm:h-[300px] sm:w-[300px] md:h-[350px] md:w-[350px]"></div></div><div class="relative z-10 w-full max-w-[90vw] rounded-lg border border-border bg-background/50 p-2 shadow-2xl backdrop-blur-sm sm:max-w-[500px]"><div class="rounded-md bg-muted p-1.5 dark:bg-gray-900 sm:p-2"><div class="flex items-center justify-between border-b border-border px-2 py-1.5 sm:px-3 sm:py-2"><div class="flex items-center space-x-1.5"><div class="h-2.5 w-2.5 rounded-full bg-red-500 sm:h-3 sm:w-3"></div><div class="h-2.5 w-2.5 rounded-full bg-yellow-500 sm:h-3 sm:w-3"></div><div class="h-2.5 w-2.5 rounded-full bg-green-500 sm:h-3 sm:w-3"></div></div><div class="flex space-x-1"><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm bg-blue-500/20 text-blue-400">Code</button><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm text-gray-400 hover:bg-gray-800">Architect</button><button class="rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm text-gray-400 hover:bg-gray-800">Debug</button></div></div><div class="p-2 sm:p-4"><pre class="scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent overflow-y-hidden text-xs text-muted-foreground max-lg:h-[25vh] sm:text-sm lg:max-h-[50vh]"><code class="block whitespace-pre font-mono"></code></pre></div></div></div></div></div></div></section><div id="features"><section class="relative overflow-hidden border-t border-border py-32"><div class="absolute inset-0" style="opacity:0"><div class="absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2"><div class="absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="text-4xl font-bold tracking-tight sm:text-5xl">Powerful features for modern developers.</h2><p class="mt-6 text-lg text-muted-foreground">Everything you need to build faster and write better code.</p></div></div><div class="md:hidden"><div class="relative px-4"><div class="overflow-hidden"><div class="flex"><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot h-6 w-6 text-white" aria-hidden="true"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Your AI Dev Team in VS Code</h3><p class="leading-relaxed text-muted-foreground">Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code h-6 w-6 text-white" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Multiple Specialized Modes</h3><p class="leading-relaxed text-muted-foreground">From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain h-6 w-6 text-white" aria-hidden="true"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Deep Project-wide Context</h3><p class="leading-relaxed text-muted-foreground">Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wrench h-6 w-6 text-white" aria-hidden="true"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Open-Source and Model-Agnostic</h3><p class="leading-relaxed text-muted-foreground">Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal h-6 w-6 text-white" aria-hidden="true"><path d="M12 19h8"></path><path d="m4 17 6-6-6-6"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Guarded Command Execution</h3><p class="leading-relaxed text-muted-foreground">Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-puzzle h-6 w-6 text-white" aria-hidden="true"><path d="M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Fully Customizable</h3><p class="leading-relaxed text-muted-foreground">Create or tweak modes, define usage rules, and shape Roo Code&#x27;s behavior precisely—your code, your way.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe h-6 w-6 text-white" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Automated Browser Actions</h3><p class="leading-relaxed text-muted-foreground">Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield h-6 w-6 text-white" aria-hidden="true"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Secure by Design</h3><p class="leading-relaxed text-muted-foreground">Security-first from the ground up, Roo Code meets rigorous standards without slowing you down. Monitoring and strict policies keep your code safe at scale.</p></div></div><div class="flex min-w-0 flex-[0_0_100%] px-4"><div class="relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><div class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap h-6 w-6 text-white" aria-hidden="true"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Seamless Setup and Workflows</h3><p class="leading-relaxed text-muted-foreground">Get started in minutes—no heavy configs. Roo Code fits alongside your existing tools and dev flow, while supercharging your productivity.</p></div></div></div></div><div class="mt-6 flex items-center justify-between px-4"><div class="flex gap-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border shadow-sm hover:text-accent-foreground h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4 text-foreground/80" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg><span class="sr-only">Previous slide</span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border shadow-sm hover:text-accent-foreground h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-foreground/80" aria-hidden="true"><path d="m9 18 6-6-6-6"></path></svg><span class="sr-only">Next slide</span></button></div><div class="flex gap-2"></div></div></div></div><div class="relative mx-auto hidden max-w-[1200px] md:block" style="opacity:0"><div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8"><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot h-6 w-6 text-white" aria-hidden="true"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Your AI Dev Team in VS Code</h3><p class="leading-relaxed text-muted-foreground">Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code h-6 w-6 text-white" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Multiple Specialized Modes</h3><p class="leading-relaxed text-muted-foreground">From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain h-6 w-6 text-white" aria-hidden="true"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Deep Project-wide Context</h3><p class="leading-relaxed text-muted-foreground">Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wrench h-6 w-6 text-white" aria-hidden="true"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Open-Source and Model-Agnostic</h3><p class="leading-relaxed text-muted-foreground">Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal h-6 w-6 text-white" aria-hidden="true"><path d="M12 19h8"></path><path d="m4 17 6-6-6-6"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Guarded Command Execution</h3><p class="leading-relaxed text-muted-foreground">Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-puzzle h-6 w-6 text-white" aria-hidden="true"><path d="M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Fully Customizable</h3><p class="leading-relaxed text-muted-foreground">Create or tweak modes, define usage rules, and shape Roo Code&#x27;s behavior precisely—your code, your way.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe h-6 w-6 text-white" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Automated Browser Actions</h3><p class="leading-relaxed text-muted-foreground">Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield h-6 w-6 text-white" aria-hidden="true"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Secure by Design</h3><p class="leading-relaxed text-muted-foreground">Security-first from the ground up, Roo Code meets rigorous standards without slowing you down. Monitoring and strict policies keep your code safe at scale.</p></div></div><div class="group relative" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"></div><div class="relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40"><div class="mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20"><div class="rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap h-6 w-6 text-white" aria-hidden="true"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div></div><h3 class="mb-3 text-xl font-medium text-foreground/90">Seamless Setup and Workflows</h3><p class="leading-relaxed text-muted-foreground">Get started in minutes—no heavy configs. Roo Code fits alongside your existing tools and dev flow, while supercharging your productivity.</p></div></div></div></div></div></section></div><div id="testimonials"><section class="relative overflow-hidden border-t border-border py-32"><div class="absolute inset-0" style="opacity:0"><div class="absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2"><div class="absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="text-4xl font-bold tracking-tight sm:text-5xl">Empowering developers worldwide.</h2><p class="mt-6 text-lg text-muted-foreground">Join thousands of developers who are revolutionizing their workflow with AI-powered assistance.</p></div></div><div class="md:hidden"><div class="overflow-hidden px-4"><div class="flex"><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl dark:border-border/70 dark:bg-background/40"><svg class="absolute left-8 top-8 h-8 w-8 text-blue-500/30 dark:text-blue-400/50" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow-mobile"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg><blockquote class="mt-12"><p class="text-lg font-light italic leading-relaxed text-muted-foreground dark:text-foreground/70">&quot;<!-- -->Roo Code is an absolute game-changer! 🚀 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent dark:from-blue-400/70"></div><p class="mt-4 font-medium text-foreground/90 dark:text-foreground">Luca</p><p class="text-sm text-muted-foreground dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl dark:border-border/70 dark:bg-background/40"><svg class="absolute left-8 top-8 h-8 w-8 text-blue-500/30 dark:text-blue-400/50" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow-mobile"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg><blockquote class="mt-12"><p class="text-lg font-light italic leading-relaxed text-muted-foreground dark:text-foreground/70">&quot;<!-- -->Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they&#x27;re the fastest to support new models and implement useful functionality whenever users mention it... simply amazing.<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent dark:from-blue-400/70"></div><p class="mt-4 font-medium text-foreground/90 dark:text-foreground">Taro Woollett-Chiba</p><p class="text-sm text-muted-foreground dark:text-muted-foreground/80">AI Product Lead<!-- --> at <!-- -->Vendidit</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl dark:border-border/70 dark:bg-background/40"><svg class="absolute left-8 top-8 h-8 w-8 text-blue-500/30 dark:text-blue-400/50" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow-mobile"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg><blockquote class="mt-12"><p class="text-lg font-light italic leading-relaxed text-muted-foreground dark:text-foreground/70">&quot;<!-- -->Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development.<!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent dark:from-blue-400/70"></div><p class="mt-4 font-medium text-foreground/90 dark:text-foreground">Can Nuri</p><p class="text-sm text-muted-foreground dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div><div class="min-w-0 flex-[0_0_100%] px-4"><div class="relative rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl dark:border-border/70 dark:bg-background/40"><svg class="absolute left-8 top-8 h-8 w-8 text-blue-500/30 dark:text-blue-400/50" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow-mobile"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg><blockquote class="mt-12"><p class="text-lg font-light italic leading-relaxed text-muted-foreground dark:text-foreground/70">&quot;<!-- -->I switched from Windsurf to Roo Code in January and honestly, it&#x27;s been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. <!-- -->&quot;</p><footer class="mt-6"><div class="h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent dark:from-blue-400/70"></div><p class="mt-4 font-medium text-foreground/90 dark:text-foreground">Michael</p><p class="text-sm text-muted-foreground dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></footer></blockquote></div></div></div></div></div><div class="relative mx-auto hidden max-w-[1200px] md:block" style="opacity:0"><div class="relative grid grid-cols-1 gap-12 md:grid-cols-2"><div class="group relative md:translate-y-4" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100 dark:from-blue-400/40 dark:via-cyan-400/40 dark:to-purple-400/40"></div><div class="relative flex h-full flex-col rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-[1.02] group-hover:border-border group-hover:bg-background/40 group-hover:shadow-2xl dark:border-border/70 dark:bg-background/40 dark:group-hover:border-border dark:group-hover:bg-background/60 dark:group-hover:shadow-[0_20px_50px_rgba(59,130,246,0.15)]"><div class="flex flex-1 flex-col p-8"><div class="flex-1"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20 transition-all duration-500 group-hover:text-blue-500/30 dark:text-blue-400/40 dark:group-hover:text-blue-400/60" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg></div><p class="relative text-lg leading-relaxed text-muted-foreground transition-colors duration-300 group-hover:text-foreground/80 dark:text-foreground/70 dark:group-hover:text-foreground/90">Roo Code is an absolute game-changer! 🚀 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better</p></div><div class="relative mt-6"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent transition-all duration-500 group-hover:w-16 group-hover:from-blue-500/70 dark:from-blue-400/70 dark:group-hover:from-blue-400/90"></div><h3 class="font-medium text-foreground/90 transition-colors duration-300 dark:text-foreground">Luca</h3><p class="text-sm text-muted-foreground transition-colors duration-300 dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div><div class="group relative md:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100 dark:from-blue-400/40 dark:via-cyan-400/40 dark:to-purple-400/40"></div><div class="relative flex h-full flex-col rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-[1.02] group-hover:border-border group-hover:bg-background/40 group-hover:shadow-2xl dark:border-border/70 dark:bg-background/40 dark:group-hover:border-border dark:group-hover:bg-background/60 dark:group-hover:shadow-[0_20px_50px_rgba(59,130,246,0.15)]"><div class="flex flex-1 flex-col p-8"><div class="flex-1"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20 transition-all duration-500 group-hover:text-blue-500/30 dark:text-blue-400/40 dark:group-hover:text-blue-400/60" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg></div><p class="relative text-lg leading-relaxed text-muted-foreground transition-colors duration-300 group-hover:text-foreground/80 dark:text-foreground/70 dark:group-hover:text-foreground/90">Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they&#x27;re the fastest to support new models and implement useful functionality whenever users mention it... simply amazing.</p></div><div class="relative mt-6"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent transition-all duration-500 group-hover:w-16 group-hover:from-blue-500/70 dark:from-blue-400/70 dark:group-hover:from-blue-400/90"></div><h3 class="font-medium text-foreground/90 transition-colors duration-300 dark:text-foreground">Taro Woollett-Chiba</h3><p class="text-sm text-muted-foreground transition-colors duration-300 dark:text-muted-foreground/80">AI Product Lead<!-- --> at <!-- -->Vendidit</p></div></div></div></div><div class="group relative md:translate-y-4" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100 dark:from-blue-400/40 dark:via-cyan-400/40 dark:to-purple-400/40"></div><div class="relative flex h-full flex-col rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-[1.02] group-hover:border-border group-hover:bg-background/40 group-hover:shadow-2xl dark:border-border/70 dark:bg-background/40 dark:group-hover:border-border dark:group-hover:bg-background/60 dark:group-hover:shadow-[0_20px_50px_rgba(59,130,246,0.15)]"><div class="flex flex-1 flex-col p-8"><div class="flex-1"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20 transition-all duration-500 group-hover:text-blue-500/30 dark:text-blue-400/40 dark:group-hover:text-blue-400/60" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg></div><p class="relative text-lg leading-relaxed text-muted-foreground transition-colors duration-300 group-hover:text-foreground/80 dark:text-foreground/70 dark:group-hover:text-foreground/90">Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development.</p></div><div class="relative mt-6"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent transition-all duration-500 group-hover:w-16 group-hover:from-blue-500/70 dark:from-blue-400/70 dark:group-hover:from-blue-400/90"></div><h3 class="font-medium text-foreground/90 transition-colors duration-300 dark:text-foreground">Can Nuri</h3><p class="text-sm text-muted-foreground transition-colors duration-300 dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div><div class="group relative md:translate-y-12" style="opacity:0;transform:translateY(20px)"><div class="absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100 dark:from-blue-400/40 dark:via-cyan-400/40 dark:to-purple-400/40"></div><div class="relative flex h-full flex-col rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-[1.02] group-hover:border-border group-hover:bg-background/40 group-hover:shadow-2xl dark:border-border/70 dark:bg-background/40 dark:group-hover:border-border dark:group-hover:bg-background/60 dark:group-hover:shadow-[0_20px_50px_rgba(59,130,246,0.15)]"><div class="flex flex-1 flex-col p-8"><div class="flex-1"><div class="mb-6"><svg class="h-8 w-8 text-blue-500/20 transition-all duration-500 group-hover:text-blue-500/30 dark:text-blue-400/40 dark:group-hover:text-blue-400/60" fill="currentColor" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="3" result="coloredBlur"></feGaussianBlur><feMerge><feMergeNode in="coloredBlur"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" class="dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"></path></svg></div><p class="relative text-lg leading-relaxed text-muted-foreground transition-colors duration-300 group-hover:text-foreground/80 dark:text-foreground/70 dark:group-hover:text-foreground/90">I switched from Windsurf to Roo Code in January and honestly, it&#x27;s been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. </p></div><div class="relative mt-6"><div class="mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent transition-all duration-500 group-hover:w-16 group-hover:from-blue-500/70 dark:from-blue-400/70 dark:group-hover:from-blue-400/90"></div><h3 class="font-medium text-foreground/90 transition-colors duration-300 dark:text-foreground">Michael</h3><p class="text-sm text-muted-foreground transition-colors duration-300 dark:text-muted-foreground/80">Reviewer<!-- --> at <!-- -->VS Code Marketplace</p></div></div></div></div></div></div></div></section></div><div id="faq"><section id="faq-section" class="border-t border-border py-20"><div class="container mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto mb-24 max-w-3xl text-center"><div style="opacity:0;transform:translateY(20px)"><h2 class="text-4xl font-bold tracking-tight sm:text-5xl">Frequently Asked Questions</h2><p class="mt-6 text-lg text-muted-foreground max-w-2xl mx-auto">Everything you need to know about Roo Code and how it can transform your development workflow.</p></div></div><div class="mx-auto max-w-3xl"><div class="space-y-4"><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">What exactly is Roo Code?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Roo Code is an open-source, AI-powered coding assistant that runs in VS Code. It goes beyond simple autocompletion by reading and writing across multiple files, executing commands, and adapting to your workflow—like having a whole dev team right inside your editor.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">How does Roo Code differ from Copilot, Cursor, or Windsurf?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Roo Code is <strong>open-source and fully customizable</strong>, letting you integrate any AI model you choose (e.g, OpenAI, Anthropic, local LLMs, etc.). It&#x27;s built for <strong>multi-file edits</strong>, so it can read, refactor, and update multiple files at once for holistic code changes. Its<!-- --> <strong>agentic abilities</strong> go beyond a typical AI autocomplete, enabling it to run tests, open a browser, and handle deeper tasks. And you&#x27;re always in control: Roo Code is<!-- --> <strong>permission-based</strong>, meaning you can control and approve any file changes or command executions.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Is Roo Code really free?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Yes! Roo Code is completely free and open-source. You&#x27;ll only pay for the AI model usage if you use a paid API (like OpenAI). If you choose free or self-hosted models, there&#x27;s no cost at all.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Will my code stay private?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Yes. Because Roo Code is an extension in your local VS Code, your code never leaves your machine unless you connect to an external AI API. Even then, you control exactly what is sent to the AI model. You can use tools like .rooignore to exclude sensitive files, and you can also run Roo Code with offline/local models for full privacy.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Which AI models does Roo Code support?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Roo Code is fully model-agnostic, giving you the flexibility to work with whatever AI models you prefer. It supports OpenAI models (like GPT-4o, GPT-4, and o1), Anthropic&#x27;s Claude (including Claude 3.5 Sonnet), Google&#x27;s Gemini models, and local LLMs via APIs or specialized plugins. You can even connect any other model that follows Roo Code&#x27;s Model Context Protocol (MCP).</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Does Roo Code support my programming language?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Likely yes! Roo Code supports a wide range of languages—Python, Java, C#, JavaScript/TypeScript, Go, Rust, etc. Since it leverages the AI model&#x27;s understanding, new or lesser-known languages may also work, depending on model support.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">How do I install and get started?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Install Roo Code from the<!-- --> <a href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">VS Code Marketplace</a> <!-- -->(or GitHub). Add your AI keys (OpenAI, Anthropic, or other) in the extension settings. Open the Roo panel (the rocket icon) in VS Code, and start typing commands in plain English!<!-- --> <a href="https://docs.roocode.com/tutorial-videos" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Watch our tutorial to help you get started.</a></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can it handle large, enterprise-scale projects?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Absolutely. Roo Code uses efficient strategies (like partial-file analysis, summarization, or user-specified context) to handle large codebases. Enterprises especially appreciate the on-prem or self-hosted model option for compliance and security needs.<!-- --> <a class="text-primary underline-offset-4 hover:underline" href="/enterprise">Learn more about Roo Code for enterprise.</a></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Is it safe for enterprise use?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Yes. Roo Code was built for enterprise environments. You can self-host AI models or use your own trusted provider. All file changes and commands go through permission gating, so nothing runs without your approval. And because Roo Code is fully open-source, it&#x27;s auditable—you can review exactly how it works before deploying it.<!-- --> <a href="https://roocode.com/enterprise" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Learn more about Roo Code for enterprise.</a></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can Roo Code run commands and tests automatically?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Yes! One of Roo Code&#x27;s biggest strengths is its ability to execute commands—always optional and fully permission-based. It can run terminal commands like npm install, execute your test suites, and even open a web browser for integration testing when you approve it.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">What if I just want a casual coding &#x27;vibe&#x27;?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Roo Code shines for both serious enterprise development and casual &quot;vibe coding.&quot; You can ask it to quickly prototype ideas, refactor on the fly, or provide design suggestions—without a rigid, step-by-step process.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Can I contribute to Roo Code?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Yes, please do! Roo Code is open-source on<!-- --> <a href="https://github.com/RooCodeInc/Roo-Code" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">GitHub</a>. Submit issues, suggest features, or open a pull request. There&#x27;s also an active community on<!-- --> <a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Discord</a> <!-- -->and<!-- --> <a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Reddit</a> <!-- -->if you want to share feedback or help others.</div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border"><button class="flex w-full items-center justify-between p-6 text-left" aria-expanded="false"><h3 class="text-lg font-medium text-foreground/90">Where can I learn more or get help?</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-5 w-5 text-muted-foreground transition-transform duration-200" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0"><div class="px-6 text-muted-foreground">Check out our<!-- --> <a href="https://docs.roocode.com" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">official documentation</a> <!-- -->for both a quick-start set up and advanced guides. You can also get community support on<!-- --> <a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Discord</a> <!-- -->and<!-- --> <a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">Reddit</a>. You can also check out our<!-- --> <a href="https://www.youtube.com/@RooCodeYT" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">YouTube</a> <!-- -->tutorials and<!-- --> <a href="https://blog.roocode.com" target="_blank" rel="noopener noreferrer" class="text-primary underline-offset-4 hover:underline">blog posts</a> <!-- -->from fellow developers showcasing real-world usage.</div></div></div></div></div></div></div></section></div><section class="relative overflow-hidden border-t-2 border-border bg-gradient-to-b from-background via-background/95 to-background dark:from-background dark:via-background/98 dark:to-background py-20 sm:py-28 lg:py-36"><div class="absolute inset-0" style="opacity:0"><div class="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-cyan-500/5 to-purple-500/5 dark:from-blue-500/10 dark:via-cyan-500/10 dark:to-purple-500/10"></div><div class="relative mx-auto max-w-[1200px]"><div class="absolute left-1/2 top-1/2 h-[600px] w-[800px] -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 blur-[100px] dark:from-blue-500/30 dark:via-cyan-500/30 dark:to-purple-500/30"></div></div></div><div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"><div class="mx-auto max-w-4xl"><div class="relative rounded-3xl border border-border/50 bg-background/60 p-8 shadow-2xl backdrop-blur-xl dark:border-border/30 dark:bg-background/40 dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] sm:p-12 lg:p-16"><div class="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"></div><div class="relative text-center"><h2 class="bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl">Install Roo Code — Open &amp; Flexible</h2><p class="mt-6 text-lg text-muted-foreground">Roo Code is open-source, model-agnostic, and developer-focused. Install from the VS Code Marketplace or the CLI in minutes, then bring your own AI model.</p><div class="mt-12 flex flex-col items-center justify-center gap-6"><a target="_blank" class="group relative inline-flex w-full items-center justify-center gap-3 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 px-6 py-4 text-lg font-medium text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-cyan-700 hover:shadow-xl hover:shadow-blue-500/25 dark:from-blue-500 dark:to-cyan-500 dark:hover:from-blue-600 dark:hover:to-cyan-600 sm:w-auto sm:px-8 sm:text-xl" href="https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"><div class="absolute -inset-px rounded-xl bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-400 opacity-0 blur transition-opacity duration-500 group-hover:opacity-70"></div><div class="relative flex items-center gap-3"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" class="h-6 w-6 sm:h-7 sm:w-7" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"></path></svg><span class="flex flex-wrap items-center gap-2"><span>VSCode Marketplace</span><span class="font-black opacity-60">·</span><span class="opacity-90">827.8k<!-- --> Downloads</span></span></div></a><div class="group relative w-full max-w-xl"><div class="absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/50 via-cyan-500/50 to-purple-500/50 opacity-30 blur-sm transition-all duration-500 group-hover:opacity-60 dark:opacity-40 dark:group-hover:opacity-70"></div><div class="relative overflow-hidden rounded-xl border border-border bg-background/80 shadow-lg backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-blue-500/50 group-hover:shadow-xl group-hover:shadow-blue-500/10 dark:border-border/50 dark:bg-background/60 dark:group-hover:border-blue-400/50"><div class="border-b border-border/50 bg-muted/30 px-4 py-3 dark:bg-muted/20"><div class="text-sm font-medium text-foreground">Install via CLI</div></div><div class="overflow-x-auto bg-background/50 dark:bg-background/30"><pre class="p-4"><code class="whitespace-pre-wrap break-all text-sm font-mono text-foreground sm:break-normal sm:text-base">code --install-extension RooVeterinaryInc.roo-cline</code></pre></div></div></div></div></div></div></div></div></section></main><footer class="border-t border-border bg-background"><div class="mx-auto max-w-7xl px-6 pb-6 pt-12 md:pb-8 md:pt-16 lg:px-8"><div class="xl:grid xl:grid-cols-3 xl:gap-8"><div class="space-y-8"><div class="flex items-center"><img alt="Roo Code Logo" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-6 w-auto" style="color:transparent" src="/Roo-Code-Logo-Horiz-white.svg"/></div><p class="max-w-md text-sm leading-6 text-muted-foreground md:pr-16 lg:pr-32">Empowering developers to build better software faster with AI-powered tools and insights.</p><a href="https://roocode.com" target="_blank" rel="noopener noreferrer" class="inline-flex items-center space-x-2 group"><img alt="Made with Roo Code" loading="lazy" width="120" height="40" decoding="async" data-nimg="1" class="h-8 w-auto opacity-70 transition-opacity group-hover:opacity-100" style="color:transparent" src="/RooCode-Badge-white.svg"/></a></div><div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0"><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Product</h3><ul class="mt-6 space-y-4"><li><button class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Features</button></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/enterprise">Enterprise</a></li><li><a href="https://roocode.com/evals" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Evals</a></li><li><a href="https://trust.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Security</a></li><li><a href="https://docs.roocode.com/community" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Integrations</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/blob/main/CHANGELOG.md" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Changelog</a></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Resources</h3><ul class="mt-6 space-y-4"><li><a href="https://roocode.com/#faq" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">FAQ</a></li><li><a href="https://docs.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Docs</a></li><li><a href="https://docs.roocode.com/tutorial-videos" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Tutorials</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/issues" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Issues</a></li><li><a href="https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Feature Requests</a></li><li><a href="https://www.youtube.com/@RooCodeYT/podcasts" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Office Hours Podcast</a></li></ul></div></div><div class="md:grid md:grid-cols-2 md:gap-8"><div><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Company</h3><ul class="mt-6 space-y-4"><li><a href="mailto:<EMAIL>" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Contact</a></li><li><a href="https://careers.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Careers</a></li><li><a href="https://blog.roocode.com" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Blog</a></li><li><a href="https://roocode.com/#testimonials" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Testimonials</a></li><li><a class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" href="/terms">Terms of Service</a></li><li><div class="relative z-10"><button class="flex items-center text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground" aria-expanded="false" aria-haspopup="true"><span>Privacy <span class="max-[320px]:hidden">Policy</span></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-4 w-4 transition-transform" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></div></li></ul></div><div class="mt-10 md:mt-0"><h3 class="text-sm font-semibold uppercase leading-6 text-foreground">Connect</h3><ul class="mt-6 space-y-4"><li><a href="https://github.com/RooCodeInc/Roo-Code" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">GitHub</a></li><li><a href="https://discord.gg/roocode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Discord</a></li><li><a href="https://reddit.com/r/RooCode" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Reddit</a></li><li><a href="https://x.com/roo_code" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">X / Twitter</a></li><li><a href="https://www.linkedin.com/company/roo-code" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">LinkedIn</a></li><li><a href="https://bsky.app/profile/roocode.bsky.social" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">Bluesky</a></li><li><a href="https://www.tiktok.com/@roo.code" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">TikTok</a></li><li><a href="https://www.youtube.com/@RooCodeYT" target="_blank" rel="noopener noreferrer" class="text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground">YouTube</a></li></ul></div></div></div></div><div class="mt-16 flex border-t border-border pt-8 sm:mt-20 lg:mt-24"><p class="mx-auto text-sm leading-5 text-muted-foreground">© <!-- -->2025<!-- --> Roo Code. All rights reserved.</p></div></div></footer></div><script src="/_next/static/chunks/webpack-c8e217c742f0746a.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[80983,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"440\",\"static/chunks/440-dfc1b1b6bb027c9d.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-efe1799434544e26.js\",\"177\",\"static/chunks/app/layout-8fb8194cc9ba0975.js\"],\"\"]\n3:I[39183,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"440\",\"static/chunks/440-dfc1b1b6bb027c9d.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-efe1799434544e26.js\",\"177\",\"static/chunks/app/layout-8fb8194cc9ba0975.js\"],\"Providers\"]\n6:I[6445,[],\"OutletBoundary\"]\n9:I[6445,[],\"ViewportBoundary\"]\nb:I[6445,[],\"MetadataBoundary\"]\nd:I[38826,[],\"\"]\n:HL[\"/_next/static/css/0341f4bb96c92710.css\",\"style\"]\n0:{\"P\":null,\"b\":\"YTJ-QSGZfGhCTAM91W4zn\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/0341f4bb96c92710.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"link\",null,{\"rel\":\"stylesheet\",\"type\":\"text/css\",\"href\":\"https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css\"}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L2\",null,{\"src\":\"https://www.googletagmanager.com/gtag/js?id=AW-17391954825\",\"strategy\":\"afterInteractive\"}],"])</script><script>self.__next_f.push([1,"[\"$\",\"$L2\",null,{\"id\":\"google-analytics\",\"strategy\":\"afterInteractive\",\"children\":\"\\n\\t\\t\\t\\t\\t\\twindow.dataLayer = window.dataLayer || [];\\n\\t\\t\\t\\t\\t\\tfunction gtag(){dataLayer.push(arguments);}\\n\\t\\t\\t\\t\\t\\tgtag('js', new Date());\\n\\t\\t\\t\\t\\t\\tgtag('config', 'AW-17391954825');\\n\\t\\t\\t\\t\\t\"}],[\"$\",\"div\",null,{\"itemScope\":true,\"itemType\":\"https://schema.org/WebSite\",\"children\":[[\"$\",\"link\",null,{\"itemProp\":\"url\",\"href\":\"https://roocode.com\"}],[\"$\",\"meta\",null,{\"itemProp\":\"name\",\"content\":\"Roo Code\"}]]}],[\"$\",\"$L3\",null,{\"children\":\"$L4\"}]]}]]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L5\",\"$undefined\",null,[\"$\",\"$L6\",null,{\"children\":[\"$L7\",\"$L8\",null]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"q0mAZxt4IWvLm-rqmQ-HR\",{\"children\":[[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null]}],[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$d\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n7:null\n"])</script><script>self.__next_f.push([1,"8:null\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"title\",\"0\",{\"children\":\"Roo Code – Your AI-Powered Dev Team in VS Code\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Roo Code\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"Roo Code,AI coding agent,VS Code extension,AI pair programmer,software development,agentic coding,code refactoring,debugging\"}],[\"$\",\"meta\",\"4\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"5\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"6\",{\"name\":\"category\",\"content\":\"technology\"}],[\"$\",\"link\",\"7\",{\"rel\":\"canonical\",\"href\":\"https://roocode.com\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"Roo Code – Your AI-Powered Dev Team in VS Code\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://roocode.com\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"Roo Code\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image\",\"content\":\"https://roocode.com/android-chrome-512x512.png\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"512\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"512\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:alt\",\"content\":\"Roo Code Logo\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Roo Code – Your AI-Powered Dev Team in VS Code\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://roocode.com/android-chrome-512x512.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/favicon-16x16.png\",\"sizes\":\"16x16\",\"type\":\"image/png\"}],[\"$\",\"link\",\"24\",{\"rel\":\"icon\",\"href\":\"/favicon-32x32.png\",\"sizes\":\"32x32\",\"type\":\"image/png\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-touch-icon.png\"}],[\"$\",\"link\",\"26\",{\"rel\":\"android-chrome-192x192\",\"href\":\"/android-chrome-192x192.png\",\"sizes\":\"192x192\",\"type\":\"image/png\"}],[\"$\",\"link\",\"27\",{\"rel\":\"android-chrome-512x512\",\"href\":\"/android-chrome-512x512.png\",\"sizes\":\"512x512\",\"type\":\"image/png\"}]]\n"])</script><script>self.__next_f.push([1,"e:I[33001,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"AnimatedBackground\"]\nf:I[21925,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"AnimatedText\"]\n10:I[99697,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"CodeExample\"]\n11:I[64518,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"Features\"]\n12:I[52032,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"Testimonials\"]\n13:I[7615,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"FAQSection\"]\n14:I[70678,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"959\",\""])</script><script>self.__next_f.push([1,"static/chunks/959-f08c3b6982f621d2.js\",\"957\",\"static/chunks/957-18ce6710bd363011.js\",\"974\",\"static/chunks/app/page-43cedaf71957c51a.js\"],\"InstallSection\"]\n"])</script><script>self.__next_f.push([1,"5:[[\"$\",\"section\",null,{\"className\":\"relative flex h-[calc(125vh-theme(spacing.12))] items-center overflow-hidden md:h-[calc(100svh-theme(spacing.12))] lg:h-[calc(100vh-theme(spacing.12))]\",\"children\":[[\"$\",\"$Le\",null,{}],[\"$\",\"div\",null,{\"className\":\"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"grid gap-8 md:gap-12 lg:grid-cols-2 lg:gap-16\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-center space-y-6 sm:space-y-8\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl\",\"children\":[[\"$\",\"span\",null,{\"className\":\"block\",\"children\":\"Your\"}],[\"$\",\"$Lf\",null,{\"className\":\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\"children\":\"AI-Powered\"}],[\"$\",\"span\",null,{\"className\":\"block\",\"children\":\"Dev Team, Right in Your Editor.\"}]]}],[\"$\",\"p\",null,{\"className\":\"mt-4 max-w-md text-base text-muted-foreground sm:mt-6 sm:text-lg\",\"children\":[\"Supercharge your editor with AI that\",\" \",[\"$\",\"$Lf\",null,{\"className\":\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\"children\":\"understands your codebase\"}],\", streamlines development, and helps you write, refactor, and debug with ease.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0\",\"children\":[[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [\u0026_svg]:pointer-events-none [\u0026_svg]:size-4 [\u0026_svg]:shrink-0 bg-primary text-primary-foreground shadow h-10 rounded-md px-8 w-full hover:bg-gray-200 dark:bg-white dark:text-black sm:w-auto\",\"ref\":\"$undefined\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline\",\"target\":\"_blank\",\"className\":\"flex w-full items-center justify-center\",\"children\":[\"Install Roo Code\",[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"ml-2 h-4 w-4\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\",\"clipRule\":\"evenodd\"}]}]]}]}],[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [\u0026_svg]:pointer-events-none [\u0026_svg]:size-4 [\u0026_svg]:shrink-0 shadow-sm hover:text-accent-foreground h-10 rounded-md px-8 w-full sm:w-auto bg-white/20 dark:bg-white/10 backdrop-blur-sm border border-black/40 dark:border-white/30 hover:border-blue-400 hover:bg-white/30 dark:hover:bg-white/20 hover:shadow-[0_0_20px_rgba(59,130,246,0.5)] transition-all duration-300\",\"ref\":\"$undefined\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://roocode.com/enterprise\",\"target\":\"_blank\",\"className\":\"flex w-full items-center justify-center\",\"children\":\"For Enterprise\"}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative mt-8 flex items-center justify-center lg:mt-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 flex items-center justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"h-[250px] w-[250px] rounded-full bg-blue-500/20 blur-[100px] sm:h-[300px] sm:w-[300px] md:h-[350px] md:w-[350px]\"}]}],[\"$\",\"$L10\",null,{}]]}]]}]}]]}],[\"$\",\"div\",null,{\"id\":\"features\",\"children\":[\"$\",\"$L11\",null,{}]}],[\"$\",\"div\",null,{\"id\":\"testimonials\",\"children\":[\"$\",\"$L12\",null,{}]}],[\"$\",\"div\",null,{\"id\":\"faq\",\"children\":[\"$\",\"$L13\",null,{}]}],[\"$\",\"$L14\",null,{\"downloads\":\"827.8k\"}]]\n"])</script><script>self.__next_f.push([1,"15:I[91095,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"440\",\"static/chunks/440-dfc1b1b6bb027c9d.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-efe1799434544e26.js\",\"177\",\"static/chunks/app/layout-8fb8194cc9ba0975.js\"],\"NavBar\"]\n16:I[95823,[],\"\"]\n17:I[20531,[],\"\"]\n18:I[29685,[\"858\",\"static/chunks/aba955ec-9833060e0c799abe.js\",\"840\",\"static/chunks/e5c963a3-f3448fec3e69041a.js\",\"777\",\"static/chunks/88e8e2ac-7a1619a6e98006b4.js\",\"798\",\"static/chunks/2ae967de-afd0847aa04941df.js\",\"872\",\"static/chunks/5d6f4545-30a8b6f9fd887cd0.js\",\"687\",\"static/chunks/687-4648d3c56bec30f0.js\",\"156\",\"static/chunks/156-01d88aff08204544.js\",\"440\",\"static/chunks/440-dfc1b1b6bb027c9d.js\",\"954\",\"static/chunks/954-09ef0ac74eac9fe3.js\",\"554\",\"static/chunks/554-efe1799434544e26.js\",\"177\",\"static/chunks/app/layout-8fb8194cc9ba0975.js\"],\"Footer\"]\n4:[\"$\",\"div\",null,{\"className\":\"flex min-h-screen flex-col bg-background text-foreground\",\"children\":[[\"$\",\"$L15\",null,{\"stars\":\"19.4k\",\"downloads\":\"827.8k\"}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L16\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L17\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;b"])</script><script>self.__next_f.push([1,"ackground:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L18\",null,{}]]}]\n"])</script></body></html>