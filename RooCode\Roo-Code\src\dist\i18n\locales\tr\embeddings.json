{"unknownError": "Bilinmeyen hata", "authenticationFailed": "Gömülmeler oluşturulamadı: Kimlik doğrulama başarısız oldu. Lütfen API anahtarınızı kontrol edin.", "failedWithStatus": "{{attempts}} denemeden sonra gömülmeler oluşturulamadı: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "{{attempts}} den<PERSON>eden sonra gömülmeler o<PERSON>madı: {{errorMessage}}", "failedMaxAttempts": "{{attempts}} den<PERSON>eden sonra gömülmeler o<PERSON>", "textExceedsTokenLimit": "{{index}} dizinindeki metin maksimum jeton sınırını aşıyor ({{itemTokens}} > {{maxTokens}}). Atlanıyor.", "rateLimitRetry": "<PERSON><PERSON><PERSON> sı<PERSON><PERSON><PERSON><PERSON><PERSON>, {{delayMs}}ms içinde yeniden deneniyor (deneme {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Hata gövdesi okunamadı", "requestFailed": "Ollama API isteği {{status}} {{statusText}} durumuyla başarısız oldu: {{errorBody}}", "invalidResponseStructure": "Ollama API'den geçersiz yanıt yapısı: \"embeddings\" dizisi bulunamadı veya dizi değil.", "embeddingFailed": "<PERSON>llama gömü<PERSON>esi başarısız oldu: {{message}}", "serviceNotRunning": "Ollama hizmeti {{baseUrl}} adresinde çalışmıyor", "serviceUnavailable": "Ollama hizmeti kullanılamıyor (durum: {{status}})", "modelNotFound": "Ollama modeli bulunamadı: {{modelId}}", "modelNotEmbeddingCapable": "Ollama modeli gömme yeteneğine <PERSON>ğil: {{modelId}}", "hostNotFound": "Ollama ana bilgisayarı bulunamadı: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "{{filePath}} dosyası işlenirken bilinmeyen hata", "unknownErrorDeletingPoints": "{{filePath}} i<PERSON><PERSON> noktalar silinirken bilinmeyen hata", "failedToProcessBatchWithError": "{{maxRetries}} denemeden sonra toplu işlem başarısız oldu: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Qdrant vektör veritabanına bağlanılamadı. Qdrant'ın çalıştığından ve {{qdrantUrl}} adresinde erişilebilir olduğundan emin olun. Hata: {{errorMessage}}", "vectorDimensionMismatch": "Yeni model i<PERSON>in vektör dizini güncellenemedi. Lütfen dizini temizleyip yeniden başlatmayı deneyin. Detaylar: {{errorMessage}}"}, "validation": {"authenticationFailed": "Kimlik doğrulama başarısız oldu. Lütfen ayarlardan API anahtarınızı kontrol edin.", "connectionFailed": "Gömücü hizmetine bağlanılamadı. Lütfen bağlantı ayarlarınızı kontrol edin ve hizmetin çalıştığından emin olun.", "modelNotAvailable": "Belirtilen model mevcut değil. Lütfen model yapılandırmanızı kontrol edin.", "configurationError": "Geçersiz gömücü yapılandırması. Lütfen ayarlarınızı gözden geçirin.", "serviceUnavailable": "Gömücü hizmeti mevcut değil. Lütfen çalıştığından ve erişilebilir olduğundan emin olun.", "invalidEndpoint": "Geçersiz API uç noktası. Lütfen URL yapılandırmanızı kontrol edin.", "invalidEmbedderConfig": "Geçersiz gömücü yapılandırması. Lütfen ayarlarınızı kontrol edin.", "invalidApiKey": "Geçersiz API anahtarı. Lütfen API anahtarı yapılandırmanızı kontrol edin.", "invalidBaseUrl": "Geçersiz temel URL. Lütfen URL yapılandırmanızı kontrol edin.", "invalidModel": "Geçersiz model. Lütfen model yapılandırmanızı kontrol edin.", "invalidResponse": "Embedder hizmetinden geçersiz yanıt. Lütfen yapılandırmanızı kontrol edin.", "apiKeyRequired": "Bu gömücü için API anahtarı gereklidir", "baseUrlRequired": "Bu gömücü için temel URL gereklidir"}, "serviceFactory": {"openAiConfigMissing": "Gömücü oluşturmak için OpenAI yapılandırması eksik", "ollamaConfigMissing": "Gömücü oluşturmak için Ollama yapılandırması eksik", "openAiCompatibleConfigMissing": "Gömücü oluşturmak için OpenAI uyumlu yapılandırması eksik", "geminiConfigMissing": "Gömücü oluşturmak için Gemini yapılandırması eksik", "mistralConfigMissing": "Gömücü oluşturmak için Mistral yapılandırması eksik", "vercelAiGatewayConfigMissing": "Gömücü oluşturmak için Vercel AI Gateway yapılandırması eksik", "invalidEmbedderType": "Geçersiz gömücü türü yapılandırıldı: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "'{{provider}}' sağlayıcısı ile '{{modelId}}' modeli için vektör boyutu belirlenemedi. OpenAI uyumlu sağlayıcı ayarlarında 'Gömme Boyutu'nun doğru a<PERSON>landığından emin ol.", "vectorDimensionNotDetermined": "'{{provider}}' sağlayıcısı ile '{{modelId}}' modeli için vektör boyutu belirlenemedi. Model profillerini veya yapılandırmayı kontrol et.", "qdrantUrlMissing": "Vektör deposu oluşturmak için Qdrant URL'si eksik", "codeIndexingNotConfigured": "Hizmetler oluşturulamıyor: Kod indeksleme düzgün yapılandırılmamış"}, "orchestrator": {"indexingFailedNoBlocks": "İndeksleme başarısız: <PERSON>ç<PERSON> kod bloğu başarıyla indekslenemedi. Bu genellikle bir embedder yapılandırma sorunu olduğunu gösterir.", "indexingFailedCritical": "İndeksleme başarısız: İşlenecek dosyalar bulunmasına rağmen hiçbir kod bloğu başarıyla indekslenemedi. Bu kritik bir embedder hatası olduğunu gösterir.", "fileWatcherStarted": "<PERSON><PERSON><PERSON> başlatıldı.", "fileWatcherStopped": "<PERSON><PERSON><PERSON> du<PERSON>.", "failedDuringInitialScan": "İlk tarama sırasında başarısız: {{errorMessage}}", "unknownError": "Bilinmeyen hata", "indexingRequiresWorkspace": "İndeksleme açık bir workspace klasörü gerektirir"}}