@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\test-cli\out\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\test-cli\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\test-cli\out\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\test-cli\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules\@vscode\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\@vscode+test-cli@0.0.11\node_modules;C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@vscode\test-cli\out\bin.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@vscode\test-cli\out\bin.mjs" %*
)
