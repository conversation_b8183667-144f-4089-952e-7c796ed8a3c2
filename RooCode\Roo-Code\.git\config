[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
	hooksPath = .husky/_
[remote "origin"]
	url = https://github.com/RooCodeInc/Roo-Code
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[lfs]
	repositoryformatversion = 0
[branch "gemini-cli-fork"]
	vscode-merge-base = origin/main
[remote "upstream"]
	url = https://github.com/RooCodeInc/Roo-Code.git
	fetch = +refs/heads/*:refs/remotes/upstream/*
[branch "feature/gemini-cli-provider"]
	remote = upstream
	merge = refs/heads/main
	vscode-merge-base = upstream/main
