{"unknownError": "<PERSON><PERSON><PERSON> scon<PERSON>", "authenticationFailed": "Creazione degli embedding non riuscita: Autenticazione fallita. Controlla la tua chiave API.", "failedWithStatus": "Creazione degli embedding non riuscita dopo {{attempts}} tentativi: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Creazione degli embedding non riuscita dopo {{attempts}} tentativi: {{errorMessage}}", "failedMaxAttempts": "Creazione degli embedding non riuscita dopo {{attempts}} tentativi", "textExceedsTokenLimit": "Il testo all'indice {{index}} supera il limite massimo di token ({{itemTokens}} > {{maxTokens}}). Saltato.", "rateLimitRetry": "Limite di velocità raggiunto, nuovo tentativo tra {{delayMs}}ms (tentativo {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Impossibile leggere il corpo dell'errore", "requestFailed": "Richiesta API Ollama fallita con stato {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Struttura di risposta non valida dall'API Ollama: array \"embeddings\" non trovato o non è un array.", "embeddingFailed": "Embedding Ollama fallito: {{message}}", "serviceNotRunning": "Il servizio Ollama non è in esecuzione su {{baseUrl}}", "serviceUnavailable": "Il servizio Ollama non è disponibile (stato: {{status}})", "modelNotFound": "<PERSON><PERSON> non trovato: {{modelId}}", "modelNotEmbeddingCapable": "Il modello Ollama non è in grado di eseguire l'embedding: {{modelId}}", "hostNotFound": "Host <PERSON>llama non trovato: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Errore sconosciuto nell'elaborazione del file {{filePath}}", "unknownErrorDeletingPoints": "Errore sconosciuto nell'eliminazione dei punti per {{filePath}}", "failedToProcessBatchWithError": "Elaborazione del batch fallita dopo {{maxRetries}} tentativi: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Impossibile connettersi al database vettoriale Qdrant. Assicurati che Qdrant sia in esecuzione e accessibile su {{qdrantUrl}}. Errore: {{errorMessage}}", "vectorDimensionMismatch": "Impossibile aggiornare l'indice vettoriale per il nuovo modello. Prova a cancellare l'indice e a ricominciare. Dettagli: {{errorMessage}}"}, "validation": {"authenticationFailed": "Autenticazione fallita. Controlla la tua chiave API nelle impostazioni.", "connectionFailed": "Connessione al servizio di embedder fallita. Controlla le impostazioni di connessione e assicurati che il servizio sia in esecuzione.", "modelNotAvailable": "Il modello specificato non è disponibile. Controlla la configurazione del tuo modello.", "configurationError": "Configurazione dell'embedder non valida. Rivedi le tue impostazioni.", "serviceUnavailable": "Il servizio di embedder non è disponibile. Assicurati che sia in esecuzione e accessibile.", "invalidEndpoint": "Endpoint API non valido. Controlla la configurazione del tuo URL.", "invalidEmbedderConfig": "Configurazione dell'embedder non valida. Controlla le tue impostazioni.", "invalidApiKey": "Chiave API non valida. Controlla la configurazione della tua chiave API.", "invalidBaseUrl": "URL di base non valido. Controlla la configurazione del tuo URL.", "invalidModel": "Modello non valido. Controlla la configurazione del tuo modello.", "invalidResponse": "Risposta non valida dal servizio embedder. Controlla la tua configurazione.", "apiKeyRequired": "È richiesta una chiave API per questo embedder", "baseUrlRequired": "È richiesto un URL di base per questo embedder"}, "serviceFactory": {"openAiConfigMissing": "Configurazione OpenAI mancante per la creazione dell'embedder", "ollamaConfigMissing": "Configurazione Ollama mancante per la creazione dell'embedder", "openAiCompatibleConfigMissing": "Configurazione compatibile con OpenAI mancante per la creazione dell'embedder", "geminiConfigMissing": "Configurazione Gemini mancante per la creazione dell'embedder", "mistralConfigMissing": "Configurazione di Mistral mancante per la creazione dell'embedder", "vercelAiGatewayConfigMissing": "Configurazione di Vercel AI Gateway mancante per la creazione dell'embedder", "invalidEmbedderType": "Tipo di embedder configurato non valido: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Impossibile determinare la dimensione del vettore per il modello '{{modelId}}' con il provider '{{provider}}'. Assicurati che la 'Dimensione di embedding' sia impostata correttamente nelle impostazioni del provider compatibile con OpenAI.", "vectorDimensionNotDetermined": "Impossibile determinare la dimensione del vettore per il modello '{{modelId}}' con il provider '{{provider}}'. Controlla i profili del modello o la configurazione.", "qdrantUrlMissing": "URL Qdrant mancante per la creazione dello storage vettoriale", "codeIndexingNotConfigured": "Impossibile creare i servizi: L'indicizzazione del codice non è configurata correttamente"}, "orchestrator": {"indexingFailedNoBlocks": "Indicizzazione fallita: <PERSON><PERSON><PERSON> blocco di codice è stato indicizzato con successo. Questo di solito indica un problema di configurazione dell'embedder.", "indexingFailedCritical": "Indicizzazione fallita: <PERSON><PERSON><PERSON> blocco di codice è stato indicizzato con successo nonostante siano stati trovati file da elaborare. Questo indica un errore critico dell'embedder.", "fileWatcherStarted": "Monitoraggio file avviato.", "fileWatcherStopped": "Monitoraggio file fermato.", "failedDuringInitialScan": "Fallito durante la scansione iniziale: {{errorMessage}}", "unknownError": "<PERSON><PERSON><PERSON> scon<PERSON>", "indexingRequiresWorkspace": "L'indicizzazione richiede una cartella di workspace aperta"}}