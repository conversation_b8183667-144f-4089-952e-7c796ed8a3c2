(()=>{var e={};e.id=66,e.ids=[66],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9869:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17044:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c,metadata:()=>l});var t=s(90811),o=s(99750);let n="Terms of Service",a="Terms of Service for Roo Code Cloud. Learn about our service terms, commercial conditions, and legal framework.",i="/terms",d=o.k.ogImage,l={title:n,description:a,alternates:{canonical:`${o.k.url}${i}`},openGraph:{title:n,description:a,url:`${o.k.url}${i}`,siteName:o.k.name,images:[{url:d.url,width:d.width,height:d.height,alt:d.alt}],locale:o.k.locale,type:"article"},twitter:{card:o.k.twitterCard,title:n,description:a,images:[d.url]},keywords:[...o.k.keywords,"terms of service","legal","agreement","subscription"]};function c(){return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-12 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"prose prose-lg mx-auto max-w-4xl dark:prose-invert",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl",children:"Roo Code Cloud Terms of Service"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:(0,t.jsx)("em",{children:"(Version 1.0 – Effective June 19, 2025)"})}),(0,t.jsxs)("p",{className:"lead",children:['These Terms of Service ("',(0,t.jsx)("strong",{children:"TOS"}),'") govern access to and use of the Roo Code Cloud service (the "',(0,t.jsx)("strong",{children:"Service"}),'"). They apply to:']}),(0,t.jsxs)("ul",{className:"lead",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"(a)"})," every ",(0,t.jsx)("strong",{children:"Sales Order Form"})," or similar document mutually executed by Roo Code and the customer that references these TOS; ",(0,t.jsx)("strong",{children:"and"})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"(b)"})," any"," ",(0,t.jsx)("strong",{children:"online plan-selection, self-service sign-up, or in-app purchase flow"})," ",'through which a customer clicks an "I Agree" (or equivalent) button to accept these TOS — such flow also being an ',(0,t.jsx)("strong",{children:'"Order Form."'})]})]}),(0,t.jsxs)("p",{children:["By ",(0,t.jsx)("strong",{children:"creating an account, clicking to accept, or using the Service"}),', the person or entity doing so ("',(0,t.jsx)("strong",{children:"Customer"}),'") agrees to be bound by these TOS, even if no separate Order Form is signed.']}),(0,t.jsxs)("p",{children:['If Roo Code and Customer later execute a Master Subscription Agreement ("',(0,t.jsx)("strong",{children:"MSA"}),'"), the MSA governs; otherwise, these TOS and the applicable Order Form together form the entire agreement (the "',(0,t.jsx)("strong",{children:"Agreement"}),'").']}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"1. Agreement Framework"}),(0,t.jsxs)("ol",{children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Incorporation of Standard Terms."}),(0,t.jsx)("br",{}),"The"," ",(0,t.jsx)("a",{href:"https://commonpaper.com/standards/cloud-service-agreement/2.0/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:(0,t.jsx)("em",{children:"Common Paper Cloud Service Standard Terms v 2.0"})})," ",'(the "',(0,t.jsx)("strong",{children:"Standard Terms"}),'") are incorporated by reference. If these TOS conflict with the Standard Terms, these TOS control.']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Order of Precedence."}),(0,t.jsx)("br",{}),"(a) Order Form (b) these TOS (c) Standard Terms."]})]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"2. Key Commercial Terms"}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full border-collapse border border-border",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"bg-muted/50",children:[(0,t.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Term"}),(0,t.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Value"})]})}),(0,t.jsxs)("tbody",{children:[(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Governing Law / Forum"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"Delaware law; exclusive jurisdiction and venue in the state or federal courts located in Delaware"})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Plans & Subscription Periods"}),(0,t.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,t.jsx)("em",{children:"Free Plan:"})," month-to-month.",(0,t.jsx)("br",{}),(0,t.jsx)("em",{children:"Paid Plans:"})," Monthly ",(0,t.jsx)("strong",{children:"or"})," Annual, as selected in an Order Form or the online flow."]})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Auto-Renewal & Non-Renewal Notice"}),(0,t.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,t.jsx)("em",{children:"Free Plan:"})," renews continuously until cancelled in the dashboard.",(0,t.jsx)("br",{}),(0,t.jsx)("em",{children:"Paid Plans:"})," renew for the same period unless either party gives 30 days' written notice before the current period ends."]})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Fees & Usage"}),(0,t.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,t.jsx)("em",{children:"Free Plan:"})," Subscription Fee = $0.",(0,t.jsx)("br",{}),(0,t.jsx)("em",{children:"Paid Plans:"})," Fees stated in the Order Form or online checkout"," ",(0,t.jsx)("strong",{children:"plus"})," usage-based fees, calculated and invoiced monthly."]})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Payment Terms"}),(0,t.jsxs)("td",{className:"border border-border px-4 py-2",children:[(0,t.jsx)("em",{children:"Monthly paid plans:"})," credit-card charge on the billing date.",(0,t.jsx)("br",{}),(0,t.jsx)("em",{children:"Annual paid plans:"})," invoiced Net 30 (credit card optional)."]})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"General Liability Cap"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"The greater of (i) USD 100 and (ii) 1 \xd7 Fees paid or payable in the 12 months before the event giving rise to liability."})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Increased Cap / Unlimited Claims"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"None"})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Trial / Pilot"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"Not offered"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Beta Features"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"None – only generally available features are provided"})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Security Standard"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code maintains commercially reasonable administrative, physical, and technical safeguards"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Machine-Learning Use"}),(0,t.jsxs)("td",{className:"border border-border px-4 py-2",children:["Roo Code ",(0,t.jsx)("strong",{children:"does not"})," use Customer Content to train, fine-tune, or improve any ML or AI models"]})]}),(0,t.jsxs)("tr",{className:"bg-muted/25",children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Data Processing Addendum (DPA)"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"GDPR/CCPA-ready DPA available upon written request"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Publicity / Logo Rights"}),(0,t.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code may identify Customer (name & logo) in marketing materials unless Customer opts out in writing"})]})]})]})}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"3. Modifications to the Standard Terms"}),(0,t.jsxs)("ol",{children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Section 1.6 (Machine Learning)."}),(0,t.jsx)("br",{}),'"Provider will not use Customer Content or Usage Data to train, fine-tune, or improve any machine-learning or AI model, except with Customer\'s prior written consent."']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Section 3 (Security)."}),(0,t.jsx)("br",{}),'Replace "reasonable" with "commercially reasonable."']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Section 4 (Fees & Payment)."}),(0,t.jsx)("br",{}),"Add usage-billing language above and delete any provision allowing unilateral fee increases."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Section 5 (Term & Termination)."}),(0,t.jsx)("br",{}),"Insert auto-renewal and free-plan language above."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Sections 7 (Trials / Betas) and any SLA references."}),(0,t.jsx)("br",{}),"Deleted – Roo Code offers no trials, pilots, betas, or SLA credits under these TOS."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Section 12.12 (Publicity)."}),(0,t.jsx)("br",{}),'As reflected in the "Publicity / Logo Rights" row above.']})]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"4. Use of the Service"}),(0,t.jsx)("p",{children:"Customer may access and use the Service solely for its internal business purposes and subject to the Acceptable Use Policy in the Standard Terms."}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"5. Account Management & Termination"}),(0,t.jsxs)("ul",{children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Self-service cancellation or downgrade."}),(0,t.jsx)("br",{}),"Customer may cancel a Free Plan immediately, or cancel/downgrade a Paid Plan effective at the end of the current billing cycle, via the web dashboard."]}),(0,t.jsx)("li",{children:"Either party may otherwise terminate the Agreement as allowed under Section 5 of the Standard Terms."})]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"6. Privacy & Data"}),(0,t.jsxs)("p",{children:["Roo Code's Privacy Notice (",(0,t.jsx)("a",{href:"https://roocode.com/privacy",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"https://roocode.com/privacy"}),") explains how Roo Code collects and handles personal information. If Customer requires a DPA, email"," ",(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"}),"."]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"7. Warranty Disclaimer"}),(0,t.jsxs)("p",{children:["Except as expressly stated in the Agreement, the Service is provided"," ",(0,t.jsx)("strong",{children:'"as is,"'})," and all implied warranties are disclaimed to the maximum extent allowed by law."]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"8. Limitation of Liability"}),(0,t.jsx)("p",{children:"The caps in Section 2 apply to all claims under the Agreement, whether in contract, tort, or otherwise, except for Excluded Claims defined in the Standard Terms."}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"9. Miscellaneous"}),(0,t.jsxs)("ol",{children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Assignment."}),(0,t.jsx)("br",{}),"Customer may not assign the Agreement without Roo Code's prior written consent, except to a successor in a merger or sale of substantially all assets."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Export Compliance."}),(0,t.jsx)("br",{}),"Each party will comply with all applicable export-control laws and regulations and will not export or re-export any software or technical data without the required government licences."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Entire Agreement."}),(0,t.jsx)("br",{}),"The Agreement supersedes all prior or contemporaneous agreements for the Service."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Amendments."}),(0,t.jsx)("br",{}),"Roo Code may update these TOS by posting a revised version at the same URL and emailing or in-app notifying Customer at least 30 days before changes take effect. Continued use after the effective date constitutes acceptance."]})]}),(0,t.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"10. Contact"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Roo Code, Inc."}),(0,t.jsx)("br",{}),"98 Graceland Dr, San Rafael, CA 94901 USA",(0,t.jsx)("br",{}),"Email:"," ",(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"})]})]})})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45674:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var t=s(88253),o=s(21418),n=s(52052),a=s.n(n),i=s(75779),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let l={children:["",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17044)),"C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Off-Topic\\RooCode\\Roo-Code\\apps\\web-roo-code\\src\\app\\terms\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36295)),"C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Off-Topic\\RooCode\\Roo-Code\\apps\\web-roo-code\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,64544,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,20441,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,93670,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Off-Topic\\RooCode\\Roo-Code\\apps\\web-roo-code\\src\\app\\terms\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},57133:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[353,790,979],()=>s(45674));module.exports=t})();