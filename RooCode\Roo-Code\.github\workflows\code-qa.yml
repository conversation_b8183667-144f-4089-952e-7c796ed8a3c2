name: Code QA Roo Code

on:
    workflow_dispatch:
    push:
        branches: [main]
    pull_request:
        types: [opened, reopened, ready_for_review, synchronize]
        branches: [main]

jobs:
    check-translations:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Verify all translations are complete
              run: node scripts/find-missing-translations.js

    knip:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Run knip checks
              run: pnpm knip

    compile:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Lint
              run: pnpm lint
            - name: Check types
              run: pnpm check-types

    unit-test:
        name: platform-unit-test (${{ matrix.name }})
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                include:
                    - os: ubuntu-latest
                      name: ubuntu-latest
                    - os: windows-latest
                      name: windows-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Run unit tests
              run: pnpm test

    check-openrouter-api-key:
        runs-on: ubuntu-latest
        outputs:
            exists: ${{ steps.openrouter-api-key-check.outputs.defined }}
        steps:
            - name: Check if OpenRouter API key exists
              id: openrouter-api-key-check
              shell: bash
              run: |
                  if [ "${{ secrets.OPENROUTER_API_KEY }}" != '' ]; then
                    echo "defined=true" >> $GITHUB_OUTPUT;
                  else
                    echo "defined=false" >> $GITHUB_OUTPUT;
                  fi

    integration-test:
        runs-on: ubuntu-latest
        needs: [check-openrouter-api-key]
        if: needs.check-openrouter-api-key.outputs.exists == 'true'
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Create .env.local file
              working-directory: apps/vscode-e2e
              run: echo "OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY }}" > .env.local
            - name: Set VS Code test version
              run: echo "VSCODE_VERSION=1.101.2" >> $GITHUB_ENV
            - name: Cache VS Code test runtime
              uses: actions/cache@v4
              with:
                  path: apps/vscode-e2e/.vscode-test
                  key: ${{ runner.os }}-vscode-test-${{ env.VSCODE_VERSION }}
            - name: Pre-download VS Code test runtime with retry
              working-directory: apps/vscode-e2e
              run: |
                  for attempt in 1 2 3; do
                    echo "Download attempt $attempt of 3..."
                    node -e "
                    const { downloadAndUnzipVSCode } = require('@vscode/test-electron');
                    downloadAndUnzipVSCode({ version: process.env.VSCODE_VERSION || '1.101.2' })
                      .then(() => {
                        console.log('✅ VS Code test runtime downloaded successfully');
                        process.exit(0);
                      })
                      .catch(err => {
                        console.error('❌ Failed to download VS Code (attempt $attempt):', err);
                        process.exit(1);
                      });
                    " && break || {
                      if [ $attempt -eq 3 ]; then
                        echo "All download attempts failed"
                        exit 1
                      fi
                      echo "Retrying in 5 seconds..."
                      sleep 5
                    }
                  done
            - name: Run integration tests
              working-directory: apps/vscode-e2e
              run: xvfb-run -a pnpm test:ci
