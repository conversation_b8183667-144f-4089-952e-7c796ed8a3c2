{"unknownError": "<PERSON><PERSON>r tidak dikenal", "authenticationFailed": "Gagal membuat embeddings: <PERSON><PERSON><PERSON><PERSON><PERSON> gagal. Silakan periksa API key <PERSON>a.", "failedWithStatus": "Gagal membuat embeddings setelah {{attempts}} percobaan: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Gagal membuat embeddings setelah {{attempts}} percobaan: {{errorMessage}}", "failedMaxAttempts": "Gagal membuat embeddings setelah {{attempts}} per<PERSON>baan", "textExceedsTokenLimit": "Teks pada indeks {{index}} mele<PERSON>hi batas maksimum token ({{itemTokens}} > {{maxTokens}}). Dilewati.", "rateLimitRetry": "Batas rate tercapai, mencoba lagi dalam {{delayMs}}ms (percobaan {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Tidak dapat membaca body error", "requestFailed": "Permintaan API Ollama gagal dengan status {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Struktur respons tidak valid dari API Ollama: array \"embeddings\" tidak ditemukan atau bukan array.", "embeddingFailed": "Embedding <PERSON><PERSON><PERSON> gagal: {{message}}", "serviceNotRunning": "<PERSON><PERSON><PERSON> tidak ber<PERSON> di {{baseUrl}}", "serviceUnavailable": "<PERSON><PERSON><PERSON> tidak tersedia (status: {{status}})", "modelNotFound": "Model Ollama tidak ditemukan: {{modelId}}", "modelNotEmbeddingCapable": "Model Ollama tidak mampu melakukan embedding: {{modelId}}", "hostNotFound": "Host <PERSON>llama tidak ditemukan: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Error tidak dikenal saat memproses file {{filePath}}", "unknownErrorDeletingPoints": "Error tidak dikenal saat menghapus points untuk {{filePath}}", "failedToProcessBatchWithError": "Gagal memproses batch setelah {{maxRetries}} percobaan: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Gagal terhubung ke database vektor Qdrant. <PERSON>ikan <PERSON>nt berjalan dan dapat diakses di {{qdrantUrl}}. Error: {{errorMessage}}", "vectorDimensionMismatch": "<PERSON><PERSON> memperbarui indeks vektor untuk model baru. <PERSON>lakan coba bersihkan indeks dan mulai lagi. Detail: {{errorMessage}}"}, "validation": {"authenticationFailed": "Autentikasi gagal. <PERSON><PERSON>an periksa kunci API Anda di pengaturan.", "connectionFailed": "<PERSON>l terhubung ke layanan embedder. <PERSON><PERSON>an periksa pengaturan koneksi Anda dan pastikan layanan berjalan.", "modelNotAvailable": "Model yang ditentukan tidak tersedia. <PERSON><PERSON><PERSON> per<PERSON> k<PERSON> model <PERSON><PERSON>.", "configurationError": "Konfigurasi embedder tidak valid. Harap tinjau pengaturan Anda.", "serviceUnavailable": "<PERSON>anan embedder tidak tersedia. <PERSON>ikan layanan tersebut berjalan dan dapat diakses.", "invalidEndpoint": "Endpoint API tidak valid. Silakan periksa konfigurasi URL Anda.", "invalidEmbedderConfig": "Konfigurasi embedder tidak valid. Silakan periksa pengaturan Anda.", "invalidApiKey": "Kunci API tidak valid. Silakan periksa konfigurasi kunci API Anda.", "invalidBaseUrl": "URL dasar tidak valid. Silakan periksa konfigurasi URL Anda.", "invalidModel": "Model tidak valid. <PERSON><PERSON><PERSON> per<PERSON> k<PERSON> model <PERSON><PERSON>.", "invalidResponse": "Respons tidak valid dari layanan embedder. <PERSON>lakan periksa konfigu<PERSON>i <PERSON>.", "apiKeyRequired": "Kunci API diperlukan untuk embedder ini", "baseUrlRequired": "URL dasar diperlukan untuk embedder ini"}, "serviceFactory": {"openAiConfigMissing": "Konfigurasi OpenAI tidak ada untuk membuat embedder", "ollamaConfigMissing": "Kon<PERSON><PERSON><PERSON><PERSON> tidak ada untuk membuat embedder", "openAiCompatibleConfigMissing": "Konfigurasi yang kompatibel dengan OpenAI tidak ada untuk membuat embedder", "geminiConfigMissing": "Konfigurasi Gemini tidak ada untuk membuat embedder", "mistralConfigMissing": "Kon<PERSON><PERSON><PERSON><PERSON> hilang untuk pembuatan embedder", "vercelAiGatewayConfigMissing": "Konfigurasi Vercel AI Gateway hilang untuk pembuatan embedder", "invalidEmbedderType": "Tipe embedder yang dikonfigurasi tidak valid: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Tidak dapat menentukan dimensi vektor untuk model '{{modelId}}' dengan penyedia '{{provider}}'. Pastikan 'Dimensi Embedding' diatur dengan benar di pengaturan penyedia yang kompatibel dengan OpenAI.", "vectorDimensionNotDetermined": "Tidak dapat menentukan dimensi vektor untuk model '{{modelId}}' dengan penyedia '{{provider}}'. Periksa profil model atau konfigurasi.", "qdrantUrlMissing": "URL Qdrant tidak ada untuk membuat penyimpanan vektor", "codeIndexingNotConfigured": "Tidak dapat membuat layanan: Pengindeksan kode tidak dikonfigurasi dengan benar"}, "orchestrator": {"indexingFailedNoBlocks": "Pengindeksan gagal: <PERSON><PERSON>k ada blok kode yang ber<PERSON>il diindek<PERSON>. <PERSON>i <PERSON><PERSON>a <PERSON>n masalah konfigurasi embedder.", "indexingFailedCritical": "Pengindeksan gagal: Tidak ada blok kode yang berhasil diindeks meskipun menemukan file untuk diproses. Ini menunjukkan kegagalan kritis embedder.", "fileWatcherStarted": "Pemantau file dimulai.", "fileWatcherStopped": "Pemantau file dihentikan.", "failedDuringInitialScan": "<PERSON><PERSON> selama pem<PERSON>ian awal: {{errorMessage}}", "unknownError": "Kesalahan tidak diketahui", "indexingRequiresWorkspace": "Pengindeksan memerlukan folder workspace yang terbuka"}}