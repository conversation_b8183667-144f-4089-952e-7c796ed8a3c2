
> @roo-code/cloud@0.0.0 test C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud
> vitest run


[1m[46m RUN [49m[22m [36mv3.2.4 [39m[90mC:/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/packages/cloud[39m

[90mstdout[2m | src/__tests__/WebAuthService.spec.ts[2m > [22m[2mWebAuthService[2m > [22m[2mconstructor[2m > [22m[2mshould use console.log as default logger
[22m[39m[auth] Using WebAuthService

[90mstderr[2m | src/__tests__/WebAuthService.spec.ts[2m > [22m[2mWebAuthService[2m > [22m[2mlogin[2m > [22m[2mshould generate state and open external URL
[90mstdout[2m | src/__tests__/WebAuthService.spec.ts[2m > [22m[2mWebAuthService[2m > [22m[2mlogin[2m > [22m[2mshould generate state and open external URL
[22m[39mError loading VS Code module: Cannot find module 'vscode'
[22m[39mVS Code module loaded from dynamic import
Require stack:
- C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\src\importVscode.ts


 [32m✓[39m src/__tests__/WebAuthService.spec.ts [2m([22m[2m50 tests[22m[2m)[22m[33m 351[2mms[22m[39m
[90mstdout[2m | src/__tests__/StaticTokenAuthService.spec.ts[2m > [22m[2mStaticTokenAuthService[2m > [22m[2mconstructor[2m > [22m[2mshould use console.log as default logger
[22m[39m[auth] Using StaticTokenAuthService
[auth] Failed to parse JWT: InvalidTokenError: Invalid token specified: missing part #2
    at jwtDecode (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/jwt-decode@4.0.0/node_modules/[4mjwt-decode[24m/build/esm/index.js:42:15)
    at new StaticTokenAuthService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticTokenAuthService.ts:25:14[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\StaticTokenAuthService.spec.ts:81:30
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:155:11
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:26
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1897:20
    at new Promise (<anonymous>)
    at runWithTimeout (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1863:10)
    at runTest (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1574:12)
[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m

 [32m✓[39m src/__tests__/StaticTokenAuthService.spec.ts [2m([22m[2m29 tests[22m[2m)[22m[32m 80[2mms[22m[39m
 [32m✓[39m src/__tests__/RefreshTimer.test.ts [2m([22m[2m8 tests[22m[2m)[22m[32m 16[2mms[22m[39m
[90mstdout[2m | src/__tests__/StaticSettingsService.test.ts[2m > [22m[2mStaticSettingsService[2m > [22m[2mconstructor[2m > [22m[2mshould throw error for invalid base64
[22m[39m[StaticSettingsService] failed to parse static settings: Unexpected token '�', "�{ږ'~m��" is not valid JSON SyntaxError: Unexpected token '�', "�{ږ'~m��" is not valid JSON
    at JSON.parse (<anonymous>)
    at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:24:28[90m)[39m
    at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\StaticSettingsService.test.ts:36:17
    at Proxy.assertThrows (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:2723:5)
    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1618:25)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1088:12)
    at Proxy.overwritingMethodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1670:33)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1420:16)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1029:14)

[90mstdout[2m | src/__tests__/StaticSettingsService.test.ts[2m > [22m[2mStaticSettingsService[2m > [22m[2mconstructor[2m > [22m[2mshould throw error for invalid JSON
[22m[39m[StaticSettingsService] failed to parse static settings: Expected property name or '}' in JSON at position 2 (line 1 column 3) SyntaxError: Expected property name or '}' in JSON at position 2 (line 1 column 3)
    at JSON.parse (<anonymous>)
    at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:24:28[90m)[39m
    at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\StaticSettingsService.test.ts:41:17
    at Proxy.assertThrows (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:2723:5)
    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1618:25)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1088:12)
    at Proxy.overwritingMethodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1670:33)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1420:16)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1029:14)

[90mstdout[2m | src/__tests__/StaticSettingsService.test.ts[2m > [22m[2mStaticSettingsService[2m > [22m[2mconstructor[2m > [22m[2mshould throw error for invalid schema
[22m[39m[StaticSettingsService] failed to parse static settings: [
  {
    "code": "invalid_type",
    "expected": "number",
    "received": "undefined",
    "path": [
      "version"
    ],
    "message": "Required"
  },
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [
      "defaultSettings"
    ],
    "message": "Required"
  },
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [
      "allowList"
    ],
    "message": "Required"
  }
] ZodError: [
  {
    "code": "invalid_type",
    "expected": "number",
    "received": "undefined",
    "path": [
      "version"
    ],
    "message": "Required"
  },
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [
      "defaultSettings"
    ],
    "message": "Required"
  },
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [
      "allowList"
    ],
    "message": "Required"
  }
]
    at Object.get error [as error] (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/zod@3.25.61/node_modules/[4mzod[24m/dist/esm/v3/types.js:39:31)
    at ZodObject.parse (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/zod@3.25.61/node_modules/[4mzod[24m/dist/esm/v3/types.js:114:22)
    at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:25:38[90m)[39m
    at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\StaticSettingsService.test.ts:47:17
    at Proxy.assertThrows (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:2723:5)
    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1618:25)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1088:12)
    at Proxy.overwritingMethodWrapper (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/chai@5.2.0/node_modules/[4mchai[24m/chai.js:1670:33)
    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+expect@3.2.4/node_modules/[4m@vitest[24m/expect/dist/index.js:1420:16) {
  issues: [
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'number'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    },
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'object'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    },
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'object'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    }
  ],
  addIssue: [36m[Function (anonymous)][39m,
  addIssues: [36m[Function (anonymous)][39m,
  errors: [
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'number'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    },
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'object'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    },
    {
      code: [32m'invalid_type'[39m,
      expected: [32m'object'[39m,
      received: [32m'undefined'[39m,
      path: [36m[Array][39m,
      message: [32m'Required'[39m
    }
  ]
}

 [32m✓[39m src/__tests__/StaticSettingsService.test.ts [2m([22m[2m10 tests[22m[2m)[22m[32m 55[2mms[22m[39m
 [32m✓[39m src/__tests__/CloudSettingsService.test.ts [2m([22m[2m22 tests[22m[2m)[22m[32m 110[2mms[22m[39m
[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould use CloudSettingsService when no environment variable is set
[22m[39m[auth] Using WebAuthService

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould use CloudSettingsService when no environment variable is set
[22m[39m[auth] changeState: initializing -> logged-out

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould use StaticSettingsService when ROO_CODE_CLOUD_ORG_SETTINGS is set
[22m[39m[auth] Using WebAuthService

 [32m✓[39m src/__tests__/CloudSettingsService.parsing.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 69[2mms[22m[39m
[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould use StaticSettingsService when ROO_CODE_CLOUD_ORG_SETTINGS is set
[22m[39m[auth] changeState: initializing -> logged-out

[90mstderr[2m | src/__tests__/CloudShareService.test.ts[2m > [22m[2mCloudShareService[2m > [22m[2mshareTask[2m > [22m[2mshould share task with organization visibility and copy to clipboard
[22m[39mError loading VS Code module: Cannot find module 'vscode'
Require stack:
- C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\src\importVscode.ts

[90mstdout[2m | src/__tests__/CloudShareService.test.ts[2m > [22m[2mCloudShareService[2m > [22m[2mshareTask[2m > [22m[2mshould share task with organization visibility and copy to clipboard
[22m[39mVS Code module loaded from dynamic import

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould throw error when ROO_CODE_CLOUD_ORG_SETTINGS contains invalid data
[22m[39m[auth] Using WebAuthService

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould throw error when ROO_CODE_CLOUD_ORG_SETTINGS contains invalid data
[22m[39m[auth] changeState: initializing -> logged-out

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould throw error when ROO_CODE_CLOUD_ORG_SETTINGS contains invalid data
[22m[39m[StaticSettingsService] failed to parse static settings: Unexpected token '�', "�{ږ'~m�돝j�" is not valid JSON SyntaxError: Unexpected token '�', "�{ږ'~m�돝j�" is not valid JSON
    at JSON.parse (<anonymous>)
    at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:24:28[90m)[39m
    at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
    at CloudService.initialize [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:122:29[90m)[39m
[90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
    at Function.createInstance [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:331:3[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\CloudService.integration.test.ts:113:3
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:20
[CloudService] Failed to initialize: Error: Failed to parse static settings
    at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:32:10[90m)[39m
    at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
[90m    ... 4 lines matching cause stack trace ...[39m
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:20 {
  [cause]: SyntaxError: Unexpected token '�', "�{ږ'~m�돝j�" is not valid JSON
      at JSON.parse (<anonymous>)
      at StaticSettingsService.parseEnvironmentSettings [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:24:28[90m)[39m
      at new StaticSettingsService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticSettingsService.ts:18:24[90m)[39m
      at CloudService.initialize [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:122:29[90m)[39m
  [90m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
      at Function.createInstance [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:331:3[90m)[39m
      at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\CloudService.integration.test.ts:113:3
      at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:20
}

[90mstdout[2m | src/__tests__/CloudService.integration.test.ts[2m > [22m[2mCloudService Integration - Settings Service Selection[2m > [22m[2mshould prioritize static token auth when both environment variables are set
[22m[39m[auth] Using StaticTokenAuthService
[auth] Failed to parse JWT: InvalidTokenError: Invalid token specified: missing part #2
    at jwtDecode (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/jwt-decode@4.0.0/node_modules/[4mjwt-decode[24m/build/esm/index.js:42:15)
    at new StaticTokenAuthService [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\StaticTokenAuthService.ts:25:14[90m)[39m
    at CloudService.initialize [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:109:25[90m)[39m
    at Function.createInstance [90m(C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\CloudService.ts:331:24[90m)[39m
    at [90mC:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Off-Topic\RooCode\Roo-Code\packages\cloud\[39msrc\__tests__\CloudService.integration.test.ts:137:43
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:155:11
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:26
    at file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1897:20
    at new Promise (<anonymous>)
    at runWithTimeout (file:///C:/Users/<USER>/OneDrive%20-%20Anheuser-Busch%20InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/[4m.pnpm[24m/@vitest+runner@3.2.4/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1863:10)

 [32m✓[39m src/__tests__/CloudService.integration.test.ts [2m([22m[2m4 tests[22m[2m)[22m[32m 45[2mms[22m[39m
 [32m✓[39m src/__tests__/CloudShareService.test.ts [2m([22m[2m15 tests[22m[2m)[22m[32m 48[2mms[22m[39m
 [32m✓[39m src/__tests__/TelemetryClient.test.ts [2m([22m[2m28 tests[22m[2m)[22m[32m 137[2mms[22m[39m
 [32m✓[39m src/__tests__/CloudService.test.ts [2m([22m[2m35 tests[22m[2m)[22m[32m 173[2mms[22m[39m

[31m⎯⎯⎯⎯⎯⎯[39m[1m[41m Failed Suites 2 [49m[22m[31m⎯⎯⎯⎯⎯⎯⎯[39m

[41m[1m FAIL [22m[49m src/bridge/__tests__/ExtensionChannel.test.ts[2m [ src/bridge/__tests__/ExtensionChannel.test.ts ][22m
[41m[1m FAIL [22m[49m src/bridge/__tests__/TaskChannel.test.ts[2m [ src/bridge/__tests__/TaskChannel.test.ts ][22m
[31m[1mError[22m: Cannot find module 'vscode' imported from 'C:/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/packages/cloud/src/bridge/BaseChannel.ts'.

- If you rely on tsconfig.json's "paths" to resolve modules, please install "vite-tsconfig-paths" plugin to handle module resolution.
- Make sure you don't have relative aliases in your Vitest config. Use absolute paths instead. Read more: https://vitest.dev/guide/common-errors[39m
[36m [2m❯[22m src/bridge/BaseChannel.ts:[2m2:1[22m[39m
    [90m  1| [39m[35mimport[39m type { [33mSocket[39m } [35mfrom[39m [32m"socket.io-client"[39m
    [90m  2| [39m[35mimport[39m [33m*[39m [35mas[39m vscode [35mfrom[39m [32m"vscode"[39m
    [90m   | [39m[31m^[39m
    [90m  3| [39m
    [90m  4| [39mimport type { StaticAppProperties, GitProperties } from "@roo-code/typ…


[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/2]⎯[22m[39m

[2m Test Files [22m [1m[31m2 failed[39m[22m[2m | [22m[1m[32m10 passed[39m[22m[90m (12)[39m
[2m      Tests [22m [1m[32m204 passed[39m[22m[90m (204)[39m
[2m   Start at [22m 03:44:24
[2m   Duration [22m 9.25s[2m (transform 11.11s, setup 0ms, collect 23.02s, tests 1.09s, environment 4ms, prepare 10.51s)[22m

 ELIFECYCLE  Test failed. See above for more details.
