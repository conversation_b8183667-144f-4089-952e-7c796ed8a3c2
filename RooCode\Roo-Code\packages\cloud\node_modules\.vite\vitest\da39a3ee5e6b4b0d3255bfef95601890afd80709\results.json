{"version": "3.2.4", "results": [[":src/__tests__/WebAuthService.spec.ts", {"duration": 333.9440000000004, "failed": false}], [":src/__tests__/TelemetryClient.test.ts", {"duration": 137.79899999999998, "failed": false}], [":src/__tests__/CloudService.test.ts", {"duration": 186.10490000000027, "failed": false}], [":src/__tests__/CloudSettingsService.test.ts", {"duration": 95.62649999999985, "failed": false}], [":src/bridge/__tests__/TaskChannel.test.ts", {"duration": 0, "failed": true}], [":src/__tests__/StaticTokenAuthService.spec.ts", {"duration": 126.75120000000015, "failed": false}], [":src/__tests__/CloudShareService.test.ts", {"duration": 79.05040000000008, "failed": false}], [":src/bridge/__tests__/ExtensionChannel.test.ts", {"duration": 0, "failed": true}], [":src/__tests__/RefreshTimer.test.ts", {"duration": 19.95429999999999, "failed": false}], [":src/__tests__/CloudService.integration.test.ts", {"duration": 73.2551999999996, "failed": false}], [":src/__tests__/CloudSettingsService.parsing.test.ts", {"duration": 75.33020000000033, "failed": false}], [":src/__tests__/StaticSettingsService.test.ts", {"duration": 54.25510000000031, "failed": false}]]}