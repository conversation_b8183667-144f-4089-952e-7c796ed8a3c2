{"name": "@roo-code/evals", "description": "Roo Code evals.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "_test": "dotenvx run -f .env.test -- vitest run", "clean": "rimraf dist .turbo", "cli": "dotenvx run -f .env.development .env.local -- tsx src/cli/index.ts", "drizzle-kit": "dotenvx run -f .env.development -- tsx node_modules/drizzle-kit/bin.cjs", "drizzle-kit:test": "dotenvx run -f .env.test -- tsx node_modules/drizzle-kit/bin.cjs", "drizzle-kit:production": "dotenvx run -f .env.production -- tsx node_modules/drizzle-kit/bin.cjs", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:test:migrate": "pnpm drizzle-kit:test migrate", "db:production:migrate": "pnpm drizzle-kit:production migrate", "db:push": "pnpm drizzle-kit push", "db:test:push": "pnpm drizzle-kit:test push", "db:production:push": "pnpm drizzle-kit:production push", "db:up": "dotenvx run -f .env.development .env.local -- docker compose up -d db", "db:down": "dotenvx run -f .env.development .env.local -- docker compose down db", "redis:up": "dotenvx run -f .env.development .env.local -- docker compose up -d redis", "redis:down": "dotenvx run -f .env.development .env.local -- docker compose down redis", "services:up": "dotenvx run -f .env.development .env.local -- docker compose up -d db redis", "services:down": "dotenvx run -f .env.development .env.local -- docker compose down db redis"}, "dependencies": {"@roo-code/ipc": "workspace:^", "@roo-code/types": "workspace:^", "cmd-ts": "^0.13.0", "drizzle-orm": "^0.44.1", "execa": "^9.6.0", "node-ipc": "^12.0.0", "p-map": "^7.0.3", "p-queue": "^8.1.0", "p-wait-for": "^5.0.2", "postgres": "^3.4.7", "ps-tree": "^1.2.0", "redis": "^5.5.5", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "20.x", "@types/node-ipc": "^9.2.3", "@types/ps-tree": "^1.1.6", "drizzle-kit": "^0.31.1", "tsx": "^4.19.3", "vitest": "^3.2.3"}}