"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{7615:(e,t,r)=>{r.r(t),r.d(t,{FAQSection:()=>u});var o=r(47093),a=r(34545),n=r(44891),s=r(23558),i=r(18322),l=r(4214),d=r.n(l);let c=[{question:"What exactly is Roo Code?",answer:(0,o.jsx)(o.Fragment,{children:"Roo Code is an open-source, AI-powered coding assistant that runs in VS Code. It goes beyond simple autocompletion by reading and writing across multiple files, executing commands, and adapting to your workflow—like having a whole dev team right inside your editor."})},{question:"How does Roo Code differ from Copilot, Cursor, or Windsurf?",answer:(0,o.jsxs)(o.Fragment,{children:["Roo Code is ",(0,o.jsx)("strong",{children:"open-source and fully customizable"}),", letting you integrate any AI model you choose (e.g, OpenAI, Anthropic, local LLMs, etc.). It's built for ",(0,o.jsx)("strong",{children:"multi-file edits"}),", so it can read, refactor, and update multiple files at once for holistic code changes. Its"," ",(0,o.jsx)("strong",{children:"agentic abilities"})," go beyond a typical AI autocomplete, enabling it to run tests, open a browser, and handle deeper tasks. And you're always in control: Roo Code is"," ",(0,o.jsx)("strong",{children:"permission-based"}),", meaning you can control and approve any file changes or command executions."]})},{question:"Is Roo Code really free?",answer:(0,o.jsx)(o.Fragment,{children:"Yes! Roo Code is completely free and open-source. You'll only pay for the AI model usage if you use a paid API (like OpenAI). If you choose free or self-hosted models, there's no cost at all."})},{question:"Will my code stay private?",answer:(0,o.jsx)(o.Fragment,{children:"Yes. Because Roo Code is an extension in your local VS Code, your code never leaves your machine unless you connect to an external AI API. Even then, you control exactly what is sent to the AI model. You can use tools like .rooignore to exclude sensitive files, and you can also run Roo Code with offline/local models for full privacy."})},{question:"Which AI models does Roo Code support?",answer:(0,o.jsx)(o.Fragment,{children:"Roo Code is fully model-agnostic, giving you the flexibility to work with whatever AI models you prefer. It supports OpenAI models (like GPT-4o, GPT-4, and o1), Anthropic's Claude (including Claude 3.5 Sonnet), Google's Gemini models, and local LLMs via APIs or specialized plugins. You can even connect any other model that follows Roo Code's Model Context Protocol (MCP)."})},{question:"Does Roo Code support my programming language?",answer:(0,o.jsx)(o.Fragment,{children:"Likely yes! Roo Code supports a wide range of languages—Python, Java, C#, JavaScript/TypeScript, Go, Rust, etc. Since it leverages the AI model's understanding, new or lesser-known languages may also work, depending on model support."})},{question:"How do I install and get started?",answer:(0,o.jsxs)(o.Fragment,{children:["Install Roo Code from the"," ",(0,o.jsx)("a",{href:"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"VS Code Marketplace"})," ","(or GitHub). Add your AI keys (OpenAI, Anthropic, or other) in the extension settings. Open the Roo panel (the rocket icon) in VS Code, and start typing commands in plain English!"," ",(0,o.jsx)("a",{href:"https://docs.roocode.com/tutorial-videos",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Watch our tutorial to help you get started."})]})},{question:"Can it handle large, enterprise-scale projects?",answer:(0,o.jsxs)(o.Fragment,{children:["Absolutely. Roo Code uses efficient strategies (like partial-file analysis, summarization, or user-specified context) to handle large codebases. Enterprises especially appreciate the on-prem or self-hosted model option for compliance and security needs."," ",(0,o.jsx)(d(),{href:"/enterprise",className:"text-primary underline-offset-4 hover:underline",children:"Learn more about Roo Code for enterprise."})]})},{question:"Is it safe for enterprise use?",answer:(0,o.jsxs)(o.Fragment,{children:["Yes. Roo Code was built for enterprise environments. You can self-host AI models or use your own trusted provider. All file changes and commands go through permission gating, so nothing runs without your approval. And because Roo Code is fully open-source, it's auditable—you can review exactly how it works before deploying it."," ",(0,o.jsx)("a",{href:"https://roocode.com/enterprise",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Learn more about Roo Code for enterprise."})]})},{question:"Can Roo Code run commands and tests automatically?",answer:(0,o.jsx)(o.Fragment,{children:"Yes! One of Roo Code's biggest strengths is its ability to execute commands—always optional and fully permission-based. It can run terminal commands like npm install, execute your test suites, and even open a web browser for integration testing when you approve it."})},{question:"What if I just want a casual coding 'vibe'?",answer:(0,o.jsx)(o.Fragment,{children:'Roo Code shines for both serious enterprise development and casual "vibe coding." You can ask it to quickly prototype ideas, refactor on the fly, or provide design suggestions—without a rigid, step-by-step process.'})},{question:"Can I contribute to Roo Code?",answer:(0,o.jsxs)(o.Fragment,{children:["Yes, please do! Roo Code is open-source on"," ",(0,o.jsx)("a",{href:"https://github.com/RooCodeInc/Roo-Code",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"GitHub"}),". Submit issues, suggest features, or open a pull request. There's also an active community on"," ",(0,o.jsx)("a",{href:"https://discord.gg/roocode",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Discord"})," ","and"," ",(0,o.jsx)("a",{href:"https://reddit.com/r/RooCode",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Reddit"})," ","if you want to share feedback or help others."]})},{question:"Where can I learn more or get help?",answer:(0,o.jsxs)(o.Fragment,{children:["Check out our"," ",(0,o.jsx)("a",{href:"https://docs.roocode.com",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"official documentation"})," ","for both a quick-start set up and advanced guides. You can also get community support on"," ",(0,o.jsx)("a",{href:"https://discord.gg/roocode",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Discord"})," ","and"," ",(0,o.jsx)("a",{href:"https://reddit.com/r/RooCode",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"Reddit"}),". You can also check out our"," ",(0,o.jsx)("a",{href:"https://www.youtube.com/@RooCodeYT",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"YouTube"})," ","tutorials and"," ",(0,o.jsx)("a",{href:"https://blog.roocode.com",target:"_blank",rel:"noopener noreferrer",className:"text-primary underline-offset-4 hover:underline",children:"blog posts"})," ","from fellow developers showcasing real-world usage."]})}];function u(){let[e,t]=(0,a.useState)(null),r=r=>{t(e===r?null:r)};return(0,o.jsx)("section",{id:"faq-section",className:"border-t border-border py-20",children:(0,o.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,o.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,o.jsx)("h2",{className:"text-4xl font-bold tracking-tight sm:text-5xl",children:"Frequently Asked Questions"}),(0,o.jsx)("p",{className:"mt-6 text-lg text-muted-foreground max-w-2xl mx-auto",children:"Everything you need to know about Roo Code and how it can transform your development workflow."})]})}),(0,o.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,o.jsx)("div",{className:"space-y-4",children:c.map((t,a)=>(0,o.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.1*a,ease:[.21,.45,.27,.9]},children:(0,o.jsxs)("div",{className:"group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border",children:[(0,o.jsxs)("button",{onClick:()=>r(a),className:"flex w-full items-center justify-between p-6 text-left","aria-expanded":e===a,children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-foreground/90",children:t.question}),(0,o.jsx)(s.A,{className:(0,i.cn)("h-5 w-5 text-muted-foreground transition-transform duration-200",e===a?"rotate-180":"")})]}),(0,o.jsx)("div",{className:(0,i.cn)("overflow-hidden transition-all duration-300 ease-in-out",e===a?"max-h-96 pb-6":"max-h-0"),children:(0,o.jsx)("div",{className:"px-6 text-muted-foreground",children:t.answer})})]})},a))})})]})})}},21470:(e,t,r)=>{r.r(t),r.d(t,{WhatsNewButton:()=>g});var o=r(47093),a=r(34545),n=r(22602),s=r(44891),i=r(63774),l=r(7676),d=r(59981),c=r(75173),u=r(83932),h=r(4214),m=r.n(h);function p(e){let{icon:t,color:r,title:a,description:n}=e;return(0,o.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center gap-1 space-x-2",children:[(0,o.jsx)("div",{className:"rounded-full ".concat({blue:"bg-blue-500/20",purple:"bg-purple-500/20",green:"bg-green-500/20"}[r]," p-3 ").concat({blue:"text-blue-400",purple:"text-purple-400",green:"text-green-400"}[r]),children:(0,o.jsx)(t,{className:"h-6 w-6"})}),(0,o.jsx)("h3",{className:"text-base font-semibold sm:text-lg",children:a})]}),(0,o.jsx)("p",{className:"text-sm text-gray-400 sm:text-base",children:n})]})}let x="v3.8.0";function g(){let[e,t]=(0,a.useState)(!1),r=(0,a.useRef)(null),h=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e,t=h.current,o=r.current;if(!t||!o)return;let a=t.getContext("2d");if(!a)return;let n=()=>{let e=o.getBoundingClientRect();t.width=e.width+8,t.height=e.height+8,t.style.width="".concat(t.width,"px"),t.style.height="".concat(t.height,"px")};n(),window.addEventListener("resize",n);let s=0,i=()=>{if(!a||!t)return;a.clearRect(0,0,t.width,t.height);let r=t.width-4,o=t.height-4,n=o/2;a.beginPath(),a.moveTo(2+n,2),a.lineTo(2+r-n,2),a.arcTo(2+r,2,2+r,2+n,n),a.lineTo(2+r,2+o-n),a.arcTo(2+r,2+o,2+r-n,2+o,n),a.lineTo(2+n,2+o),a.arcTo(2,2+o,2,2+o-n,n),a.lineTo(2,2+n),a.arcTo(2,2,2+n,2,n),a.closePath(),s=(s+.016)%(2*Math.PI);let l=t.width/2,d=t.height/2,c="70, 130, 255",u=a.createConicGradient(s,l,d);u.addColorStop(0,"rgba(".concat(c,", 0)")),u.addColorStop(.2,"rgba(".concat(c,", 0.8)")),u.addColorStop(.4,"rgba(".concat(c,", 0)")),u.addColorStop(1,"rgba(".concat(c,", 0)")),a.strokeStyle=u,a.lineWidth=1.5,a.stroke(),a.shadowColor="rgba(".concat(c,", 0.6)"),a.shadowBlur=5,a.strokeStyle="rgba(".concat(c,", 0.3)"),a.lineWidth=.5,a.stroke(),e=requestAnimationFrame(i)};return i(),()=>{window.removeEventListener("resize",n),e&&cancelAnimationFrame(e)}},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"relative inline-flex",ref:r,children:[(0,o.jsx)("canvas",{ref:h,className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",style:{pointerEvents:"none"}}),(0,o.jsxs)(m(),{href:"#",onClick:e=>{e.preventDefault(),t(!0)},className:"relative z-10 flex items-center space-x-2 rounded-full bg-black px-4 py-2 text-sm font-medium text-white transition-all hover:bg-gray-900",children:[(0,o.jsxs)("span",{children:["See what's new in ",x]}),(0,o.jsx)(i.A,{className:"h-3.5 w-3.5"})]})]}),(0,o.jsx)(n.N,{children:e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.P.div,{className:"fixed inset-0 z-40 bg-black/80 backdrop-blur-sm",initial:{opacity:0,backdropFilter:"blur(0px)"},animate:{opacity:1,backdropFilter:"blur(8px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},transition:{duration:.2}}),(0,o.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",onClick:()=>t(!1),children:(0,o.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,o.jsxs)(s.P.div,{className:"relative w-full max-w-2xl rounded-lg border border-gray-800 bg-black p-6 sm:p-8",initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.95},transition:{type:"spring",damping:20,stiffness:400,mass:.6,duration:.25},onClick:e=>{e.stopPropagation()},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold sm:text-2xl",children:["What's New in Roo Code ",x]}),(0,o.jsx)("button",{onClick:()=>t(!1),className:"flex-shrink-0 rounded-full p-1.5 text-gray-400 hover:bg-gray-800 hover:text-white",children:(0,o.jsx)(l.A,{className:"h-5 w-5"})})]}),(0,o.jsxs)("div",{className:"mt-4 space-y-4 sm:mt-6 sm:space-y-6",children:[(0,o.jsx)(p,{icon:d.A,color:"blue",title:"AI-Powered Code Generation",description:"Generate high-quality code snippets and entire components with our new AI assistant. Trained on millions of code repositories to understand your project context."}),(0,o.jsx)(p,{icon:c.A,color:"purple",title:"Real-time Collaboration",description:"Work together with your team in real-time with our new collaborative editing features. See changes as they happen and resolve conflicts automatically."}),(0,o.jsx)(p,{icon:u.A,color:"green",title:"Performance Optimizations",description:"We've completely rewritten our core engine for blazing fast performance. Experience up to 10x faster build times and smoother development workflow."})]})]})})})]})})]})}},21925:(e,t,r)=>{r.d(t,{AnimatedText:()=>n});var o=r(47093),a=r(44891);function n(e){let{children:t,className:r}=e;return(0,o.jsx)(a.P.span,{className:r,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,ease:[.2,.65,.3,.9]},children:t})}},33001:(e,t,r)=>{r.r(t),r.d(t,{AnimatedBackground:()=>n});var o=r(47093),a=r(34545);function n(){let e=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let t,r=e.current;if(!r)return;let o=r.getContext("2d");if(!o)return;let a=[{x:.2*r.width,y:.3*r.height,radius:.4*r.width,color:"rgba(0, 100, 255, 0.15)"},{x:.8*r.width,y:.7*r.height,radius:.5*r.width,color:"rgba(100, 0, 255, 0.1)"}],n=[],s=Math.min(50,Math.floor(window.innerWidth/40)),i=()=>{r.width=window.innerWidth,r.height=window.innerHeight,a=[{x:.2*r.width,y:.3*r.height,radius:.4*r.width,color:"rgba(0, 100, 255, 0.15)"},{x:.8*r.width,y:.7*r.height,radius:.5*r.width,color:"rgba(100, 0, 255, 0.1)"}],l()};function l(){if(!o)throw Error("Context is null (not initialized?)");if(!r)throw Error("Canvas is null (not initialized?)");o.clearRect(0,0,r.width,r.height),a.forEach(e=>{let t=o.createRadialGradient(e.x,e.y,0,e.x,e.y,e.radius);t.addColorStop(0,e.color),t.addColorStop(1,"rgba(0, 0, 0, 0)"),o.fillStyle=t,o.fillRect(0,0,r.width,r.height)}),o.strokeStyle="rgba(50, 50, 70, ".concat(.15,")"),o.lineWidth=.5;let e=.7*r.height,t=.5*r.width;for(let a=0;a<=r.width;a+=50){let n=a/r.width-.5;o.beginPath(),o.moveTo(a,0);let s=e-50*Math.abs(n);o.quadraticCurveTo(a+(t-a)*.3,s,t+(a-t)*.2,e),o.stroke()}for(let r=0;r<=e;r+=50){let a=50*(1+r/e*5);o.beginPath(),o.moveTo(t-a,r),o.lineTo(t+a,r),o.stroke()}n.forEach(e=>{e.update(),e.draw()}),function(){if(!o)throw Error("Context is null (not initialized?)");for(let e=0;e<n.length;e++)for(let t=e;t<n.length;t++){let r=n[e].x-n[t].x,a=n[e].y-n[t].y,s=Math.sqrt(r*r+a*a);if(s<150){let r=(1-s/150)*.5;o.strokeStyle="rgba(100, 150, 255, ".concat(r,")"),o.lineWidth=.5,o.beginPath(),o.moveTo(n[e].x,n[e].y),o.lineTo(n[t].x,n[t].y),o.stroke()}}}()}i(),window.addEventListener("resize",i);class d{update(){if(!r)throw Error("Canvas is null (not initialized?)");this.x+=this.speedX,this.y+=this.speedY,this.x>r.width?this.x=0:this.x<0&&(this.x=r.width),this.y>.7*r.height?this.y=0:this.y<0&&(this.y=.7*r.height),this.opacity+=.01*Math.sin(.001*Date.now()),this.opacity=Math.max(.1,Math.min(.7,this.opacity))}draw(){if(!o)throw Error("Context is null (not initialized?)");o.fillStyle="".concat(this.color).concat(this.opacity,")"),o.beginPath(),o.arc(this.x,this.y,this.size,0,2*Math.PI),o.fill()}constructor(){if(!r)throw Error("Canvas is null (not initialized?)");this.x=Math.random()*r.width,this.y=Math.random()*(.7*r.height),this.size=2*Math.random()+1,this.speedX=(Math.random()-.5)*.8,this.speedY=(Math.random()-.5)*.8,this.color="rgba(100, 150, 255, ",this.opacity=.5*Math.random()+.2}}for(let e=0;e<s;e++)n.push(new d);let c=.2*r.width,u=.3*r.height,h=e=>{c=e.clientX,u=e.clientY};return function e(){if(t=requestAnimationFrame(e),!r)throw Error("Canvas is null (not initialized?)");let o=c-a[0].x,n=u-a[0].y;a[0].x+=.05*o,a[0].y+=.05*n;let s=Math.sqrt(o*o+n*n);a[0].radius=Math.max(.2*r.width,Math.min(.4*r.width,.3*r.width+.1*s)),l()}(),window.addEventListener("mousemove",h),()=>{window.removeEventListener("resize",i),window.removeEventListener("mousemove",h),cancelAnimationFrame(t)}},[]),(0,o.jsx)("canvas",{ref:e,className:"absolute inset-0 h-full w-full",style:{zIndex:0}})}},48503:(e,t,r)=>{r.r(t),r.d(t,{CompanyLogos:()=>n});var o=r(47093),a=r(44891);function n(){return(0,o.jsx)("div",{className:"mt-10",children:(0,o.jsx)("div",{className:"mx-auto grid max-w-5xl grid-cols-2 gap-8 py-8 md:grid-cols-3 lg:grid-cols-6",children:[{name:"Company 1",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 2",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 3",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 4",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 5",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 6",logo:"/placeholder.svg?height=40&width=120"}].map((e,t)=>(0,o.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t,ease:"easeOut"},className:"flex items-center justify-center",children:(0,o.jsx)("img",{src:e.logo||"/placeholder.svg",alt:e.name,className:"h-10 w-auto opacity-70 grayscale transition-all duration-300 hover:opacity-100 hover:grayscale-0"})},t))})})}},52032:(e,t,r)=>{r.r(t),r.d(t,{Testimonials:()=>u,testimonials:()=>c});var o=r(47093),a=r(34545),n=r(44891),s=r(54554),i=r(75227),l=r(8087);function d(){let[e]=(0,i.A)({loop:!0},[(0,l.A)({playOnInit:!0,speed:1,stopOnInteraction:!0,stopOnMouseEnter:!0})]);return(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsx)("div",{className:"overflow-hidden px-4",ref:e,children:(0,o.jsx)("div",{className:"flex",children:c.map(e=>(0,o.jsx)("div",{className:"min-w-0 flex-[0_0_100%] px-4",children:(0,o.jsxs)("div",{className:"relative rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl dark:border-border/70 dark:bg-background/40",children:[(0,o.jsxs)("svg",{className:"absolute left-8 top-8 h-8 w-8 text-blue-500/30 dark:text-blue-400/50",fill:"currentColor",viewBox:"0 0 32 32",children:[(0,o.jsx)("defs",{children:(0,o.jsxs)("filter",{id:"glow-mobile",children:[(0,o.jsx)("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),(0,o.jsxs)("feMerge",{children:[(0,o.jsx)("feMergeNode",{in:"coloredBlur"}),(0,o.jsx)("feMergeNode",{in:"SourceGraphic"})]})]})}),(0,o.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z",className:"dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"})]}),(0,o.jsxs)("blockquote",{className:"mt-12",children:[(0,o.jsxs)("p",{className:"text-lg font-light italic leading-relaxed text-muted-foreground dark:text-foreground/70",children:['"',e.quote,'"']}),(0,o.jsxs)("footer",{className:"mt-6",children:[(0,o.jsx)("div",{className:"h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent dark:from-blue-400/70"}),(0,o.jsx)("p",{className:"mt-4 font-medium text-foreground/90 dark:text-foreground",children:e.name}),(0,o.jsxs)("p",{className:"text-sm text-muted-foreground dark:text-muted-foreground/80",children:[e.role," at ",e.company]})]})]})]})},e.id))})})})}let c=[{id:1,name:"Luca",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is an absolute game-changer! \uD83D\uDE80 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better"},{id:2,name:"Taro Woollett-Chiba",role:"AI Product Lead",company:"Vendidit",quote:"Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they're the fastest to support new models and implement useful functionality whenever users mention it... simply amazing."},{id:3,name:"Can Nuri",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development."},{id:4,name:"Michael",role:"Reviewer",company:"VS Code Marketplace",quote:"I switched from Windsurf to Roo Code in January and honestly, it's been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. "}];function u(){let e=(0,a.useRef)(null),t={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,o.jsxs)("section",{ref:e,className:"relative overflow-hidden border-t border-border py-32",children:[(0,o.jsx)(n.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,o.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,o.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,o.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,o.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,o.jsx)("h2",{className:"text-4xl font-bold tracking-tight sm:text-5xl",children:"Empowering developers worldwide."}),(0,o.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Join thousands of developers who are revolutionizing their workflow with AI-powered assistance."})]})}),(0,o.jsx)(d,{}),(0,o.jsx)(n.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,o.jsx)("div",{className:"relative grid grid-cols-1 gap-12 md:grid-cols-2",children:c.map((e,r)=>(0,o.jsxs)(n.P.div,{variants:t,className:"group relative ".concat(r%2==0?"md:translate-y-4":"md:translate-y-12"),children:[(0,o.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100 dark:from-blue-400/40 dark:via-cyan-400/40 dark:to-purple-400/40"}),(0,o.jsxs)("div",{className:"relative flex h-full flex-col rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-[1.02] group-hover:border-border group-hover:bg-background/40 group-hover:shadow-2xl dark:border-border/70 dark:bg-background/40 dark:group-hover:border-border dark:group-hover:bg-background/60 dark:group-hover:shadow-[0_20px_50px_rgba(59,130,246,0.15)]",children:[e.image&&(0,o.jsx)("div",{className:"absolute -right-3 -top-3 h-16 w-16 overflow-hidden rounded-xl border border-border/50 bg-background/50 p-1.5 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-110 dark:border-border/70 dark:bg-background/60",children:(0,o.jsx)("div",{className:"relative h-full w-full overflow-hidden rounded-lg",children:(0,o.jsx)(s.default,{src:e.image||"/placeholder_pfp.png",alt:e.name,fill:!0,className:"object-cover"})})}),(0,o.jsxs)("div",{className:"flex flex-1 flex-col p-8",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsxs)("svg",{className:"h-8 w-8 text-blue-500/20 transition-all duration-500 group-hover:text-blue-500/30 dark:text-blue-400/40 dark:group-hover:text-blue-400/60",fill:"currentColor",viewBox:"0 0 32 32",children:[(0,o.jsx)("defs",{children:(0,o.jsxs)("filter",{id:"glow",children:[(0,o.jsx)("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),(0,o.jsxs)("feMerge",{children:[(0,o.jsx)("feMergeNode",{in:"coloredBlur"}),(0,o.jsx)("feMergeNode",{in:"SourceGraphic"})]})]})}),(0,o.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z",className:"dark:filter dark:drop-shadow-[0_0_8px_rgba(96,165,250,0.4)]"})]})}),(0,o.jsx)("p",{className:"relative text-lg leading-relaxed text-muted-foreground transition-colors duration-300 group-hover:text-foreground/80 dark:text-foreground/70 dark:group-hover:text-foreground/90",children:e.quote})]}),(0,o.jsxs)("div",{className:"relative mt-6",children:[(0,o.jsx)("div",{className:"mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent transition-all duration-500 group-hover:w-16 group-hover:from-blue-500/70 dark:from-blue-400/70 dark:group-hover:from-blue-400/90"}),(0,o.jsx)("h3",{className:"font-medium text-foreground/90 transition-colors duration-300 dark:text-foreground",children:e.name}),(0,o.jsxs)("p",{className:"text-sm text-muted-foreground transition-colors duration-300 dark:text-muted-foreground/80",children:[e.role," at ",e.company]})]})]})]})]},e.id))})})]})]})}},64518:(e,t,r)=>{r.r(t),r.d(t,{Features:()=>g,features:()=>x});var o=r(47093),a=r(44891),n=r(19733),s=r(25521),i=r(76804),l=r(58245),d=r(79568),c=r(32972),u=r(2177),h=r(54945),m=r(83932),p=r(69157);let x=[{icon:(0,o.jsx)(n.A,{className:"h-6 w-6 text-white"}),title:"Your AI Dev Team in VS Code",description:"Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files."},{icon:(0,o.jsx)(s.A,{className:"h-6 w-6 text-white"}),title:"Multiple Specialized Modes",description:"From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly."},{icon:(0,o.jsx)(i.A,{className:"h-6 w-6 text-white"}),title:"Deep Project-wide Context",description:"Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors."},{icon:(0,o.jsx)(l.A,{className:"h-6 w-6 text-white"}),title:"Open-Source and Model-Agnostic",description:"Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs."},{icon:(0,o.jsx)(d.A,{className:"h-6 w-6 text-white"}),title:"Guarded Command Execution",description:"Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands."},{icon:(0,o.jsx)(c.A,{className:"h-6 w-6 text-white"}),title:"Fully Customizable",description:"Create or tweak modes, define usage rules, and shape Roo Code's behavior precisely—your code, your way."},{icon:(0,o.jsx)(u.A,{className:"h-6 w-6 text-white"}),title:"Automated Browser Actions",description:"Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more."},{icon:(0,o.jsx)(h.A,{className:"h-6 w-6 text-white"}),title:"Secure by Design",description:"Security-first from the ground up, Roo Code meets rigorous standards without slowing you down. Monitoring and strict policies keep your code safe at scale."},{icon:(0,o.jsx)(m.A,{className:"h-6 w-6 text-white"}),title:"Seamless Setup and Workflows",description:"Get started in minutes—no heavy configs. Roo Code fits alongside your existing tools and dev flow, while supercharging your productivity."}];function g(){let e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,o.jsxs)("section",{className:"relative overflow-hidden border-t border-border py-32",children:[(0,o.jsx)(a.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,o.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,o.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,o.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,o.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,o.jsx)("h2",{className:"text-4xl font-bold tracking-tight sm:text-5xl",children:"Powerful features for modern developers."}),(0,o.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Everything you need to build faster and write better code."})]})}),(0,o.jsx)(p.FeaturesMobile,{}),(0,o.jsx)(a.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,o.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8",children:x.map((t,r)=>(0,o.jsxs)(a.P.div,{variants:e,className:"group relative",children:[(0,o.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 group-hover:opacity-100 dark:from-blue-500/50 dark:via-cyan-500/50 dark:to-purple-500/50"}),(0,o.jsxs)("div",{className:"relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40",children:[(0,o.jsx)("div",{className:"mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20",children:(0,o.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:t.icon})}),(0,o.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:t.title}),(0,o.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:t.description})]})]},r))})})]})]})}},69157:(e,t,r)=>{r.r(t),r.d(t,{FeaturesMobile:()=>u});var o=r(47093),a=r(34545),n=r(75227),s=r(73093),i=r(29301),l=r(40959),d=r(23392),c=r(64518);function u(){let e=(0,s.A)({delay:5e3,stopOnInteraction:!0,stopOnMouseEnter:!0,rootNode:e=>e}),[t,r]=(0,n.A)({loop:!0,containScroll:"trimSnaps"},[e]),[u,h]=(0,a.useState)(0),[m,p]=(0,a.useState)([]),x=(0,a.useCallback)(e=>r&&r.scrollTo(e),[r]),g=(0,a.useCallback)(e=>{p(e.scrollSnapList())},[]),f=(0,a.useCallback)(e=>{h(e.selectedScrollSnap())},[]);return(0,a.useEffect)(()=>{if(r)return g(r),f(r),r.on("reInit",g),r.on("select",f),()=>{r.off("reInit",g),r.off("select",f)}},[r,g,f]),(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsxs)("div",{className:"relative px-4",children:[(0,o.jsx)("div",{className:"overflow-hidden",ref:t,children:(0,o.jsx)("div",{className:"flex",children:c.features.map((e,t)=>(0,o.jsx)("div",{className:"flex min-w-0 flex-[0_0_100%] px-4",children:(0,o.jsxs)("div",{className:"relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/50 dark:hover:border-border/80 dark:hover:bg-background/40",children:[(0,o.jsx)("div",{className:"mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/10 to-cyan-500/10 p-2.5 dark:from-blue-500/20 dark:to-cyan-500/20",children:(0,o.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:(0,o.jsx)("div",{className:"text-white",children:e.icon})})}),(0,o.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:e.title}),(0,o.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:e.description})]})},t))})}),(0,o.jsxs)("div",{className:"mt-6 flex items-center justify-between px-4",children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>null==r?void 0:r.scrollPrev(),children:[(0,o.jsx)(l.A,{className:"h-4 w-4 text-foreground/80"}),(0,o.jsx)("span",{className:"sr-only",children:"Previous slide"})]}),(0,o.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>null==r?void 0:r.scrollNext(),children:[(0,o.jsx)(d.A,{className:"h-4 w-4 text-foreground/80"}),(0,o.jsx)("span",{className:"sr-only",children:"Next slide"})]})]}),(0,o.jsx)("div",{className:"flex gap-2",children:m.map((e,t)=>(0,o.jsx)("button",{type:"button",className:"h-3 w-3 rounded-full border border-border p-0 ".concat(t===u?"bg-foreground":"bg-background"),onClick:()=>x(t),"aria-label":"Go to slide ".concat(t+1)},t))})]})]})})}},70678:(e,t,r)=>{r.r(t),r.d(t,{InstallSection:()=>l});var o=r(47093),a=r(4784),n=r(4214),s=r.n(n),i=r(44891);function l(e){let{downloads:t}=e;return(0,o.jsxs)("section",{className:"relative overflow-hidden border-t-2 border-border bg-gradient-to-b from-background via-background/95 to-background dark:from-background dark:via-background/98 dark:to-background py-20 sm:py-28 lg:py-36",children:[(0,o.jsxs)(i.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-blue-500/5 via-cyan-500/5 to-purple-500/5 dark:from-blue-500/10 dark:via-cyan-500/10 dark:to-purple-500/10"}),(0,o.jsx)("div",{className:"relative mx-auto max-w-[1200px]",children:(0,o.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[600px] w-[800px] -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 blur-[100px] dark:from-blue-500/30 dark:via-cyan-500/30 dark:to-purple-500/30"})})]}),(0,o.jsx)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsx)("div",{className:"mx-auto max-w-4xl",children:(0,o.jsxs)("div",{className:"relative rounded-3xl border border-border/50 bg-background/60 p-8 shadow-2xl backdrop-blur-xl dark:border-border/30 dark:bg-background/40 dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] sm:p-12 lg:p-16",children:[(0,o.jsx)("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"}),(0,o.jsxs)("div",{className:"relative text-center",children:[(0,o.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Install Roo Code — Open & Flexible"}),(0,o.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Roo Code is open-source, model-agnostic, and developer-focused. Install from the VS Code Marketplace or the CLI in minutes, then bring your own AI model."}),(0,o.jsxs)("div",{className:"mt-12 flex flex-col items-center justify-center gap-6",children:[(0,o.jsxs)(s(),{href:"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline",target:"_blank",className:"group relative inline-flex w-full items-center justify-center gap-3 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 px-6 py-4 text-lg font-medium text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-cyan-700 hover:shadow-xl hover:shadow-blue-500/25 dark:from-blue-500 dark:to-cyan-500 dark:hover:from-blue-600 dark:hover:to-cyan-600 sm:w-auto sm:px-8 sm:text-xl",children:[(0,o.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-400 opacity-0 blur transition-opacity duration-500 group-hover:opacity-70"}),(0,o.jsxs)("div",{className:"relative flex items-center gap-3",children:[(0,o.jsx)(a.$$9,{className:"h-6 w-6 sm:h-7 sm:w-7"}),(0,o.jsxs)("span",{className:"flex flex-wrap items-center gap-2",children:[(0,o.jsx)("span",{children:"VSCode Marketplace"}),null!==t&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:"font-black opacity-60",children:"\xb7"}),(0,o.jsxs)("span",{className:"opacity-90",children:[t," Downloads"]})]})]})]})]}),(0,o.jsxs)("div",{className:"group relative w-full max-w-xl",children:[(0,o.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/50 via-cyan-500/50 to-purple-500/50 opacity-30 blur-sm transition-all duration-500 group-hover:opacity-60 dark:opacity-40 dark:group-hover:opacity-70"}),(0,o.jsxs)("div",{className:"relative overflow-hidden rounded-xl border border-border bg-background/80 shadow-lg backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-blue-500/50 group-hover:shadow-xl group-hover:shadow-blue-500/10 dark:border-border/50 dark:bg-background/60 dark:group-hover:border-blue-400/50",children:[(0,o.jsx)("div",{className:"border-b border-border/50 bg-muted/30 px-4 py-3 dark:bg-muted/20",children:(0,o.jsx)("div",{className:"text-sm font-medium text-foreground",children:"Install via CLI"})}),(0,o.jsx)("div",{className:"overflow-x-auto bg-background/50 dark:bg-background/30",children:(0,o.jsx)("pre",{className:"p-4",children:(0,o.jsx)("code",{className:"whitespace-pre-wrap break-all text-sm font-mono text-foreground sm:break-normal sm:text-base",children:"code --install-extension RooVeterinaryInc.roo-cline"})})})]})]})]})]})]})})})]})}},99697:(e,t,r)=>{r.r(t),r.d(t,{CodeExample:()=>s});var o=r(47093),a=r(34545),n=r(44891);function s(){let[e,t]=(0,a.useState)("code"),[r,s]=(0,a.useState)(!1),[l,d]=(0,a.useState)(""),[c,u]=(0,a.useState)(0),h=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(r&&c<i[e].code.length){let t=setTimeout(()=>{d(t=>t+i[e].code[c]),u(c+1),h.current&&(h.current.scrollTop=h.current.scrollHeight)},15);return()=>clearTimeout(t)}if(c>=i[e].code.length){s(!1);let t=setTimeout(()=>{m("code"===e?"architect":"architect"===e?"debug":"code")},1e3);return()=>clearTimeout(t)}},[r,c,e]);let m=e=>{t(e),d(""),u(0),s(!0),h.current&&(h.current.scrollTop=0)};return(0,a.useEffect)(()=>{s(!0)},[]),(0,o.jsx)("div",{className:"relative z-10 w-full max-w-[90vw] rounded-lg border border-border bg-background/50 p-2 shadow-2xl backdrop-blur-sm sm:max-w-[500px]",children:(0,o.jsxs)("div",{className:"rounded-md bg-muted p-1.5 dark:bg-gray-900 sm:p-2",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between border-b border-border px-2 py-1.5 sm:px-3 sm:py-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,o.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-red-500 sm:h-3 sm:w-3"}),(0,o.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-yellow-500 sm:h-3 sm:w-3"}),(0,o.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-green-500 sm:h-3 sm:w-3"})]}),(0,o.jsxs)("div",{className:"flex space-x-1",children:[(0,o.jsx)("button",{onClick:()=>m("code"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("code"===e?"bg-blue-500/20 text-blue-400":"text-gray-400 hover:bg-gray-800"),children:"Code"}),(0,o.jsx)("button",{onClick:()=>m("architect"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("architect"===e?"bg-purple-500/20 text-purple-400":"text-gray-400 hover:bg-gray-800"),children:"Architect"}),(0,o.jsx)("button",{onClick:()=>m("debug"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("debug"===e?"bg-green-500/20 text-green-400":"text-gray-400 hover:bg-gray-800"),children:"Debug"})]})]}),(0,o.jsx)("div",{className:"p-2 sm:p-4",children:(0,o.jsx)("pre",{ref:h,className:"scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent overflow-y-hidden text-xs text-muted-foreground max-lg:h-[25vh] sm:text-sm lg:max-h-[50vh]",children:(0,o.jsxs)("code",{className:"block whitespace-pre font-mono",children:[l,r&&(0,o.jsx)(n.P.span,{animate:{opacity:[1,0]},transition:{repeat:Number.POSITIVE_INFINITY,duration:.8},className:"inline-block h-4 w-2 bg-blue-500"})]})})})]})})}let i={code:{code:'// Code Mode\nYou: Generate a React component for a user profile card\n\nRoo: Here\'s a React component for a user profile card:\n\nimport React from \'react\';\n\ninterface UserProfileProps {\n  name: string;\n  role: string;\n  avatar: string;\n  bio: string;\n  stats: {\n    projects: number;\n    followers: number;\n    following: number;\n  };\n}\n\nexport const UserProfile: React.FC<UserProfileProps> = ({\n  name,\n  role,\n  avatar,\n  bio,\n  stats\n}) => {\n  return (\n    <div className="bg-white rounded-lg shadow-md p-6 max-w-sm">\n      <div className="flex items-center space-x-4">\n        <img \n          src={avatar || "/placeholder.svg"} \n          alt={name} \n          className="w-16 h-16 rounded-full object-cover"\n        />\n        <div>\n          <h3 className="text-xl font-bold">{name}</h3>\n          <p className="text-gray-600">{role}</p>\n        </div>\n      </div>\n      <p className="mt-4 text-gray-700">{bio}</p>\n      <div className="mt-6 flex justify-between">\n        <div className="text-center">\n          <span className="block font-bold">{stats.projects}</span>\n          <span className="text-sm text-gray-500">Projects</span>\n        </div>\n        <div className="text-center">\n          <span className="block font-bold">{stats.followers}</span>\n          <span className="text-sm text-gray-500">Followers</span>\n        </div>\n        <div className="text-center">\n          <span className="block font-bold">{stats.following}</span>\n          <span className="text-sm text-gray-500">Following</span>\n        </div>\n      </div>\n    </div>\n  );\n};'},architect:{code:"// Architect Mode\n// Project: E-commerce Platform\n// Request: Design the authentication system\n\nRoo is analyzing your project...\n\n\uD83D\uDCC2 Recommended Architecture:\n\nauth/\n├─ services/\n│  ├─ authService.ts       // Core authentication logic\n│  ├─ tokenService.ts      // JWT handling and refresh\n│  └─ userService.ts       // User data operations\n├─ hooks/\n│  ├─ useAuth.ts           // React hook for auth state\n│  └─ useProtectedRoute.ts // Route protection\n├─ components/\n│  ├─ LoginForm.tsx        // Login UI\n│  ├─ SignupForm.tsx       // Registration UI\n│  ├─ PasswordReset.tsx    // Password recovery\n│  └─ TwoFactorAuth.tsx    // 2FA implementation\n└─ context/\n   └─ AuthContext.tsx      // Global auth state\n\n\uD83D\uDD10 Security Recommendations:\n- Implement PKCE flow for auth code exchange\n- Use HttpOnly cookies for refresh tokens\n- Rate limit authentication attempts\n- Add device fingerprinting for suspicious login detection\n\n⚡ Performance Considerations:\n- Prefetch user data on auth\n- Implement token refresh without UI disruption\n- Lazy load auth components\n\nWould you like me to generate any of these files?"},debug:{code:"// Debug Mode\n// Analyzing error: TypeError: Cannot read property 'map' of undefined\n\nRoo has analyzed your code and found 3 issues:\n\n\uD83D\uDC1B Issue #1: Null data reference\n  Line 42: const items = data.items.map(item => item.name);\n  \n  ✓ Root Cause: 'data' is undefined when component mounts\n  ✓ Context: API request in useEffect hasn't completed yet\n  \n  Recommended Fix:\n  const items = data?.items?.map(item => item.name) || [];\n\n\uD83D\uDC1B Issue #2: Missing dependency in useEffect\n  Line 37: useEffect(() => { fetchData() }, []);\n  \n  ✓ Root Cause: fetchData depends on 'userId' but isn't in deps array\n  ✓ Context: This causes stale data when userId changes\n  \n  Recommended Fix:\n  useEffect(() => { fetchData() }, [userId, fetchData]);\n\n\uD83D\uDC1B Issue #3: Memory leak from unfinished API call\n  Line 38: const response = await api.getItems(userId);\n  \n  ✓ Root Cause: No cleanup when component unmounts during API call\n  ✓ Context: This triggers React warning in development\n  \n  Recommended Fix:\n  Add AbortController to cancel pending requests on unmount\n\nApply these fixes automatically? [Yes/No]"}}}}]);