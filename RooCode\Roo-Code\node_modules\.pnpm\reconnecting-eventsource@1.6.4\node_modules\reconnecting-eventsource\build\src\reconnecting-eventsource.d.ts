/// <reference types="node" />
export interface ReconnectingEventSourceInit extends EventSourceInit {
    max_retry_time?: number;
    eventSourceClass?: typeof EventSource;
    lastEventId?: string;
}
export declare class EventSourceNotAvailableError extends Error {
    constructor();
}
declare type Listeners = {
    [name: string]: ((this: EventSource, event: Event) => any)[];
};
export default class ReconnectingEventSource implements EventSource {
    readonly _configuration: ReconnectingEventSourceInit | undefined;
    readonly CONNECTING = 0;
    readonly OPEN = 1;
    readonly CLOSED = 2;
    static readonly CONNECTING = 0;
    static readonly OPEN = 1;
    static readonly CLOSED = 2;
    _eventSource: EventSource | null;
    _lastEventId: string | null;
    _timer: NodeJS.Timer | null;
    _listeners: Listeners;
    _onevent_wrapped: (this: EventSource, ev: Event) => any;
    readyState: 0 | 1 | 2;
    url: string;
    withCredentials: boolean;
    readonly max_retry_time: number;
    eventSourceClass: typeof EventSource;
    constructor(url: string | URL, configuration?: ReconnectingEventSourceInit);
    dispatchEvent(event: Event): boolean;
    _start(): void;
    _onopen(event: Event): void;
    _onerror(event: Event): void;
    _onevent(event: Event): void;
    onopen(event: Event): void;
    onerror(event: Event): void;
    onmessage(event: MessageEvent): void;
    close(): void;
    addEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
    addEventListener(type: string, listener: (this: EventSource, event: MessageEvent<any>) => any, options?: boolean | AddEventListenerOptions): void;
    removeEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
    removeEventListener(type: string, listener: (this: EventSource, event: MessageEvent<any>) => any, options?: boolean | EventListenerOptions): void;
}
export {};
//# sourceMappingURL=reconnecting-eventsource.d.ts.map