#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/vitest@3.2.4_@types+debug@4_2fe315908b063f893bb1289d219d08c2/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/vitest@3.2.4_@types+debug@4_2fe315908b063f893bb1289d219d08c2/node_modules:/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/vitest@3.2.4_@types+debug@4_2fe315908b063f893bb1289d219d08c2/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/vitest@3.2.4_@types+debug@4_2fe315908b063f893bb1289d219d08c2/node_modules:/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Off-Topic/RooCode/Roo-Code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
