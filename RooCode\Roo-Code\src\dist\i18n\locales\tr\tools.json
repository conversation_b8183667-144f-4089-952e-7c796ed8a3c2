{"readFile": {"linesRange": " (satır {{start}}-{{end}})", "definitionsOnly": " (sad<PERSON><PERSON>)", "maxLines": " (maks. {{max}} satır)", "imageTooLarge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dosyası çok büyük ({{size}} MB). İzin verilen maksimum boyut {{max}} MB.", "imageWithSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({{size}} KB)"}, "toolRepetitionLimitReached": "Roo bir döngüye takılmış gibi görün<PERSON>yor, ayn<PERSON> eylemi ({{toolName}}) tekrar tekrar deniyor. Bu, mevcut stratejisinde bir sorun olduğunu gösterebilir. Görevi yeniden ifade etmeyi, daha spesifik talimatlar vermeyi veya onu farklı bir yaklaşıma yönlendirmeyi düşünün.", "codebaseSearch": {"approval": "Kod tabanında '{{query}}' aranıyor..."}, "newTask": {"errors": {"policy_restriction": "Politika kısıtlamaları nedeniyle yeni görev oluşturulamadı."}}}