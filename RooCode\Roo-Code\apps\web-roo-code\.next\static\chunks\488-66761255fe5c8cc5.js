(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[488],{3019:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(34545),i=r(67111),o=r(4828),a=r(24097),c=r(11809);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,c=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(c?1:-1)+n,f=Math.asin(u/s)/a.Kg,p=l?i:i+o*f;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),l?i-o*f:i),theta:f}},h=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,u=t.endAngle,l=(0,c.sA)(u-o)*Math.min(Math.abs(u-o),359.999),s=o+l,f=(0,a.IZ)(e,r,i,o),p=(0,a.IZ)(e,r,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(o>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var d=(0,a.IZ)(e,r,n,o),y=(0,a.IZ)(e,r,n,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(o<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(e,",").concat(r," Z");return h},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.sA)(s-l),d=p({cx:e,cy:r,radius:i,angle:l,sign:f,cornerRadius:o,cornerIsExternal:u}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:r,radius:i,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:u}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=u?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:s});var S="M ".concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var j=p({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),A=j.circleTangency,P=j.lineTangency,E=j.theta,k=p({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),M=k.circleTangency,T=k.lineTangency,_=k.theta,I=u?Math.abs(l-s):Math.abs(l-s)-E-_;if(I<0&&0===o)return"".concat(S,"L").concat(e,",").concat(r,"Z");S+="L".concat(T.x,",").concat(T.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(I>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else S+="L".concat(e,",").concat(r,"Z");return S},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,u=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(p<s||g===x)return null;var w=(0,i.A)("recharts-sector",O),S=p-s,j=(0,c.F4)(v,S,0,!0);return e=j>0&&360>Math.abs(g-x)?d({cx:a,cy:u,innerRadius:s,outerRadius:p,cornerRadius:Math.min(j,S/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:u,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",l({},(0,o.J9)(r,!0),{className:w,d:e,role:"img"}))}},4293:(t,e,r)=>{"use strict";r.d(e,{g:()=>l});var n=r(58090),i=r(60129),o=r(4828);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,c=t.legendWidth,l=t.legendContent,s=(0,o.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?u(u({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u(u({},r),e.props):{},o=n.dataKey,a=n.name,c=n.legendType;return{inactive:n.hide,dataKey:o,type:p.iconType||c||"square",color:(0,i.Ps)(e),value:a||o,payload:n}}),u(u(u({},p),n.s.getWithHeight(s,c)),{},{payload:e,item:s})}},4390:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>tj});var n=r(34545),i=r(81444),o=r.n(i),a=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function s(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function f(t){return a(t).concat(c(t))}var p=Object.hasOwn||function(t,e){return u.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function O(t,e,r){var n=y(t),i=n.length;if(y(e).length!==i)return!1;for(;i-- >0;)if(!k(t,e,r,n[i]))return!1;return!0}function w(t,e,r){var n,i,o,a=f(t),c=a.length;if(f(e).length!==c)return!1;for(;c-- >0;)if(!k(t,e,r,n=a[c])||(i=d(t,n),o=d(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function S(t,e){return h(t.valueOf(),e.valueOf())}function j(t,e){return t.source===e.source&&t.flags===e.flags}function A(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function P(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var M=Array.isArray,T="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,_=Object.assign,I=Object.prototype.toString.call.bind(Object.prototype.toString),D=C();function C(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,f,p,d,y,k,D=t.circular,C=t.createInternalComparator,N=t.createState,R=t.strict,B=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?w:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,w):x,areNumbersEqual:h,areObjectsEqual:n?w:O,arePrimitiveWrappersEqual:S,areRegExpsEqual:j,areSetsEqual:n?l(A,w):A,areTypedArraysEqual:n?w:P,areUrlsEqual:E};if(r&&(i=_({},i,r(i))),e){var o=s(i.areArraysEqual),a=s(i.areMapsEqual),c=s(i.areObjectsEqual),u=s(i.areSetsEqual);i=_({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,d=e.areSetsEqual,y=e.areTypedArraysEqual,k=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?c(t,e,l):"function"===s&&o(t,e,l);var h=t.constructor;if(h!==e.constructor)return!1;if(h===Object)return u(t,e,l);if(M(t))return r(t,e,l);if(null!=T&&T(t))return y(t,e,l);if(h===Date)return n(t,e,l);if(h===RegExp)return p(t,e,l);if(h===Map)return a(t,e,l);if(h===Set)return d(t,e,l);var v=I(t);return"[object Date]"===v?n(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?d(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,l):"[object URL]"===v?k(t,e,l):"[object Error]"===v?i(t,e,l):"[object Arguments]"===v?u(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),L=C?C(B):function(t,e,r,n,i,o,a){return B(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==D&&D,comparator:B,createState:N,equals:L,strict:void 0!==R&&R})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){F(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e,r){var n;return(n=function(t,e){if("object"!==L(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===L(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}C({strict:!0}),C({circular:!0}),C({circular:!0,strict:!0}),C({createInternalComparator:function(){return h}}),C({strict:!0,createInternalComparator:function(){return h}}),C({circular:!0,createInternalComparator:function(){return h}}),C({circular:!0,createInternalComparator:function(){return h},strict:!0});var W=function(t){return t},q=function(t,e){return Object.keys(e).reduce(function(r,n){return z(z({},r),{},F({},n,t(n,e[n])))},{})},X=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},K=function(t,e,r,n,i,o,a,c){};function Q(t,e){if(t){if("string"==typeof t)return H(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return H(t,e)}}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var $=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Y=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},G=function(t,e){return function(r){return Y($(t,e),r)}},J=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(s,4)||Q(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else K(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}K([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=G(o,c),h=G(a,u),d=(t=o,e=c,function(r){var n;return Y([].concat(function(t){if(Array.isArray(t))return H(t)}(n=$(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||Q(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},V=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},Z=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return J(n);case"spring":return V();default:if("cubic-bezier"===n.split("(")[0])return J(n);K(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(K(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||to(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){ti(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(t,e,r){var n;return(n=function(t,e){if("object"!==tt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function to(t,e){if(t){if("string"==typeof t)return ta(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tc=function(t,e,r){return t+(e-t)*r},tu=function(t){return t.from!==t.to},tl=function t(e,r,n){var i=q(function(t,r){if(tu(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(n,2)||to(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return tn(tn({},r),{},{from:o,velocity:a})}return r},r);return n<1?q(function(t,e){return tu(e)?tn(tn({},e),{},{velocity:tc(e.velocity,i[t].velocity,n),from:tc(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let ts=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return tn(tn({},r),{},ti({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return tn(tn({},r),{},ti({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=tl(r,l,a),i(tn(tn(tn({},t),e),q(function(t,e){return e.from},l))),o=n,Object.values(l).filter(tu).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,l=q(function(t,e){return tc.apply(void 0,te(e).concat([r(c)]))},u);if(i(tn(tn(tn({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=q(function(t,e){return tc.apply(void 0,te(e).concat([r(1)]))},u);i(tn(tn(tn({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function th(t){return function(t){if(Array.isArray(t))return td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return td(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return td(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function td(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ty(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(r),!0).forEach(function(e){tm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tm(t,e,r){return(e=tb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tb(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tg(t,e){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tO(t)}function tO(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tw(t){return(tw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tS=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&tg(o,t);var e,r,i=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tw(o);return t=e?Reflect.construct(r,arguments,tw(this).constructor):r.apply(this,arguments),tx(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=i.call(this,t,e),n=r.props,a=n.isActive,c=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tO(r)),r.changeStyle=r.changeStyle.bind(tO(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tx(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},tx(r);r.state={style:c?tm({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:i?tm({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(l);return}if(!D(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?c:t.to;if(this.state&&u){var p={style:i?tm({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=ts(r,n,Z(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(th(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(th(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=X(p,o,c),d=tv(tv(tv({},f.style),u),{},{transition:h});return[].concat(th(t),[d,o,s]).filter(W)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n,i;this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return B(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return B(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);if("number"==typeof o){N(t.bind(null,a),o);return}t(o),N(t.bind(null,a));return}"object"===R(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}})}var o=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l){this.runJSAnimation(t);return}if(p.length>1){this.runStepAnimation(t);return}var y=c?tm({},c,u):u,v=X(Object.keys(y),a,l);d.start([s,o,tv(tv({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),i=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tp)),a=n.Children.count(e),c=this.state.style;if("function"==typeof e)return e(c);if(!i||0===a||r<=0)return e;var u=function(t){var e=t.props,r=e.style,i=e.className;return(0,n.cloneElement)(t,tv(tv({},o),{},{style:tv(tv({},void 0===r?{}:r),c),className:i}))};return 1===a?u(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return u(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tb(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);tS.displayName="Animate",tS.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tS.propTypes={from:o().oneOfType([o().object,o().string]),to:o().oneOfType([o().object,o().string]),attributeName:o().string,duration:o().number,begin:o().number,easing:o().oneOfType([o().string,o().func]),steps:o().arrayOf(o().shape({duration:o().number.isRequired,style:o().object.isRequired,easing:o().oneOfType([o().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),o().func]),properties:o().arrayOf("string"),onAnimationEnd:o().func})),children:o().oneOfType([o().node,o().func]),isActive:o().bool,canBegin:o().bool,onAnimationEnd:o().func,shouldReAnimate:o().bool,onAnimationStart:o().func,onAnimationReStart:o().func};let tj=tS},5402:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},6697:(t,e,r)=>{"use strict";r.d(e,{A:()=>function t(){var e=new n,r=[],i=[],o=c;function u(t){let n=e.get(t);if(void 0===n){if(o!==c)return o;e.set(t,n=r.push(t)-1)}return i[n%i.length]}return u.domain=function(t){if(!arguments.length)return r.slice();for(let i of(r=[],e=new n,t))e.has(i)||e.set(i,r.push(i)-1);return u},u.range=function(t){return arguments.length?(i=Array.from(t),u):i.slice()},u.unknown=function(t){return arguments.length?(o=t,u):o},u.copy=function(){return t(r,i).unknown(o)},a.C.apply(u,arguments),u},h:()=>c});class n extends Map{constructor(t,e=o){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(i(this,t))}has(t){return super.has(i(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function i({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function o(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(53188);let c=Symbol("implicit")},7970:(t,e,r)=>{"use strict";r.d(e,{J:()=>h,M:()=>y});var n=r(34545),i=r(67111),o=r(4390),a=r(4828);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},d),t),c=(0,n.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=s[0],y=s[1];(0,n.useEffect)(function(){if(c.current&&c.current.getTotalLength)try{var t=c.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,O=r.className,w=r.animationEasing,S=r.animationDuration,j=r.animationBegin,A=r.isAnimationActive,P=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,i.A)("recharts-rectangle",O);return P?n.createElement(o.Ay,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:S,animationEasing:w,isActive:P},function(t){var e=t.width,i=t.height,l=t.x,s=t.y;return n.createElement(o.Ay,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:S,isActive:A,easing:w},n.createElement("path",u({},(0,a.J9)(r,!0),{className:E,d:p(l,s,e,i,x),ref:c})))}):n.createElement("path",u({},(0,a.J9)(r,!0),{className:E,d:p(v,m,b,g,x)}))}},8771:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},9830:(t,e,r)=>{var n=r(64220),i=r(35807),o=r(30627);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},10219:(t,e,r)=>{"use strict";r.d(e,{K:()=>s});var n=r(34545);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(o=function(){return!!t})()}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t,e,r){return(e=l(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}var s=function(t){function e(){var t,r;if(!(this instanceof e))throw TypeError("Cannot call a class as a function");return t=e,r=arguments,t=a(t),function(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,o()?Reflect.construct(t,r||[],a(this).constructor):t.apply(this,r))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t),function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,l(n.key),n)}}(e.prototype,[{key:"render",value:function(){return null}}]),Object.defineProperty(e,"prototype",{writable:!1}),e}(n.Component);u(s,"displayName","ZAxis"),u(s,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"})},14689:(t,e,r)=>{"use strict";r.d(e,{W:()=>v});var n=r(34545),i=r(67111),o=r(56671),a=r(35785),c=r(60129);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(l=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function p(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function y(t){var e=t.xAxisId,r=(0,o.yi)(),u=(0,o.rY)(),l=(0,o.AF)(e);return null==l?null:n.createElement(a.u,d({},l,{className:(0,i.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))}var v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","XAxis"),p(v,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},14734:(t,e,r)=>{"use strict";r.d(e,{Y:()=>s});var n=r(34545),i=r(68194),o=r.n(i),a=r(36068),c=r(17435),u=["component"];function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t){var e,r=t.component,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,u);return(0,n.isValidElement)(r)?e=(0,n.cloneElement)(r,i):o()(r)?e=(0,n.createElement)(r,i):(0,c.R)(!1,"Customized's props `component` must be React.element or Function, but got %s.",l(r)),n.createElement(a.W,{className:"recharts-customized-wrapper"},e)}s.displayName="Customized"},16999:(t,e,r)=>{var n=r(70992);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},20588:(t,e,r)=>{var n=r(30857),i=r(80997),o=r(37616);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},24097:(t,e,r)=>{"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){(function(t,e,r){var i;(i=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=n(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==n(i)?i:i+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{IZ:()=>c,Kg:()=>a,yy:()=>f}),r(22385),r(34545),r(68194);var a=Math.PI/180,c=function(t,e,r,n){return{x:t+Math.cos(-a*n)*r,y:e+Math.sin(-a*n)*r}},u=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=u({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},f=function(t,e){var r,n=l({x:t.x,y:t.y},e),i=n.radius,a=n.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var f=s(e),p=f.startAngle,h=f.endAngle,d=a;if(p<=h){for(;d>h;)d-=360;for(;d<p;)d+=360;r=d>=p&&d<=h}else{for(;d>p;)d-=360;for(;d<h;)d+=360;r=d>=h&&d<=p}return r?o(o({},e),{},{radius:i,angle:d+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null}},30857:(t,e,r)=>{var n=r(9006);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},35785:(t,e,r)=>{"use strict";r.d(e,{u:()=>L});var n=r(34545),i=r(68194),o=r.n(i),a=r(4358),c=r.n(a),u=r(67111),l=r(15080),s=r(36068),f=r(66812),p=r(41091),h=r(11809),d=r(29925),y=r(4828),v=r(85477),m=r(99283),b=r(91536);function g(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function x(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var j=["viewBox"],A=["viewBox"],P=["ticks"];function E(t){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D=function(){return!!t})()}function C(t){return(C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function N(t,e){return(N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function R(t,e,r){return(e=B(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=E(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:e+""}var L=function(t){var e,r;function i(t){var e,r,n;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r=i,n=[t],r=C(r),(e=function(t,e){if(e&&("object"===E(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,D()?Reflect.construct(r,n||[],C(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&N(i,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=_(t,j),i=this.props,o=i.viewBox,a=_(i,A);return!(0,l.b)(r,o)||!(0,l.b)(n,a)||!(0,l.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,d=c.tickSize,y=c.mirror,v=c.tickMargin,m=y?-1:1,b=t.tickSize||d,g=(0,h.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+ +!y*f)-m*b)-m*v,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=u+ +!y*s)-m*b)-m*v,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=u+ +y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(i=l+ +y*f)+m*b)+m*v,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=T(T(T({},(0,y.J9)(this.props,!1)),(0,y.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=T(T({},f),{},{x1:e,y1:r+p*o,x2:e+i,y2:r+p*o})}else{var h=+("left"===a&&!l||"right"===a&&l);f=T(T({},f),{},{x1:e+h*i,y1:r,x2:e+h*i,y2:r+o})}return n.createElement("line",k({},f,{className:(0,u.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var a=this,l=this.props,f=l.tickLine,p=l.stroke,O=l.tick,w=l.tickFormatter,j=l.unit,A=function(t,e,r){var n,i,a,c,u,l=t.tick,s=t.ticks,f=t.viewBox,p=t.minTickGap,d=t.orientation,y=t.interval,O=t.tickFormatter,w=t.unit,j=t.angle;if(!s||!s.length||!l)return[];if((0,h.Et)(y)||m.m.isSsr)return g(s,("number"==typeof y&&(0,h.Et)(y)?y:0)+1);var A=[],P="top"===d||"bottom"===d?"width":"height",E=w&&"width"===P?(0,v.Pu)(w,{fontSize:e,letterSpacing:r}):{width:0,height:0},k=function(t,n){var i,a,c=o()(O)?O(t.value,n):t.value;return"width"===P?(a={width:(i=(0,v.Pu)(c,{fontSize:e,letterSpacing:r})).width+E.width,height:i.height+E.height},(0,b.bx)(a,j)):(0,v.Pu)(c,{fontSize:e,letterSpacing:r})[P]},M=s.length>=2?(0,h.sA)(s[1].coordinate-s[0].coordinate):1,T=(n="width"===P,i=f.x,a=f.y,c=f.width,u=f.height,1===M?{start:n?i:a,end:n?i+c:a+u}:{start:n?i+c:a+u,end:n?i:a});return"equidistantPreserveStart"===y?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(o=function(){var e,o=null==n?void 0:n[l];if(void 0===o)return{v:g(n,s)};var a=l,p=function(){return void 0===e&&(e=r(o,a)),e},h=o.coordinate,d=0===l||x(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+i),l+=s)}())return o.v;return[]}(M,T,k,s,p):("preserveStart"===y||"preserveStartEnd"===y?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=S(S({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),x(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+i),a[c-1]=S(S({},s),{},{isShow:!0}))}for(var h=o?c-1:c,d=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var s=t*(o.coordinate-t*c()/2-u);a[e]=o=S(S({},o),{},{tickCoord:s<0?o.coordinate-s*t:o.coordinate})}else a[e]=o=S(S({},o),{},{tickCoord:o.coordinate});x(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=S(S({},o),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(M,T,k,s,p,"preserveStartEnd"===y):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);o[e]=l=S(S({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else o[e]=l=S(S({},l),{},{tickCoord:l.coordinate});x(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+i),o[e]=S(S({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return o}(M,T,k,s,p)).filter(function(t){return t.isShow})}(T(T({},this.props),{},{ticks:t}),e,r),P=this.getTickTextAnchor(),E=this.getTickVerticalAnchor(),M=(0,y.J9)(this.props,!1),_=(0,y.J9)(O,!1),I=T(T({},M),{},{fill:"none"},(0,y.J9)(f,!1)),D=A.map(function(t,e){var r=a.getTickLineCoord(t),l=r.line,h=r.tick,y=T(T(T(T({textAnchor:P,verticalAnchor:E},M),{},{stroke:"none",fill:p},_),h),{},{index:e,payload:t,visibleTicksCount:A.length,tickFormatter:w});return n.createElement(s.W,k({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,d.XC)(a.props,t,e)),f&&n.createElement("line",k({},I,l,{className:(0,u.A)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),O&&i.renderTickItem(O,y,"".concat(o()(w)?w(t.value,e):t.value).concat(j||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},D)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,i=e.width,a=e.height,c=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,h=f.ticks,d=_(f,P),y=h;return(o()(c)&&(y=c(h&&h.length>0?this.props:d)),i<=0||a<=0||!y||!y.length)?null:n.createElement(s.W,{className:(0,u.A)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var i,a=(0,u.A)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,T(T({},e),{},{className:a})):o()(t)?t(T(T({},e),{},{className:a})):n.createElement(f.E,k({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&I(i.prototype,e),r&&I(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);R(L,"displayName","CartesianAxis"),R(L,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},35883:(t,e,r)=>{t.exports=r(60048)()},36068:(t,e,r)=>{"use strict";r.d(e,{W:()=>u});var n=r(34545),i=r(67111),o=r(4828),a=["children","className"];function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var u=n.forwardRef(function(t,e){var r=t.children,u=t.className,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,a),s=(0,i.A)("recharts-layer",u);return n.createElement("g",c({className:s},(0,o.J9)(l,!0),{ref:e}),r)})},41091:(t,e,r)=>{"use strict";r.d(e,{J:()=>A});var n=r(34545),i=r(22385),o=r.n(i),a=r(68194),c=r.n(a),u=r(19529),l=r.n(u),s=r(67111),f=r(66812),p=r(4828),h=r(11809),d=r(24097);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,r=t.formatter,n=o()(t.children)?e:t.children;return c()(r)?r(n):n},w=function(t,e,r){var i,a,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u.cx,y=u.cy,v=u.innerRadius,m=u.outerRadius,b=u.startAngle,g=u.endAngle,O=u.clockWise,w=(v+m)/2,S=(0,h.sA)(g-b)*Math.min(Math.abs(g-b),360),j=S>=0?1:-1;"insideStart"===c?(i=b+j*l,a=O):"insideEnd"===c?(i=g-j*l,a=!O):"end"===c&&(i=g+j*l,a=O),a=S<=0?a:!a;var A=(0,d.IZ)(p,y,w,i),P=(0,d.IZ)(p,y,w,i+(a?1:-1)*359),E="M".concat(A.x,",").concat(A.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!a,",\n    ").concat(P.x,",").concat(P.y),k=o()(t.id)?(0,h.NF)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(k)},e))},S=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,d.IZ)(i,o,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.IZ)(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},j=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,s=u>=0?1:-1,f=s*n,p=s>0?"end":"start",d=s>0?"start":"end",y=c>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return g(g({},{x:o+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return g(g({},{x:o+c/2,y:a+u+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var x={x:o-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:u}:{})}if("right"===i){var O={x:o+c+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?g({x:o+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===i?g({x:o+c-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===i?g({x:o+c/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===i?g({x:o+c/2,y:a+u-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===i?g({x:o+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===i?g({x:o+c-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===i?g({x:o+v,y:a+u-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===i?g({x:o+c-v,y:a+u-f,textAnchor:m,verticalAnchor:p},w):l()(i)&&((0,h.Et)(i.x)||(0,h._3)(i.x))&&((0,h.Et)(i.y)||(0,h._3)(i.y))?g({x:o+(0,h.F4)(i.x,c),y:a+(0,h.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):g({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function A(t){var e,r=t.offset,i=g({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,v)),a=i.viewBox,u=i.position,l=i.value,d=i.children,y=i.content,m=i.className,b=i.textBreakAll;if(!a||o()(l)&&o()(d)&&!(0,n.isValidElement)(y)&&!c()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,i);if(c()(y)){if(e=(0,n.createElement)(y,i),(0,n.isValidElement)(e))return e}else e=O(i);var A="cx"in a&&(0,h.Et)(a.cx),P=(0,p.J9)(i,!0);if(A&&("insideStart"===u||"insideEnd"===u||"end"===u))return w(i,e,P);var E=A?S(i):j(i);return n.createElement(f.E,x({className:(0,s.A)("recharts-label",void 0===m?"":m)},P,E,{breakAll:b}),e)}A.displayName="Label";var P=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.Et)(y)&&(0,h.Et)(v)){if((0,h.Et)(s)&&(0,h.Et)(f))return{x:s,y:f,width:y,height:v};if((0,h.Et)(p)&&(0,h.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,h.Et)(s)&&(0,h.Et)(f)?{x:s,y:f,width:0,height:0}:(0,h.Et)(e)&&(0,h.Et)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};A.parseViewBox=P,A.renderCallByParent=function(t,e){var r,i,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var a=t.children,u=P(t),s=(0,p.aS)(a,A).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!o)return s;return[(r=t.label,i=e||u,r?!0===r?n.createElement(A,{key:"label-implicit",viewBox:i}):(0,h.vh)(r)?n.createElement(A,{key:"label-implicit",viewBox:i,value:r}):(0,n.isValidElement)(r)?r.type===A?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:i}):n.createElement(A,{key:"label-implicit",content:r,viewBox:i}):c()(r)?n.createElement(A,{key:"label-implicit",content:r,viewBox:i}):l()(r)?n.createElement(A,x({viewBox:i},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},41460:(t,e,r)=>{var n=r(75762),i=r(30627),o=r(18204),a=r(75955);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},41751:(t,e,r)=>{"use strict";r.d(e,{Z:()=>A});var n=r(34545),i=r(22385),o=r.n(i),a=r(19529),c=r.n(a),u=r(68194),l=r.n(u),s=r(66322),f=r.n(s),p=r(41091),h=r(36068),d=r(4828),y=r(60129);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function A(t){var e=t.valueAccessor,r=void 0===e?j:e,i=S(t,m),a=i.data,c=i.dataKey,u=i.clockWise,l=i.id,s=i.textBreakAll,f=S(i,b);return a&&a.length?n.createElement(h.W,{className:"recharts-label-list"},a.map(function(t,e){var i=o()(c)?r(t,e):(0,y.kr)(t&&t.payload,c),a=o()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p.J,x({},(0,d.J9)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:i,textBreakAll:s,viewBox:p.J.parseViewBox(o()(u)?t:w(w({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}A.displayName="LabelList",A.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var o=t.children,a=(0,d.aS)(o,A).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return i?[(r=t.label)?!0===r?n.createElement(A,{key:"labelList-implicit",data:e}):n.isValidElement(r)||l()(r)?n.createElement(A,{key:"labelList-implicit",data:e,content:r}):c()(r)?n.createElement(A,x({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return g(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return g(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a}},45843:(t,e,r)=>{"use strict";r.d(e,{u:()=>v});var n=r(34545),i=r(66864),o=r(36068),a=r(4828),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}var v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=p(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f()?Reflect.construct(t,e||[],p(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&h(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,u=t.width,f=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,c),m=(0,a.J9)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,i.A)(!1);var b=p.map(function(t){var i,a,c=h(t,f),p=c.x,v=c.y,b=c.value,g=c.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(g,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=O[0],a=O[1]}else i=a=g;if("vertical"===r){var w=d.scale,S=v+e,j=S+u,A=S-u,P=w(b-i),E=w(b+a);x.push({x1:E,y1:j,x2:E,y2:A}),x.push({x1:P,y1:S,x2:E,y2:S}),x.push({x1:P,y1:j,x2:P,y2:A})}else if("horizontal"===r){var k=y.scale,M=p+e,T=M-u,_=M+u,I=k(b-i),D=k(b+a);x.push({x1:T,y1:D,x2:_,y2:D}),x.push({x1:M,y1:I,x2:M,y2:D}),x.push({x1:T,y1:I,x2:_,y2:I})}return n.createElement(o.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(o.W,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);d(v,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),d(v,"displayName","ErrorBar")},46186:t=>{t.exports=function(t,e){return t<e}},47359:(t,e,r)=>{"use strict";r.d(e,{A:()=>o,z:()=>a});var n=r(53188),i=r(6697);function o(){var t,e,r=(0,i.A)().unknown(void 0),a=r.domain,c=r.range,u=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<u,i=n?l:u,o=n?u:l;t=(o-i)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),i+=(o-i-t*(r-f))*h,e=t*(1-f),s&&(i=Math.round(i),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return i+t*e});return c(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([u,l]=t,u*=1,l*=1,d()):[u,l]},r.rangeRound=function(t){return[u,l]=t,u*=1,l*=1,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return o(a(),[u,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(o.apply(null,arguments).paddingInner(1))}},49070:(t,e,r)=>{var n=r(77918),i=r(65870);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},49365:(t,e,r)=>{"use strict";r.d(e,{I:()=>E});var n=r(52778),i=r(69303),o=r(85283),a=r(23792),c=r(66566),u=r(18490),l=class extends a.Q{constructor(t,e){super(),this.options=e,this.#t=t,this.#e=null,this.#r=(0,c.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#t;#n=void 0;#i=void 0;#o=void 0;#a;#c;#r;#e;#u;#l;#s;#f;#p;#h;#d=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#n.addObserver(this),s(this.#n,this.options)?this.#y():this.updateResult(),this.#v())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return f(this.#n,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return f(this.#n,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#m(),this.#b(),this.#n.removeObserver(this)}setOptions(t){let e=this.options,r=this.#n;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Eh)(this.options.enabled,this.#n))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#g(),this.#n.setOptions(this.options),e._defaulted&&!(0,u.f8)(this.options,e)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#n,observer:this});let n=this.hasListeners();n&&p(this.#n,r,this.options,e)&&this.#y(),this.updateResult(),n&&(this.#n!==r||(0,u.Eh)(this.options.enabled,this.#n)!==(0,u.Eh)(e.enabled,this.#n)||(0,u.d2)(this.options.staleTime,this.#n)!==(0,u.d2)(e.staleTime,this.#n))&&this.#x();let i=this.#O();n&&(this.#n!==r||(0,u.Eh)(this.options.enabled,this.#n)!==(0,u.Eh)(e.enabled,this.#n)||i!==this.#h)&&this.#w(i)}getOptimisticResult(t){var e,r;let n=this.#t.getQueryCache().build(this.#t,t),i=this.createResult(n,t);return e=this,r=i,(0,u.f8)(e.getCurrentResult(),r)||(this.#o=i,this.#c=this.options,this.#a=this.#n.state),i}getCurrentResult(){return this.#o}trackResult(t,e){return new Proxy(t,{get:(t,r)=>(this.trackProp(r),e?.(r),Reflect.get(t,r))})}trackProp(t){this.#d.add(t)}getCurrentQuery(){return this.#n}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),r=this.#t.getQueryCache().build(this.#t,e);return r.fetch().then(()=>this.createResult(r,e))}fetch(t){return this.#y({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#o))}#y(t){this.#g();let e=this.#n.fetch(this.options,t);return t?.throwOnError||(e=e.catch(u.lQ)),e}#x(){this.#m();let t=(0,u.d2)(this.options.staleTime,this.#n);if(u.S$||this.#o.isStale||!(0,u.gn)(t))return;let e=(0,u.j3)(this.#o.dataUpdatedAt,t);this.#f=setTimeout(()=>{this.#o.isStale||this.updateResult()},e+1)}#O(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#n):this.options.refetchInterval)??!1}#w(t){this.#b(),this.#h=t,!u.S$&&!1!==(0,u.Eh)(this.options.enabled,this.#n)&&(0,u.gn)(this.#h)&&0!==this.#h&&(this.#p=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#y()},this.#h))}#v(){this.#x(),this.#w(this.#O())}#m(){this.#f&&(clearTimeout(this.#f),this.#f=void 0)}#b(){this.#p&&(clearInterval(this.#p),this.#p=void 0)}createResult(t,e){let r,n=this.#n,i=this.options,a=this.#o,l=this.#a,f=this.#c,d=t!==n?t.state:this.#i,{state:y}=t,v={...y},m=!1;if(e._optimisticResults){let r=this.hasListeners(),a=!r&&s(t,e),c=r&&p(t,n,e,i);(a||c)&&(v={...v,...(0,o.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(v.fetchStatus="idle")}let{error:b,errorUpdatedAt:g,status:x}=v;r=v.data;let O=!1;if(void 0!==e.placeholderData&&void 0===r&&"pending"===x){let t;a?.isPlaceholderData&&e.placeholderData===f?.placeholderData?(t=a.data,O=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#s?.state.data,this.#s):e.placeholderData,void 0!==t&&(x="success",r=(0,u.pl)(a?.data,t,e),m=!0)}if(e.select&&void 0!==r&&!O)if(a&&r===l?.data&&e.select===this.#u)r=this.#l;else try{this.#u=e.select,r=e.select(r),r=(0,u.pl)(a?.data,r,e),this.#l=r,this.#e=null}catch(t){this.#e=t}this.#e&&(b=this.#e,r=this.#l,g=Date.now(),x="error");let w="fetching"===v.fetchStatus,S="pending"===x,j="error"===x,A=S&&w,P=void 0!==r,E={status:x,fetchStatus:v.fetchStatus,isPending:S,isSuccess:"success"===x,isError:j,isInitialLoading:A,isLoading:A,data:r,dataUpdatedAt:v.dataUpdatedAt,error:b,errorUpdatedAt:g,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>d.dataUpdateCount||v.errorUpdateCount>d.errorUpdateCount,isFetching:w,isRefetching:w&&!S,isLoadingError:j&&!P,isPaused:"paused"===v.fetchStatus,isPlaceholderData:m,isRefetchError:j&&P,isStale:h(t,e),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===E.status?t.reject(E.error):void 0!==E.data&&t.resolve(E.data)},r=()=>{e(this.#r=E.promise=(0,c.T)())},i=this.#r;switch(i.status){case"pending":t.queryHash===n.queryHash&&e(i);break;case"fulfilled":("error"===E.status||E.data!==i.value)&&r();break;case"rejected":("error"!==E.status||E.error!==i.reason)&&r()}}return E}updateResult(){let t=this.#o,e=this.createResult(this.#n,this.options);this.#a=this.#n.state,this.#c=this.options,void 0!==this.#a.data&&(this.#s=this.#n),!(0,u.f8)(e,t)&&(this.#o=e,this.#S({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#d.size)return!0;let n=new Set(r??this.#d);return this.options.throwOnError&&n.add("error"),Object.keys(this.#o).some(e=>this.#o[e]!==t[e]&&n.has(e))})()}))}#g(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#n)return;let e=this.#n;this.#n=t,this.#i=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#v()}#S(t){i.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#o)}),this.#t.getQueryCache().notify({query:this.#n,type:"observerResultsUpdated"})})}};function s(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&f(t,e,e.refetchOnMount)}function f(t,e,r){if(!1!==(0,u.Eh)(e.enabled,t)&&"static"!==(0,u.d2)(e.staleTime,t)){let n="function"==typeof r?r(t):r;return"always"===n||!1!==n&&h(t,e)}return!1}function p(t,e,r,n){return(t!==e||!1===(0,u.Eh)(n.enabled,t))&&(!r.suspense||"error"!==t.state.status)&&h(t,r)}function h(t,e){return!1!==(0,u.Eh)(e.enabled,t)&&t.isStaleByTime((0,u.d2)(e.staleTime,t))}var d=r(34545),y=r(52950);r(47093);var v=d.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),m=()=>d.useContext(v),b=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},g=t=>{d.useEffect(()=>{t.clearReset()},[t])},x=t=>{let{result:e,errorResetBoundary:r,throwOnError:n,query:i,suspense:o}=t;return e.isError&&!r.isReset()&&!e.isFetching&&i&&(o&&void 0===e.data||(0,u.GU)(n,[e.error,i]))},O=d.createContext(!1),w=()=>d.useContext(O);O.Provider;var S=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),r=t.staleTime;t.staleTime="function"==typeof r?(...t)=>e(r(...t)):e(r),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},j=(t,e)=>t.isLoading&&t.isFetching&&!e,A=(t,e)=>t?.suspense&&e.isPending,P=(t,e,r)=>e.fetchOptimistic(t).catch(()=>{r.clearReset()});function E(t,e){return function(t,e,r){var n,o,a,c,l;let s=w(),f=m(),p=(0,y.jE)(r),h=p.defaultQueryOptions(t);null===(o=p.getDefaultOptions().queries)||void 0===o||null===(n=o._experimental_beforeQuery)||void 0===n||n.call(o,h),h._optimisticResults=s?"isRestoring":"optimistic",S(h),b(h,f),g(f);let v=!p.getQueryCache().get(h.queryHash),[O]=d.useState(()=>new e(p,h)),E=O.getOptimisticResult(h),k=!s&&!1!==t.subscribed;if(d.useSyncExternalStore(d.useCallback(t=>{let e=k?O.subscribe(i.jG.batchCalls(t)):u.lQ;return O.updateResult(),e},[O,k]),()=>O.getCurrentResult(),()=>O.getCurrentResult()),d.useEffect(()=>{O.setOptions(h)},[h,O]),A(h,E))throw P(h,O,f);if(x({result:E,errorResetBoundary:f,throwOnError:h.throwOnError,query:p.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw E.error;if(null===(c=p.getDefaultOptions().queries)||void 0===c||null===(a=c._experimental_afterQuery)||void 0===a||a.call(c,h,E),h.experimental_prefetchInRender&&!u.S$&&j(E,s)){let t=v?P(h,O,f):null===(l=p.getQueryCache().get(h.queryHash))||void 0===l?void 0:l.promise;null==t||t.catch(u.lQ).finally(()=>{O.updateResult()})}return h.notifyOnChangeProps?E:O.trackResult(E)}(t,l,e)}},53188:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function i(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>i})},56671:(t,e,r)=>{"use strict";r.d(e,{DR:()=>d,rY:()=>x,yi:()=>g,Yp:()=>y,sk:()=>b,AF:()=>v,Nk:()=>m});var n=r(34545),i=r(66864);r(85885),r(68221);var o=r(72638),a=r.n(o)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),c=(0,n.createContext)(void 0),u=(0,n.createContext)(void 0),l=(0,n.createContext)(void 0),s=(0,n.createContext)({}),f=(0,n.createContext)(void 0),p=(0,n.createContext)(0),h=(0,n.createContext)(0),d=function(t){var e=t.state,r=e.xAxisMap,i=e.yAxisMap,o=e.offset,d=t.clipPathId,y=t.children,v=t.width,m=t.height,b=a(o);return n.createElement(c.Provider,{value:r},n.createElement(u.Provider,{value:i},n.createElement(s.Provider,{value:o},n.createElement(l.Provider,{value:b},n.createElement(f.Provider,{value:d},n.createElement(p.Provider,{value:m},n.createElement(h.Provider,{value:v},y)))))))},y=function(){return(0,n.useContext)(f)},v=function(t){var e=(0,n.useContext)(c);null==e&&(0,i.A)(!1);var r=e[t];return null==r&&(0,i.A)(!1),r},m=function(t){var e=(0,n.useContext)(u);null==e&&(0,i.A)(!1);var r=e[t];return null==r&&(0,i.A)(!1),r},b=function(){return(0,n.useContext)(l)},g=function(){return(0,n.useContext)(h)},x=function(){return(0,n.useContext)(p)}},57042:(t,e,r)=>{var n=r(38954);t.exports=function(t,e){return n(t,e)}},59504:(t,e,r)=>{var n=r(30627),i=r(80252),o=r(35654);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},60048:(t,e,r)=>{var n=r(86675),i=r(19202),o=r(70992);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},60129:(t,e,r)=>{"use strict";r.d(e,{s0:()=>n8,gH:()=>n0,YB:()=>ii,HQ:()=>ie,xi:()=>io,Hj:()=>ix,BX:()=>n5,tA:()=>n2,DW:()=>id,y2:()=>ih,nb:()=>ip,Ay:()=>nZ,vf:()=>n4,Mk:()=>iv,Ps:()=>n1,Mn:()=>il,kA:()=>iy,Rh:()=>n7,w7:()=>is,zb:()=>iw,kr:()=>nV,_L:()=>n9,KC:()=>iO,A1:()=>n6,W7:()=>ir,AQ:()=>ig,_f:()=>ia});var n,i,o,a,c,u,l,s={};r.r(s),r.d(s,{scaleBand:()=>f.A,scaleDiverging:()=>function t(){var e=tN(rV()(tv));return e.copy=function(){return rY(e,t())},tS.K.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tX(rV()).domain([.1,1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},tS.K.apply(e,arguments)},scaleDivergingPow:()=>rZ,scaleDivergingSqrt:()=>r0,scaleDivergingSymlog:()=>function t(){var e=tH(rV());return e.copy=function(){return rY(e,t()).constant(e.constant())},tS.K.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,td),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,td):[0,1],tN(n)},scaleImplicit:()=>t$.h,scaleLinear:()=>tR,scaleLog:()=>function t(){let e=tX(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tS.C.apply(e,arguments),e},scaleOrdinal:()=>t$.A,scalePoint:()=>f.z,scalePow:()=>tZ,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=S){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(+r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[A(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(g),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tS.C.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[A(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tS.C.apply(tN(c),arguments)},scaleRadial:()=>function t(){var e,r=tw(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(t1(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,td)).map(t1)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},tS.C.apply(o,arguments),tN(o)},scaleSequential:()=>function t(){var e=tN(r$()(tv));return e.copy=function(){return rY(e,t())},tS.K.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tX(r$()).domain([1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},tS.K.apply(e,arguments)},scaleSequentialPow:()=>rG,scaleSequentialQuantile:()=>function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t*=1))return r((A(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(g),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return t5(t);if(e>=1)return t2(t);var n,i=(n-1)*e,o=Math.floor(i),a=t2((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?t8:function(t=g){if(t===g)return t8;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(i,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(t3(e,n,r),o(e[i],a)>0&&t3(e,n,i);c<u;){for(t3(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?t3(e,n,u):t3(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(t5(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tS.K.apply(n,arguments)},scaleSequentialSqrt:()=>rJ,scaleSequentialSymlog:()=>function t(){var e=tH(r$());return e.copy=function(){return rY(e,t()).constant(e.constant())},tS.K.apply(e,arguments)},scaleSqrt:()=>t0,scaleSymlog:()=>function t(){var e=tH(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tS.C.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[A(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},tS.C.apply(o,arguments)},scaleTime:()=>rQ,scaleUtc:()=>rH,tickFormat:()=>tC});var f=r(47359);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=p?10:u>=h?5:u>=d?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?y(t,e,2*r):[n,i,o]}function v(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?y(e,t,r):y(t,e,r);if(!(o>=i))return[];let c=o-i+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((o-t)/a);else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((i+t)/a);else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function m(t,e,r){return y(t*=1,e*=1,r*=1)[2]}function b(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=g,r=(e,r)=>g(t(e),r),n=(e,r)=>t(e)-r):(e=t===g||t===x?t:w,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function w(){return 0}function S(t){return null===t?NaN:+t}let j=O(g),A=j.right;function P(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function k(){}j.left,O(S).center;var M="\\s*([+-]?\\d+)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",I=/^#([0-9a-f]{3,8})$/,D=RegExp(`^rgb\\(${M},${M},${M}\\)$`),C=RegExp(`^rgb\\(${_},${_},${_}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${T}\\)$`),R=RegExp(`^rgba\\(${_},${_},${_},${T}\\)$`),B=RegExp(`^hsl\\(${T},${_},${_}\\)$`),L=RegExp(`^hsla\\(${T},${_},${_},${T}\\)$`),U={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function z(){return this.rgb().formatHex()}function F(){return this.rgb().formatRgb()}function W(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=I.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?q(e):3===r?new Q(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?X(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?X(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=D.exec(t))?new Q(e[1],e[2],e[3],1):(e=C.exec(t))?new Q(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?X(e[1],e[2],e[3],e[4]):(e=R.exec(t))?X(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=B.exec(t))?V(e[1],e[2]/100,e[3]/100,1):(e=L.exec(t))?V(e[1],e[2]/100,e[3]/100,e[4]):U.hasOwnProperty(t)?q(U[t]):"transparent"===t?new Q(NaN,NaN,NaN,0):null}function q(t){return new Q(t>>16&255,t>>8&255,255&t,1)}function X(t,e,r,n){return n<=0&&(t=e=r=NaN),new Q(t,e,r,n)}function K(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof k||(i=W(i)),i)?new Q((i=i.rgb()).r,i.g,i.b,i.opacity):new Q:new Q(t,e,r,null==n?1:n)}function Q(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function H(){return`#${J(this.r)}${J(this.g)}${J(this.b)}`}function $(){let t=Y(this.opacity);return`${1===t?"rgb(":"rgba("}${G(this.r)}, ${G(this.g)}, ${G(this.b)}${1===t?")":`, ${t})`}`}function Y(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function G(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function J(t){return((t=G(t))<16?"0":"")+t.toString(16)}function V(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Z(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=W(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new tt(a,c,u,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function ti(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}P(k,W,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:z,formatHex:z,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Z(this).formatHsl()},formatRgb:F,toString:F}),P(Q,K,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Q(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Q(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Q(G(this.r),G(this.g),G(this.b),Y(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:H,formatHex:H,formatHex8:function(){return`#${J(this.r)}${J(this.g)}${J(this.b)}${J((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:$,toString:$})),P(tt,function(t,e,r,n){return 1==arguments.length?Z(t):new tt(t,e,r,null==n?1:n)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new Q(tn(t>=240?t-240:t+120,i,n),tn(t,i,n),tn(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),Y(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=Y(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));let to=t=>()=>t;function ta(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):to(isNaN(t)?e:t)}let tc=function t(e){var r,n=1==(r=+e)?ta:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):to(isNaN(t)?e:t)};function i(t,e){var r=n((t=K(t)).r,(e=K(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function tu(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=K(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}tu(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return ti((r-n/e)*e,a,i,o,c)}}),tu(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return ti((r-n/e)*e,i,o,a,c)}});function tl(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?to(e):("number"===i?tl:"string"===i?(n=W(e))?(e=n,tc):function(t,e){var r,n,i,o,a,c=ts.lastIndex=tf.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(i=ts.exec(t))&&(o=tf.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(i=i[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,s.push({i:u,x:tl(i,o)})),c=tf.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof W?tc:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tl:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function th(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tb(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=tm(i,n),o=r(a,o)):(n=tm(n,i),o=r(o,a)),function(t){return o(n(t))}}function tg(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=tm(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=A(t,e,1,n)-1;return o[r](i[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,r,n,i,o,a=ty,c=ty,u=tp,l=tv;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==tv&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?tg:tb,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(c,a.map(t),tl)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=th,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tw(){return tO()(tv,tv)}var tS=r(53188),tj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tA(t){var e;if(!(e=tj.exec(t)))throw Error("invalid format: "+t);return new tP({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tP(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tA.prototype=tP.prototype,tP.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tT={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var i=r[0],o=r[1],a=o-(n=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,c=i.length;return a===c?i:a>c?i+Array(a-c+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function t_(t){return t}var tI=Array.prototype.map,tD=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tC(t,e,r,n){var i,c,u,l=b(t,e,r);switch((n=tA(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null==n.precision&&!isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(s)/3)))-tk(Math.abs(l))))&&(n.precision=u),a(n,s);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(u=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=l)))-tk(i))+1)&&(n.precision=u-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(u=Math.max(0,-tk(Math.abs(l))))&&(n.precision=u-("%"===n.type)*2)}return o(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tC(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],l=o[c],s=10;for(l<u&&(i=u,u=l,l=i,i=a,a=c,c=i);s-- >0;){if((i=m(u,l,r))===n)return o[a]=u,o[c]=l,e(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else if(i<0)u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function tR(){var t=tw();return t.copy=function(){return tx(t,tR())},tS.C.apply(t,arguments),tN(t)}function tB(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function tL(t){return Math.log(t)}function tU(t){return Math.exp(t)}function tz(t){return-Math.log(-t)}function tF(t){return-Math.exp(-t)}function tW(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tq(t){return(e,r)=>-t(-e,r)}function tX(t){let e,r,n=t(tL,tU),i=n.domain,a=10;function c(){var o,c;return e=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),t=>Math.log(t)/o),r=10===(c=a)?tW:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=tq(e),r=tq(r),t(tz,tF)):t(tL,tU),n}return n.base=function(t){return arguments.length?(a=+t,c()):a},n.domain=function(t){return arguments.length?(i(t),c()):i()},n.ticks=t=>{let n,o,c=i(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<u)){if(o>l)break;d.push(o)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<u)){if(o>l)break;d.push(o)}2*d.length<h&&(d=v(u,l,h))}else d=v(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=tA(i)).precision||(i.trim=!0),i=o(i)),t===1/0)return i;let c=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=c?i(t):""}},n.nice=()=>i(tB(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tK(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tQ(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tH(t){var e=1,r=t(tK(1),tQ(e));return r.constant=function(r){return arguments.length?t(tK(e=+r),tQ(e)):e},tN(r)}o=(i=function(t){var e,r,i,o=void 0===t.grouping||void 0===t.thousands?t_:(e=tI.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",c=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?t_:(i=tI.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tA(t)).fill,r=t.align,i=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):tT[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?c:/[%p]/.test(g)?s:"",w=tT[g],S=/[defgprs%]/.test(g);function j(t){var a,c,s,h=x,j=O;if("c"===g)j=w(t)+j,t="";else{var A=(t*=1)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==i&&(A=!1),h=(A?"("===i?i:f:"-"===i||"("===i?"":i)+h,j=("s"===g?tD[8+n/3]:"")+j+(A&&"("===i?")":""),S){for(a=-1,c=t.length;++a<c;)if(48>(s=t.charCodeAt(a))||s>57){j=(46===s?u+t.slice(a+1):t.slice(a))+j,t=t.slice(0,a);break}}}v&&!d&&(t=o(t,1/0));var P=h.length+t.length+j.length,E=P<y?Array(y-P+1).join(e):"";switch(v&&d&&(t=o(E+t,E.length?y-j.length:1/0),E=""),r){case"<":t=h+t+j+E;break;case"=":t=h+E+t+j;break;case"^":t=E.slice(0,P=E.length>>1)+h+t+j+E.slice(P);break;default:t=E+h+t+j}return l(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),j.toString=function(){return t+""},j}return{format:h,formatPrefix:function(t,e){var r=h(((t=tA(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),i=Math.pow(10,-n),o=tD[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=i.formatPrefix;var t$=r(6697);function tY(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tG(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tJ(t){return t<0?-t*t:t*t}function tV(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tG,tJ):t(tY(r),tY(1/r)):r},tN(e)}function tZ(){var t=tV(tO());return t.copy=function(){return tx(t,tZ()).exponent(t.exponent())},tS.C.apply(t,arguments),t}function t0(){return tZ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function t5(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function t8(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function t3(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t6=new Date,t4=new Date;function t9(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>t9(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(t6.setTime(+e),t4.setTime(+n),t(t6),t(t4),Math.floor(r(t6,t4))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let t7=t9(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t7.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t9(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t7:null,t7.range;let et=t9(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t9(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let er=t9(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());er.range;let en=t9(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());en.range;let ei=t9(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());ei.range;let eo=t9(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);eo.range;let ea=t9(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let ec=t9(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function eu(t){return t9(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}ec.range;let el=eu(0),es=eu(1),ef=eu(2),ep=eu(3),eh=eu(4),ed=eu(5),ey=eu(6);function ev(t){return t9(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}el.range,es.range,ef.range,ep.range,eh.range,ed.range,ey.range;let em=ev(0),eb=ev(1),eg=ev(2),ex=ev(3),eO=ev(4),ew=ev(5),eS=ev(6);em.range,eb.range,eg.range,ex.range,eO.range,ew.range,eS.range;let ej=t9(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ej.range;let eA=t9(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eA.range;let eP=t9(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eP.every=t=>isFinite(t=Math.floor(t))&&t>0?t9(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eP.range;let eE=t9(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ek(t,e,r,n,i,o){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=O(([,,t])=>t).right(a,i);if(o===a.length)return t.every(b(e/31536e6,r/31536e6,n));if(0===o)return t7.every(Math.max(b(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t9(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[eM,eT]=ek(eE,eA,em,ec,ei,er),[e_,eI]=ek(eP,ej,el,eo,en,ee);function eD(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eC(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eR={"-":"",_:" ",0:"0"},eB=/^\s*\d+/,eL=/^%/,eU=/[\\^$*+?|[\]().{}]/g;function ez(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function eF(t){return t.replace(eU,"\\$&")}function eW(t){return RegExp("^(?:"+t.map(eF).join("|")+")","i")}function eq(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eX(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eQ(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eH(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function e$(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eB.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eJ(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eV(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eZ(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e0(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eB.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e8(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eB.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eB.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e4(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e9(t,e,r){var n=eB.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eB.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rt(t,e){return ez(t.getDate(),e,2)}function re(t,e){return ez(t.getHours(),e,2)}function rr(t,e){return ez(t.getHours()%12||12,e,2)}function rn(t,e){return ez(1+eo.count(eP(t),t),e,3)}function ri(t,e){return ez(t.getMilliseconds(),e,3)}function ro(t,e){return ri(t,e)+"000"}function ra(t,e){return ez(t.getMonth()+1,e,2)}function rc(t,e){return ez(t.getMinutes(),e,2)}function ru(t,e){return ez(t.getSeconds(),e,2)}function rl(t){var e=t.getDay();return 0===e?7:e}function rs(t,e){return ez(el.count(eP(t)-1,t),e,2)}function rf(t){var e=t.getDay();return e>=4||0===e?eh(t):eh.ceil(t)}function rp(t,e){return t=rf(t),ez(eh.count(eP(t),t)+(4===eP(t).getDay()),e,2)}function rh(t){return t.getDay()}function rd(t,e){return ez(es.count(eP(t)-1,t),e,2)}function ry(t,e){return ez(t.getFullYear()%100,e,2)}function rv(t,e){return ez((t=rf(t)).getFullYear()%100,e,2)}function rm(t,e){return ez(t.getFullYear()%1e4,e,4)}function rb(t,e){var r=t.getDay();return ez((t=r>=4||0===r?eh(t):eh.ceil(t)).getFullYear()%1e4,e,4)}function rg(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ez(e/60|0,"0",2)+ez(e%60,"0",2)}function rx(t,e){return ez(t.getUTCDate(),e,2)}function rO(t,e){return ez(t.getUTCHours(),e,2)}function rw(t,e){return ez(t.getUTCHours()%12||12,e,2)}function rS(t,e){return ez(1+ea.count(eE(t),t),e,3)}function rj(t,e){return ez(t.getUTCMilliseconds(),e,3)}function rA(t,e){return rj(t,e)+"000"}function rP(t,e){return ez(t.getUTCMonth()+1,e,2)}function rE(t,e){return ez(t.getUTCMinutes(),e,2)}function rk(t,e){return ez(t.getUTCSeconds(),e,2)}function rM(t){var e=t.getUTCDay();return 0===e?7:e}function rT(t,e){return ez(em.count(eE(t)-1,t),e,2)}function r_(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function rI(t,e){return t=r_(t),ez(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rD(t){return t.getUTCDay()}function rC(t,e){return ez(eb.count(eE(t)-1,t),e,2)}function rN(t,e){return ez(t.getUTCFullYear()%100,e,2)}function rR(t,e){return ez((t=r_(t)).getUTCFullYear()%100,e,2)}function rB(t,e){return ez(t.getUTCFullYear()%1e4,e,4)}function rL(t,e){var r=t.getUTCDay();return ez((t=r>=4||0===r?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function rU(){return"+0000"}function rz(){return"%"}function rF(t){return+t}function rW(t){return Math.floor(+t/1e3)}function rq(t){return new Date(t)}function rX(t){return t instanceof Date?+t:+new Date(+t)}function rK(t,e,r,n,i,o,a,c,u,l){var s=tw(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rX)):p().map(rq)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tB(r,t)):s},s.copy=function(){return tx(s,rK(t,e,r,n,i,o,a,c,u,l))},s}function rQ(){return tS.C.apply(rK(e_,eI,eP,ej,el,eo,en,ee,et,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rH(){return tS.C.apply(rK(eM,eT,eE,eA,em,ea,ei,er,et,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r$(){var t,e,r,n,i,o=0,a=1,c=tv,u=!1;function l(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(tp),l.rangeRound=s(th),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function rY(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rG(){var t=tV(r$());return t.copy=function(){return rY(t,rG()).exponent(t.exponent())},tS.K.apply(t,arguments)}function rJ(){return rG.apply(null,arguments).exponent(.5)}function rV(){var t,e,r,n,i,o,a,c=0,u=.5,l=1,s=1,f=tv,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=o(c*=1),e=o(u*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function rZ(){var t=tV(rV());return t.copy=function(){return rY(t,rZ()).exponent(t.exponent())},tS.K.apply(t,arguments)}function r0(){return rZ.apply(null,arguments).exponent(.5)}function r1(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}u=(c=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=eW(i),s=eq(i),f=eW(o),p=eq(o),h=eW(a),d=eq(a),y=eW(c),v=eq(c),m=eW(u),b=eq(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:rt,e:rt,f:ro,g:rv,G:rb,H:re,I:rr,j:rn,L:ri,m:ra,M:rc,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rF,s:rW,S:ru,u:rl,U:rs,V:rp,w:rh,W:rd,x:null,X:null,y:ry,Y:rm,Z:rg,"%":rz},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:rx,e:rx,f:rA,g:rR,G:rL,H:rO,I:rw,j:rS,L:rj,m:rP,M:rE,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rF,s:rW,S:rk,u:rM,U:rT,V:rI,w:rD,W:rC,x:null,X:null,y:rN,Y:rB,Z:rU,"%":rz},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return j(t,e,r,n)},d:e0,e:e0,f:e6,g:eG,G:eY,H:e2,I:e2,j:e1,L:e3,m:eZ,M:e5,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eV,Q:e9,s:e7,S:e8,u:eK,U:eQ,V:eH,w:eX,W:e$,x:function(t,e,n){return j(t,r,e,n)},X:function(t,e,r){return j(t,n,e,r)},y:eG,Y:eY,Z:eJ,"%":e4};function w(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=eR[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function S(t,e){return function(r){var n,i,o=eN(1900,void 0,1);if(j(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=eC(eN(o.y,0,1))).getUTCDay())>4||0===i?eb.ceil(n):eb(n),n=ea.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=eD(eN(o.y,0,1))).getDay())>4||0===i?es.ceil(n):es(n),n=eo.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?eC(eN(o.y,0,1)).getUTCDay():eD(eN(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,eC(o)):eD(o)}}function j(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=O[(i=e.charAt(a++))in eR?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=S(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=S(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c.parse,l=c.utcFormat,c.utcParse;var r2=r(80926),r5=r(82253);function r8(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r3(t,e){return t[e]}function r6(t){let e=[];return e.key=t,e}var r4=r(20588),r9=r.n(r4),r7=r(85198),nt=r.n(r7),ne=r(22385),nr=r.n(ne),nn=r(68194),ni=r.n(nn),no=r(39335),na=r.n(no),nc=r(4358),nu=r.n(nc),nl=r(78105),ns=r.n(nl),nf=r(88851),np=r.n(nf),nh=r(59496),nd=r.n(nh),ny=r(57042),nv=r.n(ny),nm=r(63383),nb=r.n(nm),ng=r(80598),nx=r.n(ng);function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nw=function(t){return t},nS={},nj=function(t){return t===nS},nA=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nj(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nP=function(t){return function t(e,r){return 1===e?r:nA(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==nS}).length;return a>=e?r.apply(void 0,i):t(e-a,nA(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return nj(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nO(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return nO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nO(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nE=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nk=nP(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nM=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nw;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},nT=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},n_=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};nP(function(t,e,r){var n=+t;return n+r*(+e-n)}),nP(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),nP(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let nI={rangeStep:function(t,e,r){for(var n=new(nx())(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new(nx())(t).abs().log(10).toNumber())+1}};function nD(t){return function(t){if(Array.isArray(t))return nR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nC(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||nN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){if(t){if("string"==typeof t)return nR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nR(t,e)}}function nR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nB(t){var e=nC(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function nL(t,e,r){if(t.lte(0))return new(nx())(0);var n=nI.getDigitCount(t.toNumber()),i=new(nx())(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new(nx())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new(nx())(Math.ceil(c))}function nU(t,e,r){var n=1,i=new(nx())(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new(nx())(10).pow(nI.getDigitCount(t)-1),i=new(nx())(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new(nx())(Math.floor(t)))}else 0===t?i=new(nx())(Math.floor((e-1)/2)):r||(i=new(nx())(Math.floor(t)));var a=Math.floor((e-1)/2);return nM(nk(function(t){return i.add(new(nx())(t-a).mul(n)).toNumber()}),nE)(0,e)}var nz=n_(function(t){var e=nC(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=nC(nB([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(nD(nE(0,i-1).map(function(){return 1/0}))):[].concat(nD(nE(0,i-1).map(function(){return-1/0})),[l]);return r>n?nT(s):s}if(u===l)return nU(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var c=nL(new(nx())(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new(nx())(0):(o=new(nx())(e).add(r).div(2)).sub(new(nx())(o).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new(nx())(r).sub(o).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:o.sub(new(nx())(u).mul(c)),tickMax:o.add(new(nx())(l).mul(c))})}(u,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=nI.rangeStep(h,d.add(new(nx())(.1).mul(p)),p);return r>n?nT(y):y});n_(function(t){var e=nC(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=nC(nB([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return nU(u,i,o);var s=nL(new(nx())(l).sub(u).div(a-1),o,0),f=nM(nk(function(t){return new(nx())(u).add(new(nx())(t).mul(s)).toNumber()}),nE)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?nT(f):f});var nF=n_(function(t,e){var r=nC(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nC(nB([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var l=Math.max(e,2),s=nL(new(nx())(u).sub(c).div(l-1),o,0),f=[].concat(nD(nI.rangeStep(new(nx())(c),new(nx())(u).sub(new(nx())(.99).mul(s)),s)),[u]);return n>i?nT(f):f}),nW=r(45843),nq=r(11809),nX=r(4828),nK=r(4293);function nQ(t){return(nQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nH(t){return function(t){if(Array.isArray(t))return n$(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return n$(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n$(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n$(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nY(Object(r),!0).forEach(function(e){nJ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nJ(t,e,r){var n;return(n=function(t,e){if("object"!=nQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nQ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nV(t,e,r){return nr()(t)||nr()(e)?r:(0,nq.vh)(e)?nu()(t,e,r):ni()(e)?e(t):r}function nZ(t,e,r,n){var i=ns()(t,function(t){return nV(t,e)});if("number"===r){var o=i.filter(function(t){return(0,nq.Et)(t)||parseFloat(t)});return o.length?[nt()(o),r9()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!nr()(t)}):i).map(function(t){return(0,nq.vh)(t)||t instanceof Date?t:""})}var n0=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,nq.sA)(s-l)!==(0,nq.sA)(f-s)){var h=[];if((0,nq.sA)(f-s)===(0,nq.sA)(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},n1=function(t){var e,r,n=t.type.displayName,i=null!==(e=t.type)&&void 0!==e&&e.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},n2=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,nX.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?nG(nG({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var O=nr()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(O)?void 0:(0,nq.F4)(O,r,0)})}}return o},n5=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=(0,nq.F4)(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=i&&(h-=(u-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((i-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(nH(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,nq.F4)(n,i,0,!0);i-2*y-(u-1)*l<=0&&(l=0);var v=(i-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(nH(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},n8=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=o-(a.left||0)-(a.right||0),u=(0,nK.g)({children:i,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,d=u.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,nq.Et)(t[p]))return nG(nG({},t),{},nJ({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,nq.Et)(t[h]))return nG(nG({},t),{},nJ({},h,t[h]+(f||0)))}return t},n3=function(t,e,r,n,i){var o=e.props.children,a=(0,nX.aS)(o,nW.u).filter(function(t){var e;return e=t.props.direction,!!nr()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(a&&a.length){var c=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=nV(e,r);if(nr()(n))return t;var i=Array.isArray(n)?[nt()(n),r9()(n)]:[n,n],o=c.reduce(function(t,r){var n=nV(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},n6=function(t,e,r,n,i){var o=e.map(function(e){return n3(t,e,r,i,n)}).filter(function(t){return!nr()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},n4=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&n3(t,e,o,n)||nZ(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},n9=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},n7=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,nq.sA)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!np()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},it=new WeakMap,ie=function(t,e){if("function"!=typeof e)return t;it.has(t)||it.set(t,new WeakMap);var r=it.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ir=function(t,e,r){var n=t.scale,i=t.type,o=t.layout,a=t.axisType;if("auto"===n)return"radial"===o&&"radiusAxis"===a?{scale:f.A(),realScaleType:"band"}:"radial"===o&&"angleAxis"===a?{scale:tR(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.z(),realScaleType:"point"}:"category"===i?{scale:f.A(),realScaleType:"band"}:{scale:tR(),realScaleType:"linear"};if(na()(n)){var c="scale".concat(nd()(n));return{scale:(s[c]||f.z)(),realScaleType:s[c]?c:"point"}}return ni()(n)?{scale:n}:{scale:f.z(),realScaleType:"point"}},ii=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},io=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},ia=function(t,e){if(!e||2!==e.length||!(0,nq.Et)(e[0])||!(0,nq.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,nq.Et)(t[0])||t[0]<r)&&(i[0]=r),(!(0,nq.Et)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},ic={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=np()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}r1(t,e)}},none:r1,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}r1(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<i;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=l/u)}r[a-1][1]+=r[a-1][0]=o,r1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=np()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},iu=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=ic[r];return(function(){var t=(0,r5.A)([]),e=r8,r=r1,n=r3;function i(i){var o,a,c=Array.from(t.apply(this,arguments),r6),u=c.length,l=-1;for(let t of i)for(o=0,++l;o<u;++o)(c[o][l]=[0,+n(t,c[o].key,l,i)]).data=t;for(o=0,a=(0,r2.A)(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r5.A)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r5.A)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?r8:"function"==typeof t?t:(0,r5.A)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?r1:t,i):r},i})().keys(n).value(function(t,e){return+nV(t,e,0)}).order(r8).offset(i)(t)},il=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!==(i=e.type)&&void 0!==i&&i.defaultProps?nG(nG({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if((0,nq.vh)(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[(0,nq.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return nG(nG({},t),{},nJ({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return nG(nG({},e),{},nJ({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:iu(t,a.items,i)}))},{})),nG(nG({},e),{},nJ({},o,c))},{})},is=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=nz(u,i,a);return t.domain([nt()(l),r9()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:nF(t.domain(),i,a)}:null};function ip(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!nr()(i[e.dataKey])){var c=(0,nq.eP)(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=nV(i,nr()(a)?e.dataKey:a);return nr()(u)?null:e.scale(u)}var ih=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=nV(o,e.dataKey,e.domain[a]);return nr()(c)?null:e.scale(c)-i/2+n},id=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},iy=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props).stackId;if((0,nq.vh)(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},iv=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[nt()(e.concat([t[0]]).filter(nq.Et)),r9()(e.concat([t[1]]).filter(nq.Et))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},im=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ib=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ig=function(t,e,r){if(ni()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,nq.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(im.test(t[0])){var i=+im.exec(t[0])[1];n[0]=e[0]-i}else ni()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,nq.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(ib.test(t[1])){var o=+ib.exec(t[1])[1];n[1]=e[1]+o}else ni()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},ix=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=nb()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},iO=function(t,e,r){return!t||!t.length||nv()(t,nu()(r,"type.defaultProps.domain"))?e:t},iw=function(t,e){var r=t.type.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return nG(nG({},(0,nX.J9)(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:n1(t),value:nV(e,n),type:c,payload:e,chartType:u,hide:l})}},61074:(t,e,r)=>{var n=r(30428),i=r(30627),o=r(87162),a=r(75955),c=r(19202);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},64220:(t,e,r)=>{var n=r(20161);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},66322:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},66437:(t,e,r)=>{var n=r(77918),i=r(84541),o=r(65870),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},66812:(t,e,r)=>{"use strict";r.d(e,{E:()=>B});var n=r(34545),i=r(22385),o=r.n(i),a=r(67111),c=r(11809),u=r(99283),l=r(4828),s=r(85477);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!==(e=b.exec(t))&&void 0!==e?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&d(r.prototype,t),e&&d(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=y.exec(e))&&void 0!==r?r:[],4),i=n[1],o=n[2],a=n[3],c=O.parse(null!=i?i:""),u=O.parse(null!=a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=v.exec(e))&&void 0!==s?s:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var S=/\(([^()]*)\)/;function j(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(S.exec(e),2)[1];e=e.replace(S,w(r))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var A=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],P=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return T(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var _=/[ \f\n\r\t\v\u2028\u2029]+/,I=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];o()(e)||(i=r?e.toString().split(""):e.toString().split(_));var a=i.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),c=r?0:(0,s.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:c}}catch(t){return null}},D=function(t,e,r,n,i){var o,a=t.maxLines,u=t.children,l=t.style,s=t.breakAll,f=(0,c.Et)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(I({breakAll:s,style:l,children:u.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=u.length-1,m=0;y<=v&&m<=u.length-1;){var b=Math.floor((y+v)/2),g=M(d(b-1),2),x=g[0],O=g[1],w=M(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){o=O;break}m++}return o||h},C=function(t){return[{words:o()(t)?[]:t.toString().split(_)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!u.m.isSsr){var c=I({breakAll:o,children:n,style:i});if(!c)return C(n);var l=c.wordsWithComputedWidth,s=c.spaceWidth;return D({breakAll:o,children:n,maxLines:a,style:i},l,s,e,r)}return C(n)},R="#808080",B=function(t){var e,r=t.x,i=void 0===r?0:r,o=t.y,u=void 0===o?0:o,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?R:b,x=k(t,A),O=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,S=x.dy,M=x.angle,T=x.className,_=x.breakAll,I=k(x,P);if(!(0,c.vh)(i)||!(0,c.vh)(u))return null;var D=i+((0,c.Et)(w)?w:0),C=u+((0,c.Et)(S)?S:0);switch(void 0===m?"end":m){case"start":e=j("calc(".concat(h,")"));break;case"middle":e=j("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=j("calc(".concat(O.length-1," * -").concat(f,")"))}var B=[];if(y){var L=O[0].width,U=x.width;B.push("scale(".concat(((0,c.Et)(U)?U/L:1)/L,")"))}return M&&B.push("rotate(".concat(M,", ").concat(D,", ").concat(C,")")),B.length&&(I.transform=B.join(" ")),n.createElement("text",E({},(0,l.J9)(I,!0),{x:D,y:C,className:(0,a.A)("recharts-text",T),textAnchor:void 0===v?"start":v,fill:g.includes("url")?R:g}),O.map(function(t,r){var i=t.words.join(_?"":" ");return n.createElement("tspan",{x:D,dy:0===r?e:f,key:"".concat(i,"-").concat(r)},i)}))}},66864:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});function n(t,e){if(!t)throw Error("Invariant failed")}},68221:(t,e,r)=>{var n=r(92331),i=r(93125),o=r(30627),a=r(75955),c=r(19202);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},69415:(t,e,r)=>{var n=r(58831),i=r(30627),o=r(16999),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},70992:(t,e,r)=>{var n=r(11310),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},72639:(t,e,r)=>{"use strict";r.d(e,{F:()=>f});var n=r(34545),i=r(67111),o=r(11809),a=r(4828);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["x","y","top","left","width","height","className"];function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var f=function(t){var e=t.x,r=void 0===e?0:e,f=t.y,p=void 0===f?0:f,h=t.top,d=void 0===h?0:h,y=t.left,v=void 0===y?0:y,m=t.width,b=void 0===m?0:m,g=t.height,x=void 0===g?0:g,O=t.className,w=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:p,top:d,left:v,width:b,height:x},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,u));return(0,o.Et)(r)&&(0,o.Et)(p)&&(0,o.Et)(b)&&(0,o.Et)(x)&&(0,o.Et)(d)&&(0,o.Et)(v)?n.createElement("path",l({},(0,a.J9)(w,!0),{className:(0,i.A)("recharts-cross",O),d:"M".concat(r,",").concat(d,"v").concat(x,"M").concat(v,",").concat(p,"h").concat(b)})):null}},72816:(t,e,r)=>{"use strict";var n=r(8771);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},72995:(t,e,r)=>{"use strict";r.d(e,{t:()=>eC});var n=r(34545),i=r(22385),o=r.n(i),a=r(68194),c=r.n(a),u=r(35883),l=r.n(u),s=r(4358),f=r.n(s),p=r(63383),h=r.n(p),d=r(55662),y=r.n(d),v=r(67111),m=r(66864),b=r(96878),g=r(36068),x=r(96517),O=r(58090),w=r(29925),S=r(4828);function j(){return(j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var A=function(t){var e=t.cx,r=t.cy,i=t.r,o=t.className,a=(0,v.A)("recharts-dot",o);return e===+e&&r===+r&&i===+i?n.createElement("circle",j({},(0,S.J9)(t,!1),(0,w._U)(t),{className:a,cx:e,cy:r,r:i})):null},P=r(7970),E=r(47359),k=r(66812),M=r(60129),T=r(11809);function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e,r){var n;return(n=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var N=["Webkit","Moz","O","ms"],R=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=N.reduce(function(t,n){return D(D({},t),{},C({},n+r,e))},{});return n[t]=e,n};function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){K(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Q(n.key),n)}}function W(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(W=function(){return!!t})()}function q(t){return(q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function X(t,e){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function K(t,e,r){return(e=Q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Q(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}var H=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=(0,E.z)().domain(l()(0,c)).range([i,i+o-a]),s=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}},$=function(t){return t.changedTouches&&!!t.changedTouches.length},Y=function(t){var e,r;function i(t){var e,r,n;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r=i,n=[t],r=q(r),K(e=function(t,e){if(e&&("object"===B(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,W()?Reflect.construct(r,n||[],q(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),K(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),K(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),K(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),K(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),K(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),K(e,"handleSlideDragStart",function(t){var r=$(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&X(i,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=i.getIndexInRange(n,u),f=i.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=(0,M.kr)(r[t],i,t);return c()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=$(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(K(K({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(K({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:i,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.data,c=t.children,u=t.padding,l=n.Children.only(c);return l?n.cloneElement(l,{x:e,y:r,width:i,height:o,margin:u,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=z(z({},(0,S.J9)(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=h[y])||void 0===o?void 0:o.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},i.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,i=r.y,o=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:i,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,i=t.y,o=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(k.E,L({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:i+o/2},f),this.getTextOfTick(e)),n.createElement(k.E,L({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:i+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,i=t.children,o=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,T.Et)(o)||!(0,T.Et)(a)||!(0,T.Et)(c)||!(0,T.Et)(u)||c<=0||u<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(i),O=R("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,i=t.width,o=t.height,a=t.stroke,c=Math.floor(r+o/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:i,height:o,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+i-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+i-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):i.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return z({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?H({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&F(i.prototype,e),r&&F(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);K(Y,"displayName","Brush"),K(Y,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var G=r(85477),J=r(4293),V=r(41091),Z=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},tt=r(91536),te=r(17435);function tr(){return(tr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tn(t){return(tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ti(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function to(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ti(Object(r),!0).forEach(function(e){tl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ti(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ta(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ta=function(){return!!t})()}function tc(t){return(tc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tu(t,e){return(tu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tl(t,e,r){return(e=ts(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ts(t){var e=function(t,e){if("object"!=tn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tn(e)?e:e+""}var tf=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=(0,tt.P2)({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return Z(t,"discard")&&!o.isInRange(a)?null:a},tp=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tc(t),function(t,e){if(e&&("object"===tn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ta()?Reflect.construct(t,e||[],tc(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tu(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,i=t.y,o=t.r,a=t.alwaysShow,c=t.clipPathId,u=(0,T.vh)(e),l=(0,T.vh)(i);if((0,te.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=tf(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,m=to(to({clipPath:Z(this.props,"hidden")?"url(#".concat(c,")"):void 0},(0,S.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},r.renderDot(d,m),V.J.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ts(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);tl(tp,"displayName","ReferenceDot"),tl(tp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tl(tp,"renderDot",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(A,tr({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var th=r(61074),td=r.n(th),ty=r(56671);function tv(t){return(tv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tm=function(){return!!t})()}function tb(t){return(tb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tg(t,e){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tx(Object(r),!0).forEach(function(e){tw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tw(t,e,r){return(e=tS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tS(t){var e=function(t,e){if("object"!=tv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tv(e)?e:e+""}function tj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tA(){return(tA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tP=function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement("line",tA({},e,{className:"recharts-reference-line-line"}))},tE=function(t,e,r,n,i,o,a,c,u){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=u.y,d=t.y.apply(h,{position:o});if(Z(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(Z(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return Z(u,"discard")&&td()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tk(t){var e=t.x,r=t.y,i=t.segment,o=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,ty.Yp)(),f=(0,ty.AF)(o),p=(0,ty.Nk)(a),h=(0,ty.sk)();if(!s||!h)return null;(0,te.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=tE((0,tt.P2)({x:f.scale,y:p.scale}),(0,T.vh)(e),(0,T.vh)(r),i&&2===i.length,h,t.position,f.orientation,p.orientation,t);if(!d)return null;var y=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tj(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tj(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=y[0],b=m.x,x=m.y,O=y[1],w=O.x,j=O.y,A=tO(tO({clipPath:Z(t,"hidden")?"url(#".concat(s,")"):void 0},(0,S.J9)(t,!0)),{},{x1:b,y1:x,x2:w,y2:j});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",u)},tP(c,A),V.J.renderCallByParent(t,(0,tt.vh)({x1:b,y1:x,x2:w,y2:j})))}var tM=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tb(t),function(t,e){if(e&&("object"===tv(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tm()?Reflect.construct(t,e||[],tb(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tg(r,t),e=[{key:"render",value:function(){return n.createElement(tk,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tS(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tT(){return(tT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t_(t){return(t_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tI(Object(r),!0).forEach(function(e){tB(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}tw(tM,"displayName","ReferenceLine"),tw(tM,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function tC(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tC=function(){return!!t})()}function tN(t){return(tN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tR(t,e){return(tR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tB(t,e,r){return(e=tL(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tL(t){var e=function(t,e){if("object"!=t_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t_(e)?e:e+""}var tU=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=(0,tt.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!Z(i,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,tt.sl)(p,h):null},tz=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tN(t),function(t,e){if(e&&("object"===t_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tC()?Reflect.construct(t,e||[],tN(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tR(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,i=t.x2,o=t.y1,a=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;(0,te.R)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,T.vh)(e),f=(0,T.vh)(i),p=(0,T.vh)(o),h=(0,T.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tU(s,f,p,h,this.props);if(!y&&!d)return null;var m=Z(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",c)},r.renderRect(d,tD(tD({clipPath:m},(0,S.J9)(this.props,!0)),y)),V.J.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tL(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tF(t){return function(t){if(Array.isArray(t))return tW(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tW(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tW(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tW(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tB(tz,"displayName","ReferenceArea"),tB(tz,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tB(tz,"renderRect",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(P.M,tT({},e,{className:"recharts-reference-area-rect"}))});var tq=function(t,e,r,n,i){var o=(0,S.aS)(t,tM),a=(0,S.aS)(t,tp),c=[].concat(tF(o),tF(a)),u=(0,S.aS)(t,tz),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,T.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,T.Et)(e.props[p])&&(0,T.Et)(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return(0,T.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},tX=r(24097),tK=r(15080),tQ=r(5402),tH=new(r.n(tQ)()),t$="recharts.syncMouseEvents";function tY(t){return(tY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tG(t,e,r){return(e=tJ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tJ(t){var e=function(t,e){if("object"!=tY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tY(e)?e:e+""}var tV=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");tG(this,"activeIndex",0),tG(this,"coordinateList",[]),tG(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tJ(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),tZ=r(87746),t0=r(99281),t1=r(72639);function t2(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[(0,tX.IZ)(e,r,n,i),(0,tX.IZ)(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}var t5=r(3019);function t8(t){return(t8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t3(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=t8(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t8(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t4(t){var e,r,i,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(i=a.type.defaultProps)||void 0===i?void 0:i.cursor;if(!a||!m||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var b=t0.I;if("ScatterChart"===y)o=l,b=t1.F;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},b=P.M;else if("radial"===d){var g=t2(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=t5.h}else o={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return t2(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,tX.IZ)(c,u,l,f),h=(0,tX.IZ)(c,u,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},b=t0.I;var j=t6(t6(t6(t6({stroke:"#ccc",pointerEvents:"none"},f),o),(0,S.J9)(m,!1)),{},{payload:s,payloadIndex:p,className:(0,v.A)("recharts-tooltip-cursor",m.className)});return(0,n.isValidElement)(m)?(0,n.cloneElement)(m,j):(0,n.createElement)(b,j)}var t9=["item"],t7=["children","className","width","height","style","compact","title","desc"];function et(t){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ee(){return(ee=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function er(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||eu(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function en(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function ei(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ei=function(){return!!t})()}function eo(t){return(eo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ea(t,e){return(ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ec(t){return function(t){if(Array.isArray(t))return el(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||eu(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eu(t,e){if(t){if("string"==typeof t)return el(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return el(t,e)}}function el(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function es(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ef(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?es(Object(r),!0).forEach(function(e){ep(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):es(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ep(t,e,r){return(e=eh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eh(t){var e=function(t,e){if("object"!=et(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=et(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==et(e)?e:e+""}var ed={xAxis:["bottom","top"],yAxis:["left","right"]},ey={width:"100%",height:"100%"},ev={x:0,y:0};function em(t){return t}var eb=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return ef(ef(ef({},n),(0,tX.IZ)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return ef(ef(ef({},n),(0,tX.IZ)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return ev},eg=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(ec(t),ec(r)):t},[]);return o.length>0?o:t&&t.length&&(0,T.Et)(n)&&(0,T.Et)(i)?t.slice(n,i+1):[]};function ex(t){return"number"===t?[0,"auto"]:void 0}var eO=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=eg(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),o.dataKey&&!o.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,T.eP)(f,o.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(ec(i),[(0,M.zb)(c,l)]):i},[])},ew=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,M.gH)(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=eO(t,e,l,s),p=eb(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eS=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,a=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,M._L)(f,i);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?ef(ef({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,S=y[a];if(e[S])return e;var j=eg(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a])===S}),dataStartIndex:u,dataEndIndex:s}),A=j.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&(0,T.Et)(n)&&(0,T.Et)(i))return!0}return!1})(y.domain,b,v)&&(k=(0,M.AQ)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(I=(0,M.Ay)(j,m,"category")));var P=ex(v);if(!k||0===k.length){var E,k,_,I,D,C=null!==(D=y.domain)&&void 0!==D?D:P;if(m){if(k=(0,M.Ay)(j,m,v),"category"===v&&d){var N=(0,T.CG)(k);g&&N?(_=k,k=l()(0,A)):g||(k=(0,M.KC)(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(ec(t),[e])},[]))}else if("category"===v)k=g?k.filter(function(t){return""!==t&&!o()(t)}):(0,M.KC)(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||o()(e)?t:[].concat(ec(t),[e])},[]);else if("number"===v){var R=(0,M.A1)(j,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a],i="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===S&&(w||!i)}),m,i,f);R&&(k=R)}d&&("number"===v||"auto"!==x)&&(I=(0,M.Ay)(j,m,"category"))}else k=d?l()(0,A):c&&c[S]&&c[S].hasStack&&"number"===v?"expand"===h?[0,1]:(0,M.Mk)(c[S].stackGroups,u,s):(0,M.vf)(j,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===S&&(w||!r)}),v,f,!0);"number"===v?(k=tq(p,k,S,i,O),C&&(k=(0,M.AQ)(C,k,b))):"category"===v&&C&&k.every(function(t){return C.indexOf(t)>=0})&&(k=C)}return ef(ef({},e),{},ep({},S,ef(ef({},y),{},{axisType:i,domain:k,categoricalDomain:I,duplicateDomain:_,originalDomain:null!==(E=y.domain)&&void 0!==E?E:P,isCategorical:d,layout:f})))},{})},ej=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,p=t.children,h=eg(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),d=h.length,y=(0,M._L)(s,i),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?ef(ef({},e.type.defaultProps),e.props):e.props)[o],g=ex("number");return t[b]?t:(v++,m=y?l()(0,d):a&&a[b]&&a[b].hasStack?tq(p,m=(0,M.Mk)(a[b].stackGroups,c,u),b,i):tq(p,m=(0,M.AQ)(g,(0,M.vf)(h,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[o],i="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!i}),"number",s),n.defaultProps.allowDataOverflow),b,i),ef(ef({},t),{},ep({},b,ef(ef({axisType:i},n.defaultProps),{},{hide:!0,orientation:f()(ed,"".concat(i,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})},eA=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,S.aS)(l,i),p={};return f&&f.length?p=eS(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=ej(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},eP=function(t){var e=(0,T.lX)(t),r=(0,M.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:h()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,M.Hj)(e,r)}},eE=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,S.BU)(e,Y),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},ek=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eM=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,p=r.margin||{},h=(0,S.BU)(s,Y),d=(0,S.BU)(s,O.s),y=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:ef(ef({},t),{},ep({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:ef(ef({},t),{},ep({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=ef(ef({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||Y.defaultProps.height),d&&e&&(m=(0,M.s0)(m,n,r,e));var g=u-m.left-m.right,x=l-m.top-m.bottom;return ef(ef({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eT=r(88273),e_=r(14689),eI=r(88726),eD=r(10219),eC=function(t){var e=t.chartName,r=t.GraphicalChild,i=t.defaultTooltipEventType,a=void 0===i?"axis":i,u=t.validateTooltipEventTypes,l=void 0===u?["axis"]:u,s=t.axisComponents,p=t.legendContent,h=t.formatAxisMap,d=t.defaultProps,O=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,a=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=ek(f),v=y.numericAxisName,b=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(t){var e=(0,S.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=eg(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),w=void 0!==r.type.defaultProps?ef(ef({},r.type.defaultProps),r.props):r.props,j=w.dataKey,A=w.maxBarSize,P=w["".concat(v,"Id")],E=w["".concat(b,"Id")],k=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=w["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||(0,m.A)(!1);var o=n[i];return ef(ef({},t),{},ep(ep({},r.axisType,o),"".concat(r.axisType,"Ticks"),(0,M.Rh)(o)))},{}),T=k[b],_=k["".concat(b,"Ticks")],I=n&&n[P]&&n[P].hasStack&&(0,M.kA)(r,n[P].stackGroups),D=(0,S.Mn)(r.type).indexOf("Bar")>=0,C=(0,M.Hj)(T,_),N=[],R=g&&(0,M.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===b?k[b].width:"yAxis"===b?k[b].height:void 0});if(D){var B,L,U=o()(A)?d:A,z=null!==(B=null!==(L=(0,M.Hj)(T,_,!0))&&void 0!==L?L:U)&&void 0!==B?B:0;N=(0,M.BX)({barGap:p,barCategoryGap:h,bandSize:z!==C?z:C,sizeList:R[E],maxBarSize:U}),z!==C&&(N=N.map(function(t){return ef(ef({},t),{},{position:ef(ef({},t.position),{},{offset:t.position.offset-z/2})})}))}var F=r&&r.type&&r.type.getComposedData;F&&x.push({props:ef(ef({},F(ef(ef({},k),{},{displayedData:O,props:t,dataKey:j,item:r,bandSize:C,barPosition:N,offset:i,stackedData:I,layout:f,dataStartIndex:c,dataEndIndex:u}))),{},ep(ep(ep({key:r.key||"item-".concat(y)},v,k[v]),b,k[b]),"animationId",a)),childIndex:(0,S.AW)(r,t.children),item:r})}),x},j=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,S.Me)({props:i}))return null;var u=i.children,l=i.layout,f=i.stackOffset,p=i.data,d=i.reverseStackOrder,y=ek(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,S.aS)(u,r),g=(0,M.Mn)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),f,d),x=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ef(ef({},t),{},ep({},r,eA(i,ef(ef({},e),{},{graphicalItems:b,stackGroups:e.axisType===v&&g,dataStartIndex:o,dataEndIndex:a}))))},{}),w=eM(ef(ef({},x),{},{props:i,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=h(i,x[t],w,t.replace("Map",""),e)});var j=eP(x["".concat(m,"Map")]),A=O(i,ef(ef({},x),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:b,stackGroups:g,offset:w}));return ef(ef({formattedGraphicalItems:A,graphicalItems:b,offset:w,stackGroups:g},j),x)},E=function(t){var r;function i(t){var r,a,u,l,s;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return l=i,s=[t],l=eo(l),ep(u=function(t,e){if(e&&("object"===et(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ei()?Reflect.construct(l,s||[],eo(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ep(u,"accessibilityManager",new tV),ep(u,"handleLegendBBoxUpdate",function(t){if(t){var e=u.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;u.setState(ef({legendBBox:t},j({props:u.props,dataStartIndex:r,dataEndIndex:n,updateId:i},ef(ef({},u.state),{},{legendBBox:t}))))}}),ep(u,"handleReceiveSyncEvent",function(t,e,r){u.props.syncId===t&&(r!==u.eventEmitterSymbol||"function"==typeof u.props.syncMethod)&&u.applySyncEvent(e)}),ep(u,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==u.state.dataStartIndex||r!==u.state.dataEndIndex){var n=u.state.updateId;u.setState(function(){return ef({dataStartIndex:e,dataEndIndex:r},j({props:u.props,dataStartIndex:e,dataEndIndex:r,updateId:n},u.state))}),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ep(u,"handleMouseEnter",function(t){var e=u.getMouseInfo(t);if(e){var r=ef(ef({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseEnter;c()(n)&&n(r,t)}}),ep(u,"triggeredAfterMouseMove",function(t){var e=u.getMouseInfo(t),r=e?ef(ef({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseMove;c()(n)&&n(r,t)}),ep(u,"handleItemMouseEnter",function(t){u.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ep(u,"handleItemMouseLeave",function(){u.setState(function(){return{isTooltipActive:!1}})}),ep(u,"handleMouseMove",function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)}),ep(u,"handleMouseLeave",function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var r=u.props.onMouseLeave;c()(r)&&r(e,t)}),ep(u,"handleOuterEvent",function(t){var e,r,n=(0,S.X_)(t),i=f()(u.props,"".concat(n));n&&c()(i)&&i(null!==(e=/.*touch.*/i.test(n)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))&&void 0!==e?e:{},t)}),ep(u,"handleClick",function(t){var e=u.getMouseInfo(t);if(e){var r=ef(ef({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onClick;c()(n)&&n(r,t)}}),ep(u,"handleMouseDown",function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)}),ep(u,"handleMouseUp",function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)}),ep(u,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ep(u,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])}),ep(u,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])}),ep(u,"handleDoubleClick",function(t){var e=u.props.onDoubleClick;c()(e)&&e(u.getMouseInfo(t),t)}),ep(u,"handleContextMenu",function(t){var e=u.props.onContextMenu;c()(e)&&e(u.getMouseInfo(t),t)}),ep(u,"triggerSyncEvent",function(t){void 0!==u.props.syncId&&tH.emit(t$,u.props.syncId,t,u.eventEmitterSymbol)}),ep(u,"applySyncEvent",function(t){var e=u.props,r=e.layout,n=e.syncMethod,i=u.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(ef({dataStartIndex:o,dataEndIndex:a},j({props:u.props,dataStartIndex:o,dataEndIndex:a,updateId:i},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ef(ef({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),m=Math.min(l,y.y+y.height),b=h[s]&&h[s].value,g=eO(u.state,u.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:ev;u.setState(ef(ef({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else u.setState(t)}),ep(u,"renderCursor",function(t){var r,i=u.state,o=i.isTooltipActive,a=i.activeCoordinate,c=i.activePayload,l=i.offset,s=i.activeTooltipIndex,f=i.tooltipAxisBandSize,p=u.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:o,d=u.props.layout,y=t.key||"_recharts-cursor";return n.createElement(t4,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),ep(u,"renderPolarAxis",function(t,e,r){var i=f()(t,"type.axisType"),o=f()(u.state,"".concat(i,"Map")),a=t.type.defaultProps,c=void 0!==a?ef(ef({},a),t.props):t.props,l=o&&o[c["".concat(i,"Id")]];return(0,n.cloneElement)(t,ef(ef({},l),{},{className:(0,v.A)(i,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,M.Rh)(l,!0)}))}),ep(u,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,i=e.polarAngles,o=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,T.lX)(c),f=(0,T.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(i)?i:(0,M.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:(0,M.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ep(u,"renderLegend",function(){var t=u.state.formattedGraphicalItems,e=u.props,r=e.children,i=e.width,o=e.height,a=u.props.margin||{},c=i-(a.left||0)-(a.right||0),l=(0,J.g)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:p});if(!l)return null;var s=l.item,f=en(l,t9);return(0,n.cloneElement)(s,ef(ef({},f),{},{chartWidth:i,chartHeight:o,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))}),ep(u,"renderTooltip",function(){var t,e=u.props,r=e.children,i=e.accessibilityLayer,o=(0,S.BU)(r,x.m);if(!o)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=o.props.active)&&void 0!==t?t:c;return(0,n.cloneElement)(o,{viewBox:ef(ef({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:i})}),ep(u,"renderBrush",function(t){var e=u.props,r=e.margin,i=e.data,o=u.state,a=o.offset,c=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,M.HQ)(u.handleBrushChange,t.props.onChange),data:i,x:(0,T.Et)(t.props.x)?t.props.x:a.left,y:(0,T.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,T.Et)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})}),ep(u,"renderReferenceElement",function(t,e,r){if(!t)return null;var i=u.clipPathId,o=u.state,a=o.xAxisMap,c=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:c[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})}),ep(u,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?ef(ef({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ef(ef({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,M.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,S.J9)(s,!1)),(0,w._U)(s));return c.push(i.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(o))),n?c.push(i.renderActiveDot(s,ef(ef({},f),{},{cx:n.x,cy:n.y}),"".concat(u,"-basePoint-").concat(o))):a&&c.push(null),c}),ep(u,"renderGraphicChild",function(t,e,r){var i=u.filterFormatItem(t,e,r);if(!i)return null;var a=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=u.props.children,d=(0,S.BU)(h,x.m),y=i.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==i.item.type.defaultProps?ef(ef({},i.item.type.defaultProps),i.item.props):i.item.props,O=g.activeDot,w=g.hide,j=g.activeBar,A=g.activeShape,P=!!(!w&&l&&d&&(O||j||A)),E={};"axis"!==a&&d&&"click"===d.props.trigger?E={onClick:(0,M.HQ)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(E={onMouseLeave:(0,M.HQ)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,M.HQ)(u.handleItemMouseEnter,t.props.onMouseEnter)});var k=(0,n.cloneElement)(t,ef(ef({},i.props),E));if(P)if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());D=(0,T.eP)(v,_,p),C=m&&b&&(0,T.eP)(b,_,p)}else D=null==v?void 0:v[f],C=m&&b&&b[f];if(A||j){var I=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,ef(ef(ef({},i.props),E),{},{activeIndex:I})),null,null]}if(!o()(D))return[k].concat(ec(u.renderActivePoints({item:i,activePoint:D,basePoint:C,childIndex:f,isRange:m})))}else{var D,C,N,R=(null!==(N=u.getItemByXY(u.state.activeCoordinate))&&void 0!==N?N:{graphicalItem:k}).graphicalItem,B=R.item,L=void 0===B?t:B,U=R.childIndex,z=ef(ef(ef({},i.props),E),{},{activeIndex:U});return[(0,n.cloneElement)(L,z),null,null]}return m?[k,null,null]:[k,null]}),ep(u,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,ef(ef({key:"recharts-customized-".concat(r)},u.props),u.state))}),ep(u,"renderMap",{CartesianGrid:{handler:em,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:em},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:em},YAxis:{handler:em},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:(0,T.NF)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=y()(u.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),u.state={},u}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&ea(i,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=(0,S.BU)(e,x.m);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=eO(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ef(ef({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,S.OV)([(0,S.BU)(t.children,x.m)],[(0,S.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,S.BU)(this.props.children,x.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,G.A3)(r),i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=ew(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=(0,T.lX)(u).scale,h=(0,T.lX)(l).scale,d=p&&p.invert?p.invert(i.chartX):null,y=h&&h.invert?h.invert(i.chartY):null;return ef(ef({},i),{},{xValue:d,yValue:y},f)}return f?ef(ef({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=(0,T.lX)(u);return(0,tX.yy)({x:i,y:o},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,S.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ef(ef({},(0,w._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tH.on(t$,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tH.removeListener(t$,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===(0,S.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,i=e.top,o=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:i,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=er(e,2),n=r[0],i=r[1];return ef(ef({},t),{},ep({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=er(e,2),n=r[0],i=r[1];return ef(ef({},t),{},ep({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?ef(ef({},u.type.defaultProps),u.props):u.props,s=(0,S.Mn)(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return(0,P.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return(0,tX.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,tZ.NE)(a,n)||(0,tZ.nZ)(a,n)||(0,tZ.xQ)(a,n)){var h=(0,tZ.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ef(ef({},a),{},{childIndex:d}),payload:(0,tZ.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,S.Me)(this))return null;var i=this.props,o=i.children,a=i.className,c=i.width,u=i.height,l=i.style,s=i.compact,f=i.title,p=i.desc,h=en(i,t7),d=(0,S.J9)(h,!1);if(s)return n.createElement(ty.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,ee({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,S.ee)(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(ty.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",ee({className:(0,v.A)("recharts-wrapper",a),style:ef({position:"relative",cursor:"default",width:c,height:u},l)},y,{ref:function(t){r.container=t}}),n.createElement(b.u,ee({},d,{width:c,height:u,title:f,desc:p,style:ey}),this.renderClipPath(),(0,S.ee)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eh(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);ep(E,"displayName",e),ep(E,"defaultProps",ef({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),ep(E,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,a=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=eE(t);return ef(ef(ef({},h),{},{updateId:0},j(ef(ef({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,tK.b)(s,e.prevMargin)){var d=eE(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ef(ef({},ew(e,n,u)),{},{updateId:e.updateId+1}),m=ef(ef(ef({},d),y),v);return ef(ef(ef({},m),j(ef({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(!(0,S.OV)(i,e.prevChildren)){var b,g,x,O,w=(0,S.BU)(i,Y),A=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,P=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:p,E=o()(n)||A!==f||P!==p?e.updateId+1:e.updateId;return ef(ef({updateId:E},j(ef(ef({props:t},e),{},{updateId:E,dataStartIndex:A,dataEndIndex:P}),e)),{},{prevChildren:i,dataStartIndex:A,dataEndIndex:P})}return null}),ep(E,"renderActiveDot",function(t,e,r){var i;return i=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):n.createElement(A,e),n.createElement(g.W,{className:"recharts-active-dot",key:r},i)});var k=(0,n.forwardRef)(function(t,e){return n.createElement(E,ee({},t,{ref:e}))});return k.displayName=E.displayName,k}({chartName:"ScatterChart",GraphicalChild:eT.X,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:e_.W},{axisType:"yAxis",AxisComp:eI.h},{axisType:"zAxis",AxisComp:eD.K}],formatAxisMap:tt.pr})},78105:(t,e,r)=>{var n=r(81732),i=r(41460);t.exports=function(t,e){return n(i(t,e),1)}},80598:function(t,e,r){var n;!function(i){"use strict";var o,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},c=!0,u="[DecimalError] ",l=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var r,n,i,o,a,u,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),c?P(e,p):e;if(l=t.d,s=e.d,a=t.e,i=e.e,l=l.slice(),o=a-i){for(o<0?(n=l,o=-o,u=s.length):(n=s,i=a,u=l.length),o>(u=(a=Math.ceil(p/7))>u?a+1:u+1)&&(o=u,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((u=l.length)-(o=s.length)<0&&(o=u,n=s,s=l,l=n),r=0;o;)r=(l[--o]=l[o]+s[o]+r)/1e7|0,l[o]%=1e7;for(r&&(l.unshift(r),++i),u=l.length;0==l[--u];)l.pop();return e.d=l,e.e=i,c?P(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function b(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=S(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=S(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return P(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(o))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(o)?new r(0):(c=!1,e=g(j(this,i),j(t,i),i),c=!0,P(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(u+"NaN");return this.s?(c=!1,e=g(this,t,0,1).times(t),c=!0,this.minus(e)):P(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,i,o,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(u+"NaN")}for(t=O(this),c=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(i.toString()),i=a=(r=l.precision)+3;;)if(n=(o=n).plus(g(this,o,a+2)).times(.5),b(o.d).slice(0,a)===(e=b(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(P(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return c=!0,P(n,r)},y.times=y.mul=function(t){var e,r,n,i,o,a,u,l,s,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=p.length)<(s=h.length)&&(o=p,p=h,h=o,a=l,l=s,s=a),o=[],n=a=l+s;n--;)o.push(0);for(n=s;--n>=0;){for(e=0,i=l+n;i>n;)u=o[i]+h[n]*p[i-n-1]+e,o[i--]=u%1e7|0,e=u/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,c?P(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),P(r,t+O(r)+1,e))},y.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=k(n,!0):(m(t,0,1e9),void 0===e?e=i.rounding:m(e,0,8),r=k(n=P(new i(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=i.rounding:m(e,0,8),r=k((n=P(new i(this),t+O(this)+1,e)).abs(),!1,t+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return P(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,i,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(o);if(!(s=new p(s)).s){if(t.s<1)throw Error(u+"Infinity");return s}if(s.eq(o))return s;if(n=p.precision,t.eq(o))return P(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new p(o),e=Math.ceil(n/7+4),c=!1;r%2&&M((i=i.times(s)).d,e),0!==(r=f(r/2));)M((s=s.times(s)).d,e);return c=!0,t.s<0?new p(o).div(i):P(i,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,c=!1,i=t.times(j(s,n+12)),c=!0,(i=x(i)).s=a,i},y.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=O(i),n=k(i,r<=o.toExpNeg||r>=o.toExpPos)):(m(t,1,1e9),void 0===e?e=o.rounding:m(e,0,8),r=O(i=P(new o(i),t,e)),n=k(i,t<=r||r<=o.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),P(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,l,s,f,p,h,d,y,v,m,b,g,x,w,S,j,A,E,k=n.constructor,M=n.s==i.s?1:-1,T=n.d,_=i.d;if(!n.s)return new k(n);if(!i.s)throw Error(u+"Division by zero");for(s=0,l=n.e-i.e,A=_.length,S=T.length,y=(d=new k(M)).d=[];_[s]==(T[s]||0);)++s;if(_[s]>(T[s]||0)&&--l,(g=null==o?o=k.precision:a?o+(O(n)-O(i))+1:o)<0)return new k(0);if(g=g/7+2|0,s=0,1==A)for(f=0,_=_[0],g++;(s<S||f)&&g--;s++)x=1e7*f+(T[s]||0),y[s]=x/_|0,f=x%_|0;else{for((f=1e7/(_[0]+1)|0)>1&&(_=t(_,f),T=t(T,f),A=_.length,S=T.length),w=A,m=(v=T.slice(0,A)).length;m<A;)v[m++]=0;(E=_.slice()).unshift(0),j=_[0],_[1]>=1e7/2&&++j;do f=0,(c=e(_,v,A,m))<0?(b=v[0],A!=m&&(b=1e7*b+(v[1]||0)),(f=b/j|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(_,f)).length,m=v.length,1==(c=e(p,v,h,m))&&(f--,r(p,A<h?E:_,h))):(0==f&&(c=f=1),p=_.slice()),(h=p.length)<m&&p.unshift(0),r(v,p,m),-1==c&&(m=v.length,(c=e(_,v,A,m))<1&&(f++,r(v,A<m?E:_,m))),m=v.length):0===c&&(f++,v=[0]),y[s++]=f,c&&v[0]?v[m++]=T[w]||0:(v=[T[w]],m=1);while((w++<S||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=l,P(d,a?o+O(d)+1:o)}}();function x(t,e){var r,n,i,a,u,l=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(s+O(t));if(!t.s)return new h(o);for(null==e?(c=!1,u=d):u=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(u+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=u;;){if(n=P(n.times(t),u),r=r.times(++l),b((a=i.plus(g(n,r,u))).d).slice(0,u)===b(i.d).slice(0,u)){for(;f--;)i=P(i.times(i),u);return h.precision=d,null==e?(c=!0,P(i,d)):i}i=a}}function O(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function w(t,e,r){if(e>t.LN10.sd())throw c=!0,r&&(t.precision=r),Error(u+"LN10 precision limit exceeded");return P(new t(t.LN10),e)}function S(t){for(var e="";t--;)e+="0";return e}function j(t,e){var r,n,i,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(c=!1,p=x):p=e,y.eq(10))return null==e&&(c=!0),w(m,p);if(m.precision=p+=10,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=j(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(c=!0,P(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=g(y.minus(o),y.plus(o),p),h=P(y.times(y),p),i=3;;){if(l=P(l.times(h),p),b((f=s.plus(g(l,new m(i),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(w(m,p+2,x).times(a+""))),s=g(s,new m(d),p),m.precision=x,null==e?(c=!0,P(s,x)):s;s=f,i+=2}}function A(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=f((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),c&&(t.e>d||t.e<-d))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function P(t,e,r){var n,i,o,a,u,l,h,y,v=t.d;for(a=1,o=v[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(o=v.length))return t;for(a=1,h=o=v[y];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(u=h/(o=p(10,a-i-1))%10|0,l=e<0||void 0!==v[y+1]||h%o,l=r<4?(u||l)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||l||6==r&&(n>0?i>0?h/p(10,a-i):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(o=O(t),v.length=1,e=e-o-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,o=1,y--):(v.length=y+1,o=p(10,7-n),v[y]=i>0?(h/p(10,a-i)%p(10,i)|0)*o:0),l)for(;;)if(0==y){1e7==(v[0]+=o)&&(v[0]=1,++t.e);break}else{if(v[y]+=o,1e7!=v[y])break;v[y--]=0,o=1}for(n=v.length;0===v[--n];)v.pop();if(c&&(t.e>d||t.e<-d))throw Error(s+O(t));return t}function E(t,e){var r,n,i,o,a,u,l,s,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),c?P(e,d):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,u=p.length):(r=p,n=s,u=l.length),a>(i=Math.max(Math.ceil(d/7),u)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=l.length)<(u=p.length))&&(u=i),i=0;i<u;i++)if(l[i]!=p[i]){f=l[i]<p[i];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),u=l.length,i=p.length-u;i>0;--i)l[u++]=0;for(i=p.length;i>a;){if(l[--i]<p[i]){for(o=i;o&&0===l[--o];)l[o]=1e7-1;--l[o],l[i]+=1e7}l[i]-=p[i]}for(;0===l[--u];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,c?P(e,d):e):new h(0)}function k(t,e,r){var n,i=O(t),o=b(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+S(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+S(-i-1)+o,r&&(n=r-a)>0&&(o+=S(n))):i>=a?(o+=S(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+S(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=S(n))),t.s<0?"-"+o:o}function M(t,e){if(t.length>e)return t.length=e,!0}function T(t){if(!t||"object"!=typeof t)throw Error(u+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(f(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(l+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n);return this}(a=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return A(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))A(this,t);else throw Error(l+t)}if(o.prototype=y,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=T,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}(a)).default=a.Decimal=a,o=new a(1),void 0!==(n=(function(){return a}).call(e,r,e,t))&&(t.exports=n)}(0)},80926:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},80997:t=>{t.exports=function(t,e){return t>e}},81444:(t,e,r)=>{t.exports=r(72816)()},84541:(t,e,r)=>{t.exports=r(88003)(Object.getPrototypeOf,Object)},85198:(t,e,r)=>{var n=r(30857),i=r(46186),o=r(37616);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},85477:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(99283);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,o,a;n=t,o=e,a=r[e],(o=function(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:i});if(u.widthCache[o])return u.widthCache[o];try{var c=document.getElementById(s);c||((c=document.createElement("span")).setAttribute("id",s),c.setAttribute("aria-hidden","true"),document.body.appendChild(c));var f=a(a({},l),i);Object.assign(c.style,f),c.textContent="".concat(t);var p=c.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[o]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},85885:(t,e,r)=>{t.exports=r(59504)(r(69415))},86675:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},87162:(t,e,r)=>{var n=r(53175);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},87746:(t,e,r)=>{"use strict";r.d(e,{yp:()=>I,GG:()=>U,NE:()=>D,nZ:()=>C,xQ:()=>N});var n=r(34545),i=r(68194),o=r.n(i),a=r(66437),c=r.n(a),u=r(49070),l=r.n(u),s=r(57042),f=r.n(s),p=r(7970),h=r(67111),d=r(4390),y=r(4828);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var O=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},S=function(t){var e,r=x(x({},w),t),i=(0,n.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=o[0],c=o[1];(0,n.useEffect)(function(){if(i.current&&i.current.getTotalLength)try{var t=i.current.getTotalLength();t&&c(t)}catch(t){}},[]);var u=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,S=r.animationDuration,j=r.animationBegin,A=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var P=(0,h.A)("recharts-trapezoid",v);return A?n.createElement(d.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:u,y:l},duration:S,animationEasing:g,isActive:A},function(t){var e=t.upperWidth,o=t.lowerWidth,c=t.height,u=t.x,l=t.y;return n.createElement(d.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:S,easing:g},n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(u,l,e,o,c),ref:i})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(u,l,s,f,p)})))},j=r(3019),A=r(36068),P=r(72566),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(S,r);case"sector":return n.createElement(j.h,r);case"symbols":if("symbols"===e)return n.createElement(P.i,r);break;default:return null}}function I(t){var e,r=t.option,i=t.shapeType,a=t.propTransformer,u=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,T(T({},f),(0,n.isValidElement)(r)?r.props:r));else if(o()(r))e=r(f);else if(c()(r)&&!l()(r)){var p=(void 0===a?function(t,e){return T(T({},e),t)}:a)(r,f);e=n.createElement(_,{shapeType:i,elementProps:p})}else e=n.createElement(_,{shapeType:i,elementProps:f});return s?n.createElement(A.W,{className:void 0===u?"recharts-active-shape":u},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function C(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function R(t,e){var r,n,i=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return i&&o}function B(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function L(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function U(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(D(o,i)?e="trapezoids":C(o,i)?e="sectors":N(o,i)&&(e="points"),e),u=D(o,i)?null===(r=i.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:C(o,i)?null===(n=i.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:N(o,i)?i.payload:{},l=a.filter(function(t,e){var r=f()(u,t),n=o.props[c].filter(function(t){var e;return(D(o,i)?e=R:C(o,i)?e=B:N(o,i)&&(e=L),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},88273:(t,e,r)=>{"use strict";r.d(e,{X:()=>L});var n=r(34545),i=r(4390),o=r(22385),a=r.n(o),c=r(57042),u=r.n(c),l=r(68194),s=r.n(l),f=r(67111),p=r(36068),h=r(41751),d=r(4828),y=r(99283),v=r(10219),m=r(99281),b=r(45843),g=r(90451),x=r(11809),O=r(60129),w=r(29925),S=r(72566),j=r(87746),A=["option","isActive"];function P(){return(P=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function E(t){var e=t.option,r=t.isActive,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,A);return"string"==typeof e?n.createElement(j.yp,P({option:n.createElement(S.i,P({type:e},i)),isActive:r,shapeType:"symbols"},i)):n.createElement(j.yp,P({option:e,isActive:r,shapeType:"symbols"},i))}function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D=function(){return!!t})()}function C(t){return(C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function N(t,e){return(N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function R(t,e,r){return(e=B(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}var L=function(t){var e,r;function o(){var t,e,r;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=C(e),R(t=function(t,e){if(e&&("object"===k(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,D()?Reflect.construct(e,r||[],C(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),R(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0})}),R(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1})}),R(t,"id",(0,x.NF)("recharts-scatter-")),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&N(o,t),e=[{key:"renderSymbolsStatically",value:function(t){var e=this,r=this.props,i=r.shape,o=r.activeShape,a=r.activeIndex,c=(0,d.J9)(this.props,!1);return t.map(function(t,r){var u=a===r,l=_(_({},c),t);return n.createElement(p.W,M({className:"recharts-scatter-symbol",key:"symbol-".concat(null==t?void 0:t.cx,"-").concat(null==t?void 0:t.cy,"-").concat(null==t?void 0:t.size,"-").concat(r)},(0,w.XC)(e.props,t,r),{role:"img"}),n.createElement(E,M({option:u?o:i,isActive:u,key:"symbol-".concat(r)},l)))})}},{key:"renderSymbolsWithAnimation",value:function(){var t=this,e=this.props,r=e.points,o=e.isAnimationActive,a=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevPoints;return n.createElement(i.Ay,{begin:a,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"pie-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=s&&s[e];if(r){var n=(0,x.Dj)(r.cx,t.cx),o=(0,x.Dj)(r.cy,t.cy),a=(0,x.Dj)(r.size,t.size);return _(_({},t),{},{cx:n(i),cy:o(i),size:a(i)})}var c=(0,x.Dj)(0,t.size);return _(_({},t),{},{size:c(i)})});return n.createElement(p.W,null,t.renderSymbolsStatically(o))})}},{key:"renderSymbols",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=this.state.prevPoints;return r&&e&&e.length&&(!n||!u()(n,e))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(e)}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,e=t.points,r=t.xAxis,i=t.yAxis,o=t.children,a=(0,d.aS)(o,b.u);return a?a.map(function(t,o){var a=t.props,c=a.direction,u=a.dataKey;return n.cloneElement(t,{key:"".concat(c,"-").concat(u,"-").concat(e[o]),data:e,xAxis:r,yAxis:i,layout:"x"===c?"vertical":"horizontal",dataPointFormatter:function(t,e){return{x:t.cx,y:t.cy,value:"x"===c?+t.node.x:+t.node.y,errorVal:(0,O.kr)(t,e)}}})}):null}},{key:"renderLine",value:function(){var t,e,r=this.props,i=r.points,o=r.line,a=r.lineType,c=r.lineJointType,u=(0,d.J9)(this.props,!1),l=(0,d.J9)(o,!1);if("joint"===a)t=i.map(function(t){return{x:t.cx,y:t.cy}});else if("fitting"===a){var f=(0,x.jG)(i),h=f.xmin,y=f.xmax,v=f.a,b=f.b,g=function(t){return v*t+b};t=[{x:h,y:g(h)},{x:y,y:g(y)}]}var O=_(_(_({},u),{},{fill:"none",stroke:u&&u.fill},l),{},{points:t});return e=n.isValidElement(o)?n.cloneElement(o,O):s()(o)?o(O):n.createElement(m.I,M({},O,{type:c})),n.createElement(p.W,{className:"recharts-scatter-line",key:"recharts-scatter-line"},e)}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.points,i=t.line,o=t.className,c=t.xAxis,u=t.yAxis,l=t.left,s=t.top,d=t.width,y=t.height,v=t.id,m=t.isAnimationActive;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,f.A)("recharts-scatter",o),x=c&&c.allowDataOverflow,O=u&&u.allowDataOverflow,w=a()(v)?this.id:v;return n.createElement(p.W,{className:g,clipPath:x||O?"url(#clipPath-".concat(w,")"):null},x||O?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(w)},n.createElement("rect",{x:x?l:l-d/2,y:O?s:s-y/2,width:x?d:2*d,height:O?y:2*y}))):null,i&&this.renderLine(),this.renderErrorBar(),n.createElement(p.W,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!m||b)&&h.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}}],e&&I(o.prototype,e),r&&I(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);R(L,"displayName","Scatter"),R(L,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!y.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),R(L,"getComposedData",function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,i=t.item,o=t.displayedData,c=t.xAxisTicks,u=t.yAxisTicks,l=t.offset,s=i.props.tooltipType,f=(0,d.aS)(i.props.children,g.f),p=a()(e.dataKey)?i.props.dataKey:e.dataKey,h=a()(r.dataKey)?i.props.dataKey:r.dataKey,y=n&&n.dataKey,m=n?n.range:v.K.defaultProps.range,b=m&&m[0],x=e.scale.bandwidth?e.scale.bandwidth():0,w=r.scale.bandwidth?r.scale.bandwidth():0,S=o.map(function(t,o){var l=(0,O.kr)(t,p),d=(0,O.kr)(t,h),v=!a()(y)&&(0,O.kr)(t,y)||"-",m=[{name:a()(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:l,payload:t,dataKey:p,type:s},{name:a()(r.dataKey)?i.props.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:t,dataKey:h,type:s}];"-"!==v&&m.push({name:n.name||n.dataKey,unit:n.unit||"",value:v,payload:t,dataKey:y,type:s});var g=(0,O.nb)({axis:e,ticks:c,bandSize:x,entry:t,index:o,dataKey:p}),S=(0,O.nb)({axis:r,ticks:u,bandSize:w,entry:t,index:o,dataKey:h}),j="-"!==v?n.scale(v):b,A=Math.sqrt(Math.max(j,0)/Math.PI);return _(_({},t),{},{cx:g,cy:S,x:g-A,y:S-A,xAxis:e,yAxis:r,zAxis:n,width:2*A,height:2*A,size:j,node:{x:l,y:d,z:v},tooltipPayload:m,tooltipPosition:{x:g,y:S},payload:t},f&&f[o]&&f[o].props)});return _({points:S},l)})},88726:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(34545),i=r(67111),o=r(56671),a=r(35785),c=r(60129);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(l=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function p(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var y=function(t){var e=t.yAxisId,r=(0,o.yi)(),u=(0,o.rY)(),l=(0,o.Nk)(e);return null==l?null:n.createElement(a.u,d({},l,{className:(0,i.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))},v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","YAxis"),p(v,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},90451:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=function(t){return null};n.displayName="Cell"},91536:(t,e,r)=>{"use strict";r.d(e,{P2:()=>te,pr:()=>J,bx:()=>tr,vh:()=>Z,sl:()=>V});var n=r(9830),i=r.n(n),o=r(68221),a=r.n(o),c=r(60129),u=r(4828),l=r(11809),s=r(34545),f=r(67111),p=r(4390),h=r(57042),d=r.n(h),y=r(22385),v=r.n(y),m=r(36068),b=r(45843),g=r(90451),x=r(41751),O=r(99283),w=r(29925),S=r(66864),j=r(87746),A=["x","y"];function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,A),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return M(M(M(M(M({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function _(t){return s.createElement(j.yp,E({shapeType:"rectangle",propTransformer:T,activeClassName:"recharts-active-bar"},t))}var I=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i=(0,l.Et)(r)||(0,l.uy)(r);return i?t(r,n):(i||(0,S.A)(!1),e)}},D=["value","background"];function C(t){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(){return(N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function U(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(U=function(){return!!t})()}function z(t){return(z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function F(t,e){return(F=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function W(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}var X=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=z(e),W(t=function(t,e){if(e&&("object"===C(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,U()?Reflect.construct(e,r||[],z(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),W(t,"id",(0,l.NF)("recharts-bar-")),W(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),W(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&F(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,a=r.activeBar,c=(0,u.J9)(this.props,!1);return t&&t.map(function(t,r){var u=r===o,l=B(B(B({},c),t),{},{isActive:u,option:u?a:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return s.createElement(m.W,N({className:"recharts-bar-rectangle"},(0,w.XC)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),s.createElement(_,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,f=this.state.prevData;return s.createElement(p.Ay,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=f&&f[e];if(r){var o=(0,l.Dj)(r.x,t.x),a=(0,l.Dj)(r.y,t.y),c=(0,l.Dj)(r.width,t.width),u=(0,l.Dj)(r.height,t.height);return B(B({},t),{},{x:o(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===n){var s=(0,l.Dj)(0,t.height)(i);return B(B({},t),{},{y:t.y+t.height-s,height:s})}var p=(0,l.Dj)(0,t.width)(i);return B(B({},t),{},{width:p})});return s.createElement(m.W,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!d()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=(0,u.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,c=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,D);if(!a)return null;var u=B(B(B(B(B({},c),{},{fill:"#eee"},a),o),(0,w.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return s.createElement(_,N({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,a=r.layout,l=r.children,f=(0,u.aS)(l,b.u);if(!f)return null;var p="vertical"===a?n[0].height/2:n[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,c.kr)(t,e)}};return s.createElement(m.W,{clipPath:t?"url(#clipPath-".concat(e,")"):null},f.map(function(t){return s.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:a,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,a=t.left,c=t.top,u=t.width,l=t.height,p=t.isAnimationActive,h=t.background,d=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,b=(0,f.A)("recharts-bar",n),g=i&&i.allowDataOverflow,O=o&&o.allowDataOverflow,w=g||O,S=v()(d)?this.id:d;return s.createElement(m.W,{className:b},g||O?s.createElement("defs",null,s.createElement("clipPath",{id:"clipPath-".concat(S)},s.createElement("rect",{x:g?a:a-u/2,y:O?c:c-l/2,width:g?u:2*u,height:O?l:2*l}))):null,s.createElement(m.W,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(S,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,S),(!p||y)&&x.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&L(n.prototype,e),r&&L(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(s.PureComponent);function K(t){return(K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Q(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,G(n.key),n)}}function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach(function(e){Y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Y(t,e,r){return(e=G(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G(t){var e=function(t,e){if("object"!=K(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=K(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==K(e)?e:e+""}W(X,"displayName","Bar"),W(X,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!O.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),W(X,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,s=t.xAxisTicks,f=t.yAxisTicks,p=t.stackedData,h=t.dataStartIndex,d=t.displayedData,y=t.offset,v=(0,c.xi)(n,r);if(!v)return null;var m=e.layout,b=r.type.defaultProps,x=void 0!==b?B(B({},b),r.props):r.props,O=x.dataKey,w=x.children,S=x.minPointSize,j="horizontal"===m?a:o,A=p?j.scale.domain():null,P=(0,c.DW)({numericAxis:j}),E=(0,u.aS)(w,g.f),k=d.map(function(t,e){p?u=(0,c._f)(p[h+e],A):Array.isArray(u=(0,c.kr)(t,O))||(u=[P,u]);var n=I(S,X.defaultProps.minPointSize)(u[1],e);if("horizontal"===m){var u,d,y,b,g,x,w,j=[a.scale(u[0]),a.scale(u[1])],k=j[0],M=j[1];d=(0,c.y2)({axis:o,ticks:s,bandSize:i,offset:v.offset,entry:t,index:e}),y=null!==(w=null!=M?M:k)&&void 0!==w?w:void 0,b=v.size;var T=k-M;if(g=Number.isNaN(T)?0:T,x={x:d,y:a.y,width:b,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var _=(0,l.sA)(g||n)*(Math.abs(n)-Math.abs(g));y-=_,g+=_}}else{var D=[o.scale(u[0]),o.scale(u[1])],C=D[0],N=D[1];if(d=C,y=(0,c.y2)({axis:a,ticks:f,bandSize:i,offset:v.offset,entry:t,index:e}),b=N-C,g=v.size,x={x:o.x,y:y,width:o.width,height:g},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var R=(0,l.sA)(b||n)*(Math.abs(n)-Math.abs(b));b+=R}}return B(B(B({},t),{},{x:d,y:y,width:b,height:g,value:p?u:u[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,c.zb)(r,t)],tooltipPosition:{x:d+b/2,y:y+g/2}})});return B({data:k,layout:m},y)});var J=function(t,e,r,n,i){var o=t.width,a=t.height,s=t.layout,f=t.children,p=Object.keys(e),h={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},d=!!(0,u.BU)(f,X);return p.reduce(function(o,a){var u,f,p,y,v,m=e[a],b=m.orientation,g=m.domain,x=m.padding,O=void 0===x?{}:x,w=m.mirror,S=m.reversed,j="".concat(b).concat(w?"Mirror":"");if("number"===m.type&&("gap"===m.padding||"no-gap"===m.padding)){var A=g[1]-g[0],P=1/0,E=m.categoricalDomain.sort(l.ck);if(E.forEach(function(t,e){e>0&&(P=Math.min((t||0)-(E[e-1]||0),P))}),Number.isFinite(P)){var k=P/A,M="vertical"===m.layout?r.height:r.width;if("gap"===m.padding&&(u=k*M/2),"no-gap"===m.padding){var T=(0,l.F4)(t.barCategoryGap,k*M),_=k*M/2;u=_-T-(_-T)/M*T}}}f="xAxis"===n?[r.left+(O.left||0)+(u||0),r.left+r.width-(O.right||0)-(u||0)]:"yAxis"===n?"horizontal"===s?[r.top+r.height-(O.bottom||0),r.top+(O.top||0)]:[r.top+(O.top||0)+(u||0),r.top+r.height-(O.bottom||0)-(u||0)]:m.range,S&&(f=[f[1],f[0]]);var I=(0,c.W7)(m,i,d),D=I.scale,C=I.realScaleType;D.domain(g).range(f),(0,c.YB)(D);var N=(0,c.w7)(D,$($({},m),{},{realScaleType:C}));"xAxis"===n?(v="top"===b&&!w||"bottom"===b&&w,p=r.left,y=h[j]-v*m.height):"yAxis"===n&&(v="left"===b&&!w||"right"===b&&w,p=h[j]-v*m.width,y=r.top);var R=$($($({},m),N),{},{realScaleType:C,x:p,y:y,scale:D,width:"xAxis"===n?r.width:m.width,height:"yAxis"===n?r.height:m.height});return R.bandSize=(0,c.Hj)(R,N),m.hide||"xAxis"!==n?m.hide||(h[j]+=(v?-1:1)*R.width):h[j]+=(v?-1:1)*R.height,$($({},o),{},Y({},a,R))},{})},V=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},Z=function(t){return V({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},tt=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&Q(r.prototype,t),e&&Q(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();Y(tt,"EPS",1e-4);var te=function(t){var e=Object.keys(t).reduce(function(e,r){return $($({},e),{},Y({},r,tt.create(t[r])))},{});return $($({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return i()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},tr=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))}},92331:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},93125:(t,e,r)=>{var n=r(53175);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},99281:(t,e,r)=>{"use strict";r.d(e,{I:()=>H});var n=r(34545);function i(){}function o(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function c(t){this._context=t}function u(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:o(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function h(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function d(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function O(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=h(this,t,e)),r);break;default:y(this,this._t0,r=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),i=x(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var w=r(80926),S=r(82253),j=r(9974);function A(t){return t[0]}function P(t){return t[1]}function E(t,e){var r=(0,S.A)(!0),n=null,i=p,o=null,a=(0,j.i)(c);function c(c){var u,l,s,f=(c=(0,w.A)(c)).length,p=!1;for(null==n&&(o=i(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,u,c),+e(l,u,c));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?A:(0,S.A)(t),e="function"==typeof e?e:void 0===e?P:(0,S.A)(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,S.A)(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function k(t,e,r){var n=null,i=(0,S.A)(!0),o=null,a=p,c=null,u=(0,j.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.A)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return E().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?A:(0,S.A)(+t),e="function"==typeof e?e:void 0===e?(0,S.A)(0):(0,S.A)(+e),r="function"==typeof r?r:void 0===r?P:(0,S.A)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,S.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,S.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,S.A)(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),l):o},l}var M=r(59496),T=r.n(M),_=r(68194),I=r.n(_),D=r(67111),C=r(29925),N=r(4828),R=r(11809);function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var F={curveBasisClosed:function(t){return new c(t)},curveBasisOpen:function(t){return new u(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},W=function(t){return t.x===+t.x&&t.y===+t.y},q=function(t){return t.x},X=function(t){return t.y},K=function(t,e){if(I()(t))return t;var r="curve".concat(T()(t));return("curveMonotone"===r||"curveBump"===r)&&e?F["".concat(r).concat("vertical"===e?"Y":"X")]:F[r]||p},Q=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=K(void 0===r?"linear":r,a),s=u?i.filter(function(t){return W(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return W(t)}):o,p=s.map(function(t,e){return z(z({},t),{},{base:f[e]})});return(e="vertical"===a?k().y(X).x1(q).x0(function(t){return t.base.x}):k().x(q).y1(X).y0(function(t){return t.base.y})).defined(W).curve(l),e(p)}return(e="vertical"===a&&(0,R.Et)(o)?k().y(X).x1(q).x0(o):(0,R.Et)(o)?k().x(q).y1(X).y0(o):E().x(q).y(X)).defined(W).curve(l),e(s)},H=function(t){var e=t.className,r=t.points,i=t.path,o=t.pathRef;if((!r||!r.length)&&!i)return null;var a=r&&r.length?Q(t):i;return n.createElement("path",L({},(0,N.J9)(t,!1),(0,C._U)(t),{className:(0,D.A)("recharts-curve",e),d:a,ref:o}))}}}]);