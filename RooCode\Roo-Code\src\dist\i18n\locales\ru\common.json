{"extension": {"name": "Roo Code", "description": "Целая команда ИИ-разработчиков в вашем редакторе."}, "number_format": {"thousand_suffix": "тыс", "million_suffix": "млн", "billion_suffix": "млрд"}, "welcome": "Добро пожаловать, {{name}}! У вас {{count}} уведомлений.", "items": {"zero": "Нет элементов", "one": "Один элемент", "other": "{{count}} элементов"}, "confirmation": {"reset_state": "Вы уверены, что хотите сбросить все состояние и секретное хранилище в расширении? Это действие нельзя отменить.", "delete_config_profile": "Вы уверены, что хотите удалить этот профиль конфигурации?", "delete_custom_mode_with_rules": "Вы уверены, что хотите удалить этот режим {scope}?\n\nЭто также приведет к удалению соответствующей папки правил по адресу:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Неверный формат URI данных", "error_copying_image": "Ошибка копирования изображения: {{errorMessage}}", "error_saving_image": "Ошибка сохранения изображения: {{errorMessage}}", "error_opening_image": "Ошибка открытия изображения: {{error}}", "could_not_open_file": "Не удалось открыть файл: {{errorMessage}}", "could_not_open_file_generic": "Не удалось открыть файл!", "checkpoint_timeout": "Превышено время ожидания при попытке восстановления контрольной точки.", "checkpoint_failed": "Не удалось восстановить контрольную точку.", "git_not_installed": "Для функции контрольных точек требуется Git. Пожалуйста, установите Git, чтобы включить контрольные точки.", "no_workspace": "Пожалуйста, сначала откройте папку проекта", "update_support_prompt": "Не удалось обновить промпт поддержки", "reset_support_prompt": "Не удалось сбросить промпт поддержки", "enhance_prompt": "Не удалось улучшить промпт", "get_system_prompt": "Не удалось получить системный промпт", "search_commits": "Не удалось выполнить поиск коммитов", "save_api_config": "Не удалось сохранить конфигурацию API", "create_api_config": "Не удалось создать конфигурацию API", "rename_api_config": "Не удалось переименовать конфигурацию API", "load_api_config": "Не удалось загрузить конфигурацию API", "delete_api_config": "Не удалось удалить конфигурацию API", "list_api_config": "Не удалось получить список конфигураций API", "update_server_timeout": "Не удалось обновить таймаут сервера", "hmr_not_running": "Локальный сервер разработки не запущен, HMR не будет работать. Пожалуйста, запустите 'npm run dev' перед запуском расширения для включения HMR.", "retrieve_current_mode": "Ошибка: не удалось получить текущий режим из состояния.", "failed_delete_repo": "Не удалось удалить связанный теневой репозиторий или ветку: {{error}}", "failed_remove_directory": "Не удалось удалить директорию задачи: {{error}}", "custom_storage_path_unusable": "Пользовательский путь хранения \"{{path}}\" непригоден, будет использован путь по умолчанию", "cannot_access_path": "Невозможно получить доступ к пути {{path}}: {{error}}", "settings_import_failed": "Не удалось импортировать настройки: {{error}}.", "mistake_limit_guidance": "Это может указывать на сбой в процессе мышления модели или неспособность правильно использовать инструмент, что можно смягчить с помощью руководства пользователя (например, \"Попробуйте разбить задачу на более мелкие шаги\").", "violated_organization_allowlist": "Не удалось выполнить задачу: текущий профиль несовместим с настройками вашей организации", "condense_failed": "Не удалось сжать контекст", "condense_not_enough_messages": "Недостаточно сообщений для сжатия контекста", "condensed_recently": "Контекст был недавно сжат; пропускаем эту попытку", "condense_handler_invalid": "Обработчик API для сжатия контекста недействителен", "condense_context_grew": "Размер контекста увеличился во время сжатия; пропускаем эту попытку", "url_timeout": "Веб-сайт слишком долго загружался (таймаут). Это может быть из-за медленного соединения, тяжелого веб-сайта или временной недоступности. Ты можешь попробовать позже или проверить правильность URL.", "url_not_found": "Адрес веб-сайта не найден. Проверь правильность URL и попробуй снова.", "no_internet": "Нет подключения к интернету. Проверь сетевое подключение и попробуй снова.", "url_forbidden": "Доступ к этому веб-сайту запрещен. Сайт может блокировать автоматический доступ или требовать аутентификацию.", "url_page_not_found": "Страница не найдена. Проверь правильность URL.", "url_request_aborted": "Запрос на получение URL был прерван. Это может произойти, если сайт блокирует автоматический доступ, требует аутентификацию или есть проблемы с сетью. Попробуй снова или проверь, доступен ли URL в обычном браузере.", "url_fetch_failed": "Ошибка получения содержимого URL: {{error}}", "url_fetch_error_with_url": "Ошибка получения содержимого для {{url}}: {{error}}", "command_timeout": "Время выполнения команды истекло через {{seconds}} секунд", "share_task_failed": "Не удалось поделиться задачей", "share_no_active_task": "Нет активной задачи для совместного использования", "share_auth_required": "Требуется аутентификация. Войдите в систему для совместного доступа к задачам.", "share_not_enabled": "Совместный доступ к задачам не включен для этой организации.", "share_task_not_found": "Задача не найдена или доступ запрещен.", "mode_import_failed": "Не удалось импортировать режим: {{error}}", "delete_rules_folder_failed": "Не удалось удалить папку правил: {{rulesFolderPath}}. Ошибка: {{error}}", "command_not_found": "Команда '{{name}}' не найдена", "open_command_file": "Не удалось открыть файл команды", "delete_command": "Не удалось удалить команду", "no_workspace_for_project_command": "Не найдена папка рабочего пространства для команды проекта", "command_already_exists": "Команда \"{{commandName}}\" уже существует", "create_command_failed": "Не удалось создать команду", "command_template_content": "---\ndescription: \"Краткое описание того, что делает эта команда\"\n---\n\nЭто новая slash-команда. Отредактируйте этот файл, чтобы настроить поведение команды.", "claudeCode": {"processExited": "Процесс Claude Code завершился с кодом {{exitCode}}.", "errorOutput": "Вывод ошибки: {{output}}", "processExitedWithError": "Процесс Claude Code завершился с кодом {{exitCode}}. Вывод ошибки: {{output}}", "stoppedWithReason": "<PERSON> Code остановился по причине: {{reason}}", "apiKeyModelPlanMismatch": "API keys and subscription plans allow different models. Make sure the selected model is included in your plan.", "notFound": "Claude Code executable '{{claudePath}}' not found.\n\nPlease install Claude Code CLI:\n1. Visit {{installationUrl}} to download Claude Code\n2. Follow the installation instructions for your operating system\n3. Ensure the 'claude' command is available in your PATH\n4. Alternatively, configure a custom path in Roo settings under 'Claude Code Path'\n\nOriginal error: {{originalError}}"}, "gemini": {"generate_stream": "Ошибка потока контекста генерации Gemini: {{error}}", "generate_complete_prompt": "Ошибка завершения Gemini: {{error}}", "sources": "Источники:"}, "cerebras": {"authenticationFailed": "Ошибка аутентификации Cerebras API. Убедитесь, что ваш API-ключ действителен и не истек.", "accessForbidden": "Доступ к Cerebras API запрещен. Ваш API-ключ может не иметь доступа к запрашиваемой модели или функции.", "rateLimitExceeded": "Превышен лимит скорости Cerebras API. Подождите перед отправкой следующего запроса.", "serverError": "Ошибка сервера Cerebras API ({{status}}). Попробуйте позже.", "genericError": "Ошибка Cerebras API ({{status}}): {{message}}", "noResponseBody": "Ошибка Cerebras API: Нет тела ответа", "completionError": "Ошибка завершения Cerebras: {{error}}"}, "roo": {"authenticationRequired": "Провайдер Roo требует облачной аутентификации. Войдите в Roo Code Cloud."}, "api": {"invalidKeyInvalidChars": "API-ключ содержит недопустимые символы."}}, "warnings": {"no_terminal_content": "Не выбрано содержимое терминала", "missing_task_files": "Файлы этой задачи отсутствуют. Хотите удалить её из списка задач?", "auto_import_failed": "Не удалось автоматически импортировать настройки RooCode: {{error}}"}, "info": {"no_changes": "Изменения не найдены.", "clipboard_copy": "Системный промпт успешно скопирован в буфер обмена", "history_cleanup": "Очищено {{count}} зада<PERSON>(и) с отсутствующими файлами из истории.", "custom_storage_path_set": "Установлен пользовательский путь хранения: {{path}}", "default_storage_path": "Возвращено использование пути хранения по умолчанию", "settings_imported": "Настройки успешно импортированы.", "auto_import_success": "Настройки RooCode автоматически импортированы из {{filename}}", "share_link_copied": "Ссылка для совместного использования скопирована в буфер обмена", "image_copied_to_clipboard": "URI данных изображения скопирован в буфер обмена", "image_saved": "Изображение сохранено в {{path}}", "organization_share_link_copied": "Ссылка для совместного доступа организации скопирована в буфер обмена!", "public_share_link_copied": "Публичная ссылка для совместного доступа скопирована в буфер обмена!", "mode_exported": "Режим '{{mode}}' успешно экспортирован", "mode_imported": "Режим успешно импортирован"}, "answers": {"yes": "Да", "no": "Нет", "remove": "Удалить", "keep": "Оставить"}, "buttons": {"save": "Сохранить", "edit": "Редактировать", "learn_more": "Узнать больше"}, "tasks": {"canceled": "Ошибка задачи: Она была остановлена и отменена пользователем.", "deleted": "Сбой задачи: Она была остановлена и удалена пользователем.", "incomplete": "Задача #{{taskNumber}} (Незавершенная)", "no_messages": "Задача #{{taskNumber}} (Нет сообщений)"}, "storage": {"prompt_custom_path": "Введите пользовательский путь хранения истории разговоров, оставьте пустым для использования расположения по умолчанию", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Пожалуйста, введите абсолютный путь (например, D:\\RooCodeStorage или /home/<USER>/storage)", "enter_valid_path": "Пожалуйста, введите корректный путь"}, "input": {"task_prompt": "Что должен сделать Roo?", "task_placeholder": "Введите вашу задачу здесь"}, "settings": {"providers": {"groqApiKey": "Ключ API Groq", "getGroqApiKey": "Получить ключ API Groq", "claudeCode": {"pathLabel": "Путь к Claude Code", "description": "Необязательный путь к вашему CLI Claude Code. По умолчанию 'claude', если не установлено.", "placeholder": "По умолчанию: claude"}}}, "customModes": {"errors": {"yamlParseError": "Недопустимый YAML в файле .roomodes на строке {{line}}. Проверь:\n• Правильные отступы (используй пробелы, не табы)\n• Соответствующие кавычки и скобки\n• Допустимый синтаксис YAML", "schemaValidationError": "Недопустимый формат пользовательских режимов в .roomodes:\n{{issues}}", "invalidFormat": "Недопустимый формат пользовательских режимов. Убедись, что твои настройки соответствуют правильному формату YAML.", "updateFailed": "Не удалось обновить пользовательский режим: {{error}}", "deleteFailed": "Не удалось удалить пользовательский режим: {{error}}", "resetFailed": "Не удалось сбросить пользовательские режимы: {{error}}", "modeNotFound": "Ошибка записи: Режим не найден", "noWorkspaceForProject": "Не найдена папка рабочего пространства для режима, специфичного для проекта", "rulesCleanupFailed": "Режим успешно удален, но не удалось удалить папку правил в {{rulesFolderPath}}. Возможно, вам придется удалить ее вручную."}, "scope": {"project": "проект", "global": "глобальный"}}, "marketplace": {"mode": {"rulesCleanupFailed": "Режим успешно удален, но не удалось удалить папку правил в {{rulesFolderPath}}. Возможно, вам придется удалить ее вручную."}}, "mdm": {"errors": {"cloud_auth_required": "Ваша организация требует аутентификации Roo Code Cloud. Войдите в систему, чтобы продолжить.", "organization_mismatch": "Вы должны быть аутентифицированы с учетной записью Roo Code Cloud вашей организации.", "verification_failed": "Не удается проверить аутентификацию организации."}, "info": {"organization_requires_auth": "Ваша организация требует аутентификации."}}, "prompts": {"deleteMode": {"title": "Удалить пользовательский режим", "description": "Вы уверены, что хотите удалить этот режим {{scope}}? Это также удалит связанную папку правил по адресу: {{rulesFolderPath}}", "descriptionNoRules": "Вы уверены, что хотите удалить этот пользовательский режим?", "confirm": "Удалить"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Предотвратить завершение задач при наличии незавершенных дел в списке дел"}}}