<workflow>
  <initialization>
    <step number="1">
      <name>Initialize Issue Creation Process</name>
      <instructions>
        IMPORTANT: This mode assumes the first user message is already a request to create an issue.
        The user doesn't need to say "create an issue" or "make me an issue" - their first message
        is treated as the issue description itself.
        
        When the session starts, immediately:
        1. Treat the user's first message as the issue description
        2. Initialize the workflow by using the update_todo_list tool
        3. Begin the issue creation process without asking what they want to do
        
        <update_todo_list>
        <todos>
        [ ] Detect current repository information
        [ ] Determine repository structure (monorepo/standard)
        [ ] Perform initial codebase discovery
        [ ] Analyze user request to determine issue type
        [ ] Gather and verify additional information
        [ ] Determine if user wants to contribute
        [ ] Perform issue scoping (if contributing)
        [ ] Draft issue content
        [ ] Review and confirm with user
        [ ] Create GitHub issue
        </todos>
        </update_todo_list>
      </instructions>
    </step>
  </initialization>

  <step number="1">
    <name>Detect current repository information</name>
    <instructions>
      CRITICAL FIRST STEP: Verify we're in a git repository and get repository information.
      
      1. Check if we're in a git repository:
      <execute_command>
      <command>git rev-parse --is-inside-work-tree 2>/dev/null || echo "not-git-repo"</command>
      </execute_command>
      
      If the output is "not-git-repo", immediately stop and inform the user:
      
      <attempt_completion>
      <result>
      This mode must be run from within a GitHub repository. Please navigate to a git repository and try again.
      </result>
      </attempt_completion>
      
      2. If in a git repository, get the repository information:
      <execute_command>
      <command>git remote get-url origin 2>/dev/null | sed -E 's/.*[:/]([^/]+)\/([^/]+)(\.git)?$/\1\/\2/' | sed 's/\.git$//'</command>
      </execute_command>
      
      Store this as REPO_FULL_NAME for use throughout the workflow.
      
      If no origin remote exists, stop with:
      <attempt_completion>
      <result>
      No GitHub remote found. This mode requires a GitHub repository with an 'origin' remote configured.
      </result>
      </attempt_completion>
      
      Update todo after detecting repository:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [-] Determine repository structure (monorepo/standard)
      [ ] Perform initial codebase discovery
      [ ] Analyze user request to determine issue type
      [ ] Gather and verify additional information
      [ ] Determine if user wants to contribute
      [ ] Perform issue scoping (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="2">
    <name>Determine Repository Structure</name>
    <instructions>
      Check if this is a monorepo or standard repository by looking for common patterns.
      
      First, check for monorepo indicators:
      1. Look for workspace configuration:
         - package.json with "workspaces" field
         - lerna.json
         - pnpm-workspace.yaml
         - rush.json
      
      2. Check for common monorepo directory patterns:
      <list_files>
      <path>.</path>
      <recursive>false</recursive>
      </list_files>
      
      Look for directories like:
      - apps/ (application packages)
      - packages/ (shared packages)
      - services/ (service packages)
      - libs/ (library packages)
      - modules/ (module packages)
      - src/ (main source if not using workspaces)
      
      If monorepo detected:
        - Dynamically discover packages by looking for package.json files in detected directories
        - Build a list of available packages with their paths
      
      Based on the user's description, try to identify which package they're referring to.
      If unclear, ask for clarification:
      
      <ask_followup_question>
      <question>I see this is a monorepo with multiple packages. Which specific package or application is your issue related to?</question>
      <follow_up>
      [Dynamically generated list of discovered packages]
      <suggest>Let me describe which package: [specify]</suggest>
      </follow_up>
      </ask_followup_question>
      
      If standard repository:
        - Skip package selection
        - Use repository root for all searches
      
      Store the repository context for all future codebase searches and explorations.
      
      Update todo after determining context:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [-] Perform initial codebase discovery
      [ ] Analyze user request to determine issue type
      [ ] Gather and verify additional information
      [ ] Determine if user wants to contribute
      [ ] Perform issue scoping (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="3">
    <name>Perform Initial Codebase Discovery</name>
    <instructions>
      Now that we know the repository structure, immediately search the codebase to understand
      what the user is talking about before determining the issue type.
      
      DISCOVERY ACTIVITIES:
      
      1. Extract keywords and concepts from the user's INITIAL MESSAGE (their issue description)
      2. Search the codebase to verify these concepts exist
      3. Build understanding of the actual implementation
      4. Identify relevant files, components, and code patterns
      
      <codebase_search>
      <query>[Keywords from user's initial message/description]</query>
      <path>[Repository or package path from step 2]</path>
      </codebase_search>
      
      Additional searches based on initial findings:
      - If error mentioned: search for exact error strings
      - If feature mentioned: search for related functionality
      - If component mentioned: search for implementation details
      
      <search_files>
      <path>[repository or package path]</path>
      <regex>[specific patterns found in initial search]</regex>
      </search_files>
      
      Document findings:
      - Components/features found that match user's description
      - Actual implementation details discovered
      - Related code sections identified
      - Any discrepancies between user description and code reality
      
      Update todos:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [-] Analyze user request to determine issue type
      [ ] Gather and verify additional information
      [ ] Determine if user wants to contribute
      [ ] Perform issue scoping (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="4">
    <name>Analyze Request to Determine Issue Type</name>
    <instructions>
      Using the codebase discoveries from step 2, analyze the user's request to determine
      the appropriate issue type with informed context.
      
      CRITICAL GUIDANCE FOR ISSUE TYPE SELECTION:
      For issues that affect user workflows or require behavior changes:
      - PREFER the feature proposal template over bug report
      - Focus on explaining WHO is affected and WHEN this happens
      - Describe the user impact before diving into technical details
      
      Based on your findings, classify the issue:
      
      Bug indicators (verified against code):
      - Error messages that match actual error handling in code
      - Broken functionality in existing features found in codebase
      - Regression from previous behavior documented in code/tests
      - Code paths that don't work as documented
      
      Feature indicators (verified against code):
      - New functionality not found in current codebase
      - Enhancement to existing features found in code
      - Missing capabilities compared to similar features
      - Integration points that could be extended
      - WORKFLOW IMPROVEMENTS: When existing behavior works but doesn't meet user needs
      
      IMPORTANT: Use your codebase findings to inform the question:
      
      <ask_followup_question>
      <question>Based on your request about [specific feature/component found in code], what type of issue would you like to create?</question>
      <follow_up>
      [Order based on codebase findings and user description]
      <suggest>Bug Report - [Specific component] is not working as expected</suggest>
      <suggest>Feature Proposal - Add [specific capability] to [existing component]</suggest>
      </follow_up>
      </ask_followup_question>
      
      Update todos:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [-] Gather and verify additional information
      [ ] Determine if user wants to contribute
      [ ] Perform issue scoping (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="5">
    <name>Gather and Verify Additional Information</name>
    <instructions>
      Based on the issue type and initial codebase discovery, gather information while
      continuously verifying against the actual code implementation.
      
      CRITICAL FOR FEATURE REQUESTS: Be fact-driven and challenge assumptions!
      When users describe current behavior as problematic for a feature request, you MUST verify
      their claims against the actual code. If their description doesn't match reality, this
      might actually be a bug report, not a feature request.
      
      For Bug Reports:
      1. When user describes steps to reproduce:
         - Search for the UI components/commands mentioned
         - Verify the code paths that would be executed
         - Check for existing error handling or known issues
         
      2. When user provides error messages:
         - Search for exact error strings in codebase
         - Find where errors are thrown
         - Understand the conditions that trigger them
         
      3. For version information:
         - Check package.json for actual version
         - Look for version-specific code or migrations
      
      Example verification searches:
      <search_files>
      <path>[repository or package path]</path>
      <regex>[exact error message from user]</regex>
      </search_files>
      
      <codebase_search>
      <query>[feature or component name] implementation</query>
      <path>[repository or package path]</path>
      </codebase_search>
      
      For Feature Requests - AGGRESSIVE VERIFICATION WITH CONCRETE EXAMPLES:
      1. When user claims current behavior is X:
         - ALWAYS search for the actual implementation
         - Read the relevant code to verify their claim
         - Check CSS/styling files if UI-related
         - Look at configuration files
         - Examine test files to understand expected behavior
         - TRACE THE DATA FLOW: Follow values from where they're calculated to where they're used
         
      2. CRITICAL: Look for existing variables/code that could be reused:
         - Search for variables that are calculated but not used where expected
         - Identify existing patterns that could be extended
         - Find similar features that work correctly for comparison
         
      3. If discrepancy found between claim and code:
         - Do NOT proceed without clarification
         - Present CONCRETE before/after examples with actual values
         - Show exactly what happens vs what should happen
         - Ask if this might be a bug instead
         
      Example verification approach:
      User says: "Feature X doesn't work properly"
      
      Your investigation should follow this pattern:
      a) What is calculated: Search for where X is computed/defined
      b) Where it's stored: Find variables/state holding the value
      c) Where it's used: Trace all usages of that value
      d) What's missing: Identify gaps in the flow
      
      Present findings with concrete examples:
      
      <ask_followup_question>
      <question>I investigated the implementation and found something interesting:
      
      Current behavior:
      - The value is calculated at [file:line]: `value = computeX()`
      - It's stored in variable `calculatedValue` at [file:line]
      - BUT it's only used for [purpose A] at [file:line]
      - It's NOT used for [purpose B] where you expected it
      
      Concrete example:
      - When you do [action], the system calculates [value]
      - This value goes to [location A]
      - But [location B] still uses [old/different value]
      
      Is this the issue you're experiencing? This seems like the calculated value isn't being used where it should be.</question>
      <follow_up>
      <suggest>Yes, exactly! The value is calculated but not used in the right place</suggest>
      <suggest>No, the issue is that the calculation itself is wrong</suggest>
      <suggest>Actually, I see now that [location B] should use a different value</suggest>
      </follow_up>
      </ask_followup_question>
      
      4. Continue verification until facts are established:
         - If user confirms it's a bug, switch to bug report workflow
         - If user provides more specific context, search again
         - Do not accept vague claims without code verification
         
      5. For genuine feature requests after verification:
         - Document what the code currently does (with evidence and line numbers)
         - Show the exact data flow: input → processing → output
         - Confirm what the user wants changed with concrete examples
         - Ensure the request is based on accurate understanding
      
      CRITICAL: For feature requests, if user's description doesn't match codebase reality:
      - Challenge the assumption with code evidence AND concrete examples
      - Show actual vs expected behavior with specific values
      - Suggest it might be a bug if code shows different intent
      - Ask for clarification repeatedly if needed
      - Do NOT proceed until facts are established
      
      Only proceed when you have:
      - Verified current behavior in code with line-by-line analysis
      - Confirmed user's understanding matches reality
      - Determined if it's truly a feature request or actually a bug
      - Identified any existing code that could be reused for the fix
      
      Update todos after verification:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [-] Determine if user wants to contribute
      [ ] Perform issue scoping (if contributing)
      [ ] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="6">
    <name>Determine Contribution Intent with Context</name>
    <instructions>
      Before asking about contribution, perform a quick technical assessment to provide context:
      
      1. Search for complexity indicators:
         - Number of files that would need changes
         - Existing tests that would need updates
         - Dependencies and integration points
         
      2. Look for contribution helpers:
         - CONTRIBUTING.md guidelines
         - Existing similar implementations
         - Test patterns to follow
      
      <codebase_search>
      <query>CONTRIBUTING guide setup development</query>
      </codebase_search>
      
      Based on findings, provide informed context in the question:
      
      <ask_followup_question>
      <question>Based on my analysis, this [issue type] involves [brief complexity assessment from code exploration]. Are you interested in implementing this yourself, or are you reporting it for the project team to handle?</question>
      <follow_up>
      <suggest>Just reporting the problem - the project team can design the solution</suggest>
      <suggest>I want to contribute and implement this myself</suggest>
      <suggest>I'd like to provide issue scoping to help whoever implements it</suggest>
      </follow_up>
      </ask_followup_question>
      
      Update todos based on response:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [If contributing: [-] Perform issue scoping (if contributing)]
      [If not contributing: [-] Perform issue scoping (skipped - not contributing)]
      [-] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="7">
    <name>Issue Scoping for Contributors</name>
    <instructions>
      ONLY perform this step if the user wants to contribute or provide issue scoping.
      
      This step performs a comprehensive, aggressive investigation to create detailed technical
      scoping that can guide implementation. The process involves multiple sub-phases:
      
      <sub_workflow name="comprehensive_issue_scoping">
        <overview>
          Perform an exhaustive investigation to produce a comprehensive technical solution
          with extreme detail, suitable for automated fix workflows.
        </overview>
        
        <phase number="1" name="Initialize Investigation">
          <description>Expand the todo list to include detailed investigation steps</description>
          <actions>
            When starting the issue scoping phase, update the main todo list to include
            the detailed investigation steps:
            
            <update_todo_list>
            <todos>
            [x] Detect current repository information
            [x] Determine repository structure (monorepo/standard)
            [x] Perform initial codebase discovery
            [x] Analyze user request to determine issue type
            [x] Gather and verify additional information
            [x] Determine if user wants to contribute
            [-] Perform issue scoping (if contributing)
                [ ] Extract keywords from the issue description
                [ ] Perform initial broad codebase search
                [ ] Analyze search results and identify key components
                [ ] Deep dive into relevant files and implementations
                [ ] Form initial hypothesis about the issue/feature
                [ ] Attempt to disprove hypothesis through further investigation
                [ ] Identify all affected files and dependencies
                [ ] Map out the complete implementation approach
                [ ] Document technical risks and edge cases
                [ ] Formulate comprehensive technical solution
                [ ] Create detailed acceptance criteria
                [ ] Prepare issue scoping summary
            [ ] Draft issue content
            [ ] Review and confirm with user
            [ ] Create GitHub issue
            </todos>
            </update_todo_list>
          </actions>
        </phase>
        
        <phase number="2" name="Keyword Extraction">
          <description>Extract all relevant keywords, concepts, and technical terms</description>
          <actions>
            - Identify primary technical concepts from user's description
            - Extract error messages or specific symptoms
            - Note any mentioned file paths or components
            - List related features or functionality
            - Include synonyms and related terms
          </actions>
          <update_progress>
            Update the main todo list to mark "Extract keywords" as complete and move to next phase
          </update_progress>
        </phase>
        
        <phase number="3" name="Iterative Search">
          <description>Perform multiple rounds of increasingly focused searches</description>
          
          <iteration name="Initial Broad Search">
            Use codebase_search with all extracted keywords to get an overview of relevant code.
            <codebase_search>
            <query>[Combined keywords from extraction phase]</query>
            <path>[Repository or package path]</path>
            </codebase_search>
          </iteration>
          
          <iteration name="Component Discovery">
            Based on initial results, identify key components and search for:
            - Related class/function definitions
            - Import statements and dependencies
            - Configuration files
            - Test files that might reveal expected behavior
          </iteration>
          
          <iteration name="Deep Implementation Search">
            Search for specific implementation details:
            - Error handling patterns
            - State management
            - API endpoints or routes
            - Database queries or models
            - UI components and their interactions
          </iteration>
          
          <iteration name="Edge Case and Integration Search">
            Look for:
            - Edge cases in the code
            - Integration points with other systems
            - Configuration options that affect behavior
            - Feature flags or conditional logic
          </iteration>
          
          <update_progress>
            After completing all search iterations, update the todo list to show progress
          </update_progress>
        </phase>
        
        <phase number="4" name="File Analysis">
          <description>Thoroughly analyze all relevant files discovered</description>
          <actions>
            - Use list_code_definition_names to understand file structure
            - Read complete files to understand full context
            - Trace execution paths through the code
            - Identify all dependencies and imports
            - Map relationships between components
          </actions>
          <documentation>
            Document findings including:
            - File paths and their purposes
            - Key functions and their responsibilities
            - Data flow through the system
            - External dependencies
            - Potential impact areas
          </documentation>
        </phase>
        
        <phase number="5" name="Hypothesis Formation">
          <description>Form a comprehensive hypothesis about the issue or feature</description>
          <for_bugs>
            - Identify the most likely root cause
            - Trace the bug through the execution path
            - Determine why the current implementation fails
            - Consider environmental factors
          </for_bugs>
          <for_features>
            - Identify the optimal integration points
            - Determine required architectural changes
            - Plan the implementation approach
            - Consider scalability and maintainability
          </for_features>
        </phase>
        
        <phase number="6" name="Hypothesis Validation">
          <description>Aggressively attempt to disprove the hypothesis</description>
          <validation_steps>
            <step name="Search for Alternative Implementations">
              - Look for similar features implemented differently
              - Check for deprecated code that might interfere
            </step>
            <step name="Configuration and Environment Check">
              - Search for configuration that could change behavior
              - Look for environment-specific code paths
            </step>
            <step name="Test Case Analysis">
              - Find existing tests that might contradict hypothesis
              - Look for test cases that reveal edge cases
            </step>
            <step name="Historical Context">
              - Search for comments explaining design decisions
              - Look for TODO or FIXME comments related to the area
            </step>
          </validation_steps>
          <outcome>
            If hypothesis is disproven, return to search phase with new insights.
            If hypothesis stands, proceed to solution formulation.
          </outcome>
        </phase>
        
        <phase number="7" name="Solution Formulation">
          <description>Create a comprehensive technical solution - PRIORITIZE SIMPLICITY</description>
          <simplicity_principle>
            CRITICAL: Before proposing any solution, ask yourself:
            1. What existing variables/functions can I reuse?
            2. What's the minimal change that fixes the issue?
            3. Can I leverage existing patterns in the codebase?
            4. Is there a simpler approach I'm overlooking?
            
            The best solution often reuses existing code rather than creating new complexity.
          </simplicity_principle>
          
          <backwards_compatibility_principle>
            ALWAYS consider backwards compatibility:
            1. Will existing data/configurations still work with the new code?
            2. Can we detect and handle legacy formats automatically?
            3. What migration paths are needed for existing users?
            4. Are there ways to make changes additive rather than breaking?
            5. Document any compatibility considerations clearly
          </backwards_compatibility_principle>
          <components>
            <component name="existing_code_analysis">
              FIRST, identify what can be reused:
              - Variables that are already calculated but not used where needed
              - Functions that already do what we need
              - Patterns in similar features we can follow
              - Configuration that already exists but isn't applied
              
              Example finding:
              "The variable `calculatedValue` already contains what we need at line X,
              we just need to use it at line Y instead of recalculating"
            </component>
            
            <component name="implementation_plan">
              - Start with the SIMPLEST possible fix
              - Exact files to modify with line numbers
              - Prefer changing variable usage over creating new logic
              - Specific code changes required (minimal diff)
              - Order of implementation steps
              - Migration strategy if needed
            </component>
            
            <component name="dependency_analysis">
              - All files that import affected code
              - API contracts that must be maintained
              - Existing tests that validate current behavior
              - Configuration changes required (prefer reusing existing)
              - Documentation updates needed
            </component>
            
            <component name="test_strategy">
              - Unit tests to add or modify
              - Integration tests required
              - Edge cases to test
              - Performance testing needs
              - Manual testing scenarios
            </component>
            
            <component name="risk_assessment">
              - Breaking changes identified
              - Performance implications
              - Security considerations
              - Backward compatibility issues
              - Rollback strategy
            </component>
          </components>
        </phase>
        
        <phase number="8" name="Acceptance Criteria">
          <description>Create extremely detailed acceptance criteria</description>
          <format>
            Given [detailed context including system state]
            When [specific user or system action]
            Then [exact expected outcome]
            And [additional verifiable outcomes]
            But [what should NOT happen]
            
            Include:
            - Specific UI changes with exact text/behavior
            - API response formats
            - Database state changes
            - Performance requirements
            - Error handling scenarios
          </format>
          <guidelines>
            - Each criterion must be independently testable
            - Include both positive and negative test cases
            - Specify exact error messages and codes
            - Define performance thresholds where applicable
          </guidelines>
        </phase>
        
        <phase number="9" name="Issue Scoping Output">
          <description>Format the comprehensive issue scoping section</description>
          <output_template><![CDATA[
## 🔍 Comprehensive Issue Scoping

### Root Cause / Implementation Target
[Detailed explanation of the core issue or feature target, focusing on the practical problem first]

### Affected Components
- **Primary Files:**
  - `path/to/file1.ts` (lines X-Y): [Purpose and changes needed]
  - `path/to/file2.ts` (lines A-B): [Purpose and changes needed]

- **Secondary Impact:**
  - Files that import affected components
  - Related test files
  - Documentation files

### Current Implementation Analysis
[Detailed explanation of how the current code works, with specific examples showing the data flow]
Example: "The function at line X calculates [value] by [method], which results in [actual behavior]"

### Proposed Implementation

#### Step 1: [First implementation step]
- File: `path/to/file.ts`
- Changes: [Specific code changes]
- Rationale: [Why this change is needed]

#### Step 2: [Second implementation step]
[Continue for all steps...]

### Code Architecture Considerations
- Design patterns to follow
- Existing patterns in codebase to match
- Architectural constraints

### Testing Requirements
- Unit Tests:
  - [ ] Test case 1: [Description]
  - [ ] Test case 2: [Description]
- Integration Tests:
  - [ ] Test scenario 1: [Description]
- Edge Cases:
  - [ ] Edge case 1: [Description]

### Performance Impact
- Expected performance change: [Increase/Decrease/Neutral]
- Benchmarking needed: [Yes/No, specifics]
- Optimization opportunities: [List any]

### Security Considerations
- Input validation requirements
- Authentication/Authorization changes
- Data exposure risks

### Migration Strategy
[If applicable, how to migrate existing data/functionality]

### Rollback Plan
[How to safely rollback if issues arise]

### Dependencies and Breaking Changes
- External dependencies affected: [List]
- API contract changes: [List]
- Breaking changes for users: [List with mitigation]
          ]]></output_template>
        </phase>
      </sub_workflow>
      
      Additional considerations for monorepo repositories:
      - Scope all searches to the identified package (if monorepo)
      - Check for cross-package dependencies
      - Verify against package-specific conventions
      - Look for package-specific configuration
      - Check if changes affect multiple packages
      - Identify shared dependencies that might be impacted
      - Look for workspace-specific scripts or tooling
      - Consider package versioning implications
      
      After completing the comprehensive issue scoping, update the main todo list to show
      all investigation steps are complete:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
          [x] Extract keywords from the issue description
          [x] Perform initial broad codebase search
          [x] Analyze search results and identify key components
          [x] Deep dive into relevant files and implementations
          [x] Form initial hypothesis about the issue/feature
          [x] Attempt to disprove hypothesis through further investigation
          [x] Identify all affected files and dependencies
          [x] Map out the complete implementation approach
          [x] Document technical risks and edge cases
          [x] Formulate comprehensive technical solution
          [x] Create detailed acceptance criteria
          [x] Prepare issue scoping summary
      [-] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="8">
    <name>Check for Repository Issue Templates</name>
    <instructions>
      Check if the repository has custom issue templates and use them. If not, create a simple generic template.
      
      1. Check for issue templates in standard locations:
      <list_files>
      <path>.github/ISSUE_TEMPLATE</path>
      <recursive>true</recursive>
      </list_files>
      
      2. Also check for single template file:
      <list_files>
      <path>.github</path>
      <recursive>false</recursive>
      </list_files>
      
      Look for files like:
      - .github/ISSUE_TEMPLATE/*.md
      - .github/ISSUE_TEMPLATE/*.yml
      - .github/ISSUE_TEMPLATE/*.yaml
      - .github/issue_template.md
      - .github/ISSUE_TEMPLATE.md
      
      3. If templates are found:
         a. Parse the template files to extract:
            - Template name and description
            - Required fields
            - Template body structure
            - Labels to apply
         
         b. For YAML templates, look for:
            - name: Template display name
            - description: Template description
            - labels: Default labels
            - body: Form fields or markdown template
         
         c. For Markdown templates, look for:
            - Front matter with metadata
            - Template structure with placeholders
      
      4. If multiple templates exist, ask user to choose:
      <ask_followup_question>
      <question>I found the following issue templates in this repository. Which one would you like to use?</question>
      <follow_up>
      <suggest>[Template 1 name]: [Template 1 description]</suggest>
      <suggest>[Template 2 name]: [Template 2 description]</suggest>
      </follow_up>
      </ask_followup_question>
      
      5. If no templates are found:
         - Create a simple generic template based on issue type
         - For bugs: Basic structure with description, steps to reproduce, expected vs actual
         - For features: Problem description, proposed solution, impact
      
      6. Store the selected/created template information:
         - Template content/structure
         - Required fields
         - Default labels
         - Any special formatting requirements
      
      Update todos:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
      [x] Check for repository issue templates
      [-] Draft issue content
      [ ] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="9">
    <name>Draft Issue Content</name>
    <instructions>
      Create the issue body using the template from step 8 and all verified information from codebase exploration.
      
      If using a repository template:
      - Fill in the template fields with gathered information
      - Include code references and findings where appropriate
      - Respect the template's structure and formatting
      
      If using a generated template (no repo templates found):
      
      For Bug Reports:
      ```
      ## Description
      [Clear description of the bug with code context]
      
      ## Steps to Reproduce
      1. [Step with relevant code paths]
      2. [Step with component references]
      3. [Continue with specific details]
      
      ## Expected Behavior
      [What should happen based on code logic]
      
      ## Actual Behavior
      [What actually happens]
      
      ## Additional Context
      - Version: [from package.json if found]
      - Environment: [any relevant details]
      - Error logs: [if any]
      
      ## Code Investigation
      [Include findings from codebase exploration]
      - Relevant files: [list with line numbers]
      - Possible cause: [hypothesis from code review]
      
      [If user is contributing, add the comprehensive issue scoping section from step 7]
      ```
      
      For Feature Requests:
      ```
      ## Problem Description
      [What problem does this solve, who is affected, when it happens]
      
      ## Current Behavior
      [How it works now with specific examples]
      
      ## Proposed Solution
      [What should change]
      
      ## Impact
      [Who benefits and how]
      
      ## Technical Context
      [Findings from codebase exploration]
      - Similar features: [code references]
      - Integration points: [from exploration]
      - Architecture considerations: [if any]
      
      [If contributing, add the comprehensive issue scoping section from step 7]
      ```
      
      Update todos:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
      [x] Check for repository issue templates
      [x] Draft issue content
      [-] Review and confirm with user
      [ ] Create GitHub issue
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="10">
    <name>Review and Confirm with User</name>
    <instructions>
      Present the complete drafted issue to the user for review, highlighting the
      code-verified information:
      
      <ask_followup_question>
      <question>I've prepared the following GitHub issue based on my analysis of the codebase and your description. I've verified the technical details against the actual implementation. Please review:

      [Show the complete formatted issue content]

      Key verifications made:
      - ✓ Component locations confirmed in code
      - ✓ Error messages matched to source
      - ✓ Architecture compatibility checked
      [List other relevant verifications]

      Would you like me to create this issue, or would you like to make any changes?</question>
      <follow_up>
      <suggest>Yes, create this issue in the detected repository</suggest>
      <suggest>Modify the problem description</suggest>
      <suggest>Add more technical details</suggest>
      <suggest>Change the title to: [let me specify]</suggest>
      </follow_up>
      </ask_followup_question>
      
      If user requests changes, make them and show the updated version for confirmation.
      
      After confirmation:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
      [x] Check for repository issue templates
      [x] Draft issue content
      [x] Review and confirm with user
      [-] Prepare issue for submission
      [ ] Handle submission choice
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="11">
    <name>Prepare Issue for Submission</name>
    <instructions>
      Once user confirms the issue content, prepare it for submission:
      
      First, perform final duplicate check with refined search based on our findings:
      <execute_command>
      <command>gh issue list --repo $REPO_FULL_NAME --search "[key terms from verified analysis]" --state all --limit 10</command>
      </execute_command>
      
      If no exact duplicates are found, save the issue content to a temporary file within the project:
      
      <write_to_file>
      <path>./github_issue_draft.md</path>
      <content>[The complete formatted issue body from step 8]</content>
      <line_count>[calculated line count]</line_count>
      </write_to_file>
      
      After saving the issue draft, ask the user how they would like to proceed:
      
      <ask_followup_question>
      <question>I've saved the issue draft to ./github_issue_draft.md. The issue is ready for submission with the following details:

      Title: "[Descriptive title with component name]"
      Labels: [appropriate labels based on issue type]
      Repository: $REPO_FULL_NAME

      How would you like to proceed?</question>
      <follow_up>
      <suggest>Submit the issue now to the repository</suggest>
      <suggest>Let me make some edits to the issue first</suggest>
      <suggest>I'll submit it manually later</suggest>
      </follow_up>
      </ask_followup_question>
      
      Based on the user's response:
      
      If "Submit the issue now":
        - Use gh issue create with the saved file
        - Provide the created issue URL and number
        - Clean up the temporary file
        - Complete the workflow
      
      If "Let me make some edits":
        - Ask what changes they'd like to make
        - Update the draft file with their changes
        - Return to the submission question
      
      If "I'll submit it manually":
        - Inform them the draft is saved at the configured location
        - Provide the gh command they can use later
        - Complete the workflow without submission
      
      Update todos based on the outcome:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
      [x] Check for repository issue templates
      [x] Draft issue content
      [x] Review and confirm with user
      [x] Prepare issue for submission
      [-] Handle submission choice
      </todos>
      </update_todo_list>
    </instructions>
  </step>

  <step number="12">
    <name>Handle Submission Choice</name>
    <instructions>
      This step handles the user's choice from step 9.
      
      OPTION 1: Submit the issue now
      If the user chooses to submit immediately:
      
      <execute_command>
      <command>gh issue create --repo $REPO_FULL_NAME --title "[Descriptive title]" --body-file ./github_issue_draft.md --label "[appropriate labels]"</command>
      </execute_command>
      
      Label selection based on findings:
      - Bug: Use "bug" label
      - Feature: Use "enhancement" label
      - If affects multiple packages in monorepo: add "affects-multiple" label
      
      After successful creation:
      - Capture and display the issue URL
      - Clean up the temporary file:
        <execute_command>
        <command>rm ./github_issue_draft.md</command>
        </execute_command>
      - Provide a summary of key findings included
      
      OPTION 2: Make edits
      If the user wants to edit:
      
      <ask_followup_question>
      <question>What changes would you like to make to the issue?</question>
      <follow_up>
      <suggest>Update the title</suggest>
      <suggest>Modify the problem description</suggest>
      <suggest>Add or remove technical details</suggest>
      <suggest>Change the labels or other metadata</suggest>
      </follow_up>
      </ask_followup_question>
      
      - Apply the requested changes to the draft
      - Update the file with write_to_file
      - Return to step 9 to ask about submission again
      
      OPTION 3: Manual submission
      If the user will submit manually:
      
      Provide clear instructions:
      "The issue draft has been saved to ./github_issue_draft.md

      To submit it later, you can use:
      gh issue create --repo $REPO_FULL_NAME --title "[Your title]" --body-file ./github_issue_draft.md --label "[labels]"
      
      Or you can copy the content and create the issue through the GitHub web interface."
      
      Final todo update:
      <update_todo_list>
      <todos>
      [x] Detect current repository information
      [x] Determine repository structure (monorepo/standard)
      [x] Perform initial codebase discovery
      [x] Analyze user request to determine issue type
      [x] Gather and verify additional information
      [x] Determine if user wants to contribute
      [x] Perform issue scoping (if contributing)
      [x] Check for repository issue templates
      [x] Draft issue content
      [x] Review and confirm with user
      [x] Prepare issue for submission
      [x] Handle submission choice
      </todos>
      </update_todo_list>
    </instructions>
  </step>
</workflow>