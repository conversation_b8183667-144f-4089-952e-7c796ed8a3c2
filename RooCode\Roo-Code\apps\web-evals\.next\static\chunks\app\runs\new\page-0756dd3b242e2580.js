(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[128],{3426:(e,r,s)=>{Promise.resolve().then(s.bind(s,7421))},7421:(e,r,s)=>{"use strict";s.d(r,{NewRun:()=>O});var n=s(7093),t=s(4545),l=s(8923),a=s(9860),i=s(8590),o=s(4060),c=s(7296),d=s(5049),u=s.n(d),x=s(2247),m=s(8365),j=s(1040),h=s(1005),p=s(5745),f=s(8936),g=s(7676),v=s(8928),N=s(3401);let y=(0,N.createServerReference)("400a0cfc47b7520423dd766624870d0bc5aad733ca",N.callServer,void 0,N.findSourceMapURL,"createRun"),b=(0,N.createServerReference)("7f3f0c42c2a743b6a21055ec55ecfb62dd99c5c3e2",N.callServer,void 0,N.findSourceMapURL,"getExercises"),C=a.z.object({model:a.z.string().min(1,{message:"Model is required."}),description:a.z.string().optional(),suite:a.z.enum(["full","partial"]),exercises:a.z.array(a.z.string()).optional(),settings:v.us4.optional(),concurrency:a.z.number().int().min(1).max(25),timeout:a.z.number().int().min(5).max(10),systemPrompt:a.z.string().optional()}).refine(e=>"full"===e.suite||(e.exercises||[]).length>0,{message:"Exercises are required when running a partial suite.",path:["exercises"]});var z=s(8322);let S=a.z.object({id:a.z.string(),name:a.z.string()}),w=async()=>{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)return[];let r=a.z.object({data:a.z.array(S)}).safeParse(await e.json());return r.success?r.data.data.sort((e,r)=>e.name.localeCompare(r.name)):(console.error(r.error),[])},R=()=>(0,i.I)({queryKey:["getOpenRouterModels"],queryFn:w});var k=s(4548);let A=[...v.jK9,...v.LT1];function I(e){let{customSettings:{experiments:r,...s},defaultSettings:{experiments:t,...l},className:a,...i}=e,o={...l,...t},c={...s,...r};return(0,n.jsxs)("div",{className:(0,z.cn)("grid grid-cols-3 gap-2 text-sm p-2",a),...i,children:[(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Setting"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Default"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Custom"}),A.map(e=>{let r=o[e],s=c[e];return JSON.stringify(r)===JSON.stringify(s)?null:(0,n.jsx)(M,{name:e,defaultValue:JSON.stringify(r,null,2),customValue:JSON.stringify(s,null,2)},e)})]})}function M(e){let{name:r,defaultValue:s,customValue:l,...a}=e;return(0,n.jsxs)(t.Fragment,{...a,children:[(0,n.jsx)("div",{className:"font-mono",title:r,children:r}),(0,n.jsx)("pre",{className:"inline text-rose-500 line-through",title:s,children:s}),(0,n.jsx)("pre",{className:"inline text-teal-500",title:l,children:l})]})}function O(){let e=(0,l.useRouter)(),[r,s]=(0,t.useState)("openrouter"),[d,N]=(0,t.useState)(""),[S,w]=(0,t.useState)(!1),A=(0,t.useRef)(new Map),M=(0,t.useRef)(""),O=R(),V=(0,i.I)({queryKey:["getExercises"],queryFn:()=>b()}),E=(0,o.mN)({resolver:(0,c.u)(C),defaultValues:{model:"anthropic/claude-sonnet-4",description:"",suite:"full",exercises:[],settings:void 0,concurrency:1,timeout:5}}),{setValue:J,clearErrors:B,watch:F,formState:{isSubmitting:$}}=E,[_,q,L]=F(["model","suite","settings","concurrency"]),K=(0,t.useCallback)(async s=>{try{"openrouter"===r&&(s.settings={...s.settings||{},openRouterModelId:_});let{id:n}=await y(s);e.push("/runs/".concat(n))}catch(e){x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}},[r,_,e]),U=(0,t.useCallback)((e,r)=>{var s;if(M.current!==r)for(let{obj:{id:e},score:s}of(M.current=r,A.current.clear(),u().go(r,O.data||[],{key:"name"})))A.current.set(e,s);return null!==(s=A.current.get(e))&&void 0!==s?s:0},[O.data]),P=(0,t.useCallback)(e=>{J("model",e),w(!1)},[J]),X=(0,t.useCallback)(async e=>{var r,n,t;let l=null===(r=e.target.files)||void 0===r?void 0:r[0];if(l){B("settings");try{let{providerProfiles:r,globalSettings:i}=a.z.object({providerProfiles:a.z.object({currentApiConfigName:a.z.string(),apiConfigs:a.z.record(a.z.string(),v.AQ$)}),globalSettings:v.YZX}).parse(JSON.parse(await l.text())),o=null!==(n=r.apiConfigs[r.currentApiConfigName])&&void 0!==n?n:{};J("model",null!==(t=(0,v.XxZ)(o))&&void 0!==t?t:""),J("settings",{...v.Ur7,...o,...i}),s("settings"),e.target.value=""}catch(e){console.error(e),x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}}},[B,J]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.Op,{...E,children:(0,n.jsxs)("form",{onSubmit:E.handleSubmit(K),className:"flex flex-col justify-center divide-y divide-primary *:py-5",children:[(0,n.jsxs)("div",{className:"flex flex-row justify-between gap-4",children:["openrouter"===r&&(0,n.jsx)(k.zB,{control:E.control,name:"model",render:()=>{var e,r,s;return(0,n.jsxs)(k.eI,{className:"flex-1",children:[(0,n.jsxs)(k.AM,{open:S,onOpenChange:w,children:[(0,n.jsx)(k.Wv,{asChild:!0,children:(0,n.jsxs)(k.$n,{variant:"input",role:"combobox","aria-expanded":S,className:"flex items-center justify-between",children:[(0,n.jsx)("div",{children:(null===(r=O.data)||void 0===r?void 0:null===(e=r.find(e=>{let{id:r}=e;return r===_}))||void 0===e?void 0:e.name)||_||"Select OpenRouter Model"}),(0,n.jsx)(m.A,{className:"opacity-50"})]})}),(0,n.jsx)(k.hl,{className:"p-0 w-[var(--radix-popover-trigger-width)]",children:(0,n.jsxs)(k.uB,{filter:U,children:[(0,n.jsx)(k.G7,{placeholder:"Search",value:d,onValueChange:N,className:"h-9"}),(0,n.jsxs)(k.oI,{children:[(0,n.jsx)(k.xL,{children:"No model found."}),(0,n.jsx)(k.L$,{children:null===(s=O.data)||void 0===s?void 0:s.map(e=>{let{id:r,name:s}=e;return(0,n.jsxs)(k.h_,{value:r,onSelect:P,children:[s,(0,n.jsx)(j.A,{className:(0,z.cn)("ml-auto text-accent group-data-[selected=true]:text-accent-foreground size-4",r===_?"opacity-100":"opacity-0")})]},r)})})]})]})})]}),(0,n.jsx)(k.C5,{})]})}}),(0,n.jsxs)(k.eI,{className:"flex-1",children:[(0,n.jsxs)(k.$n,{type:"button",variant:"secondary",onClick:()=>{var e;return null===(e=document.getElementById("json-upload"))||void 0===e?void 0:e.click()},children:[(0,n.jsx)(h.A,{}),"Import Settings"]}),(0,n.jsx)("input",{id:"json-upload",type:"file",accept:"application/json",className:"hidden",onChange:X}),L&&(0,n.jsxs)(k.FK,{className:"max-h-64 border rounded-sm",children:[(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex items-center gap-1 p-2 border-b",children:[(0,n.jsx)(p.A,{className:"size-4 text-ring"}),(0,n.jsx)("div",{className:"text-sm",children:"Imported valid Roo Code settings. Showing differences from default settings."})]}),(0,n.jsx)(I,{defaultSettings:v.Ur7,customSettings:L})]}),(0,n.jsx)(k.$H,{orientation:"horizontal"})]}),(0,n.jsx)(k.C5,{})]})]}),(0,n.jsx)(k.zB,{control:E.control,name:"suite",render:()=>{var e;return(0,n.jsxs)(k.eI,{children:[(0,n.jsx)(k.lR,{children:"Exercises"}),(0,n.jsx)(k.tU,{defaultValue:"full",onValueChange:e=>J("suite",e),children:(0,n.jsxs)(k.j7,{children:[(0,n.jsx)(k.Xi,{value:"full",children:"All"}),(0,n.jsx)(k.Xi,{value:"partial",children:"Some"})]})}),"partial"===q&&(0,n.jsx)(k.KF,{options:(null===(e=V.data)||void 0===e?void 0:e.map(e=>({value:e,label:e})))||[],onValueChange:e=>J("exercises",e),placeholder:"Select",variant:"inverted",maxCount:4}),(0,n.jsx)(k.C5,{})]})}}),(0,n.jsx)(k.zB,{control:E.control,name:"concurrency",render:e=>{let{field:r}=e;return(0,n.jsxs)(k.eI,{children:[(0,n.jsx)(k.lR,{children:"Concurrency"}),(0,n.jsx)(k.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,n.jsx)(k.Ap,{defaultValue:[r.value],min:1,max:25,step:1,onValueChange:e=>r.onChange(e[0])}),(0,n.jsx)("div",{children:r.value})]})}),(0,n.jsx)(k.C5,{})]})}}),(0,n.jsx)(k.zB,{control:E.control,name:"timeout",render:e=>{let{field:r}=e;return(0,n.jsxs)(k.eI,{children:[(0,n.jsx)(k.lR,{children:"Timeout (Minutes)"}),(0,n.jsx)(k.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,n.jsx)(k.Ap,{defaultValue:[r.value],min:5,max:10,step:1,onValueChange:e=>r.onChange(e[0])}),(0,n.jsx)("div",{children:r.value})]})}),(0,n.jsx)(k.C5,{})]})}}),(0,n.jsx)(k.zB,{control:E.control,name:"description",render:e=>{let{field:r}=e;return(0,n.jsxs)(k.eI,{children:[(0,n.jsx)(k.lR,{children:"Description / Notes"}),(0,n.jsx)(k.MJ,{children:(0,n.jsx)(k.TM,{placeholder:"Optional",...r})}),(0,n.jsx)(k.C5,{})]})}}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsxs)(k.$n,{size:"lg",type:"submit",disabled:$,children:[(0,n.jsx)(f.A,{className:"size-4"}),"Launch"]})})]})}),(0,n.jsx)(k.$n,{variant:"default",className:"absolute top-4 right-12 size-12 rounded-full",onClick:()=>e.push("/"),children:(0,n.jsx)(g.A,{className:"size-6"})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[912,221,581,361,777,409,335,550,358],()=>r(3426)),_N_E=e.O()}]);