<github_issue_templates>
  <overview>
    This mode prioritizes using repository-specific issue templates over hardcoded ones.
    If no templates exist in the repository, simple generic templates are created on the fly.
  </overview>

  <template_detection>
    <locations>
      <location priority="1">.github/ISSUE_TEMPLATE/*.yml</location>
      <location priority="2">.github/ISSUE_TEMPLATE/*.yaml</location>
      <location priority="3">.github/ISSUE_TEMPLATE/*.md</location>
      <location priority="4">.github/issue_template.md</location>
      <location priority="5">.github/ISSUE_TEMPLATE.md</location>
    </locations>
    
    <yaml_template_structure>
      <field name="name">Display name of the template</field>
      <field name="description">Brief description of when to use this template</field>
      <field name="title">Default issue title (optional)</field>
      <field name="labels">Array of labels to apply</field>
      <field name="assignees">Array of default assignees</field>
      <field name="body">Array of form elements or markdown content</field>
    </yaml_template_structure>
    
    <yaml_form_elements>
      <element type="markdown">
        <description>Static markdown content</description>
        <attributes>
          <attr name="value">The markdown content to display</attr>
        </attributes>
      </element>
      
      <element type="input">
        <description>Single-line text input</description>
        <attributes>
          <attr name="id">Unique identifier</attr>
          <attr name="label">Display label</attr>
          <attr name="description">Help text</attr>
          <attr name="placeholder">Placeholder text</attr>
          <attr name="value">Default value</attr>
          <attr name="required">Boolean</attr>
        </attributes>
      </element>
      
      <element type="textarea">
        <description>Multi-line text input</description>
        <attributes>
          <attr name="id">Unique identifier</attr>
          <attr name="label">Display label</attr>
          <attr name="description">Help text</attr>
          <attr name="placeholder">Placeholder text</attr>
          <attr name="value">Default value</attr>
          <attr name="required">Boolean</attr>
          <attr name="render">Language for syntax highlighting</attr>
        </attributes>
      </element>
      
      <element type="dropdown">
        <description>Dropdown selection</description>
        <attributes>
          <attr name="id">Unique identifier</attr>
          <attr name="label">Display label</attr>
          <attr name="description">Help text</attr>
          <attr name="options">Array of options</attr>
          <attr name="required">Boolean</attr>
        </attributes>
      </element>
      
      <element type="checkboxes">
        <description>Multiple checkbox options</description>
        <attributes>
          <attr name="id">Unique identifier</attr>
          <attr name="label">Display label</attr>
          <attr name="description">Help text</attr>
          <attr name="options">Array of checkbox items</attr>
        </attributes>
      </element>
    </yaml_form_elements>
    
    <markdown_template_structure>
      <front_matter>
        Optional YAML front matter with:
        - name: Template name
        - about: Template description
        - title: Default title
        - labels: Comma-separated or array
        - assignees: Comma-separated or array
      </front_matter>
      <body>
        Markdown content with sections and placeholders
        Common patterns:
        - Headers with ##
        - Placeholder text in brackets or as comments
        - Checklists with - [ ]
        - Code blocks with ```
      </body>
    </markdown_template_structure>
  </template_detection>

  <generic_templates>
    <description>
      When no repository templates exist, create simple templates based on issue type.
      These should be minimal and focused on gathering essential information.
    </description>
    
    <bug_template>
      <structure>
        - Description: Clear explanation of the bug
        - Steps to Reproduce: Numbered list
        - Expected Behavior: What should happen
        - Actual Behavior: What actually happens
        - Additional Context: Version, environment, logs
        - Code Investigation: Findings from exploration (if any)
      </structure>
      <labels>["bug"]</labels>
    </bug_template>
    
    <feature_template>
      <structure>
        - Problem Description: What problem this solves
        - Current Behavior: How it works now
        - Proposed Solution: What should change
        - Impact: Who benefits and how
        - Technical Context: Code findings (if any)
      </structure>
      <labels>["enhancement", "proposal"]</labels>
    </feature_template>
  </generic_templates>

  <template_parsing_guidelines>
    <guideline>
      When parsing YAML templates:
      1. Use a YAML parser to extract the structure
      2. Convert form elements to markdown sections
      3. Preserve required field indicators
      4. Include descriptions as help text
      5. Maintain the intended flow of the template
    </guideline>
    
    <guideline>
      When parsing Markdown templates:
      1. Extract front matter if present
      2. Identify section headers
      3. Look for placeholder patterns
      4. Preserve formatting and structure
      5. Replace generic placeholders with user's information
    </guideline>
    
    <guideline>
      For template selection:
      1. If only one template exists, use it automatically
      2. If multiple exist, let user choose based on name/description
      3. Match template to issue type when possible (bug vs feature)
      4. Respect template metadata (labels, assignees, etc.)
    </guideline>
  </template_parsing_guidelines>

  <filling_templates>
    <principle>
      Fill templates intelligently using gathered information:
      - Map user's description to appropriate sections
      - Include code investigation findings where relevant
      - Preserve template structure and formatting
      - Don't leave placeholder text unfilled
      - Add contributor scoping if user is contributing
    </principle>
    
    <mapping_examples>
      <example from="Steps to Reproduce" to="User's reproduction steps + code paths"/>
      <example from="Expected behavior" to="What user expects + code logic verification"/>
      <example from="System information" to="Detected versions + environment"/>
      <example from="Additional context" to="Code findings + architecture insights"/>
    </mapping_examples>
  </filling_templates>

  <no_template_behavior>
    <description>
      When no templates exist, create appropriate generic templates on the fly.
      Keep them simple and focused on essential information.
    </description>
    
    <guidelines>
      - Don't overwhelm with too many fields
      - Focus on problem description first
      - Include technical details only if user is contributing
      - Use clear, simple section headers
      - Adapt based on issue type (bug vs feature)
    </guidelines>
  </no_template_behavior>
</github_issue_templates>